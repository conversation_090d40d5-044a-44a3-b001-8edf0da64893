const { defineConfig } = require('@vue/cli-service');
const webpack = require('webpack');

//此包依赖@paas/stylelint-config-paas 无需重复安装
const styleLintPlugin = require('stylelint-webpack-plugin');
const { PaasCheckPlugin } = require('@paas/paas-webpack-plugin');
const path = require('path');
const fs = require('fs');
const ip = require('ip');
const getAppConfig = require('./app-env/config.js');

const antdStyle = require('@paas/paas-library/src/theme/antd-sty.json');

const FRAMEWORK_CONFIG_URL = 'public/framework.config.json';

const resolve = dir => path.join(__dirname, dir); // 路径

const getConfigJson = function () {
	return require(resolve(FRAMEWORK_CONFIG_URL));
};

const isProd = process.argv.includes('--isProd');
const isDev = process.argv.includes('--isDev');
const isLocal = process.argv.includes('--isLocal');
const isTest = process.argv.includes('--isTest');

const appConfig = getAppConfig(isProd, isDev, isLocal, isTest);

const port = 8090;

const getPulicPath = () => {
	if (isTest) {
		return '/' + appConfig.enName + '-ttt';
	} else if (isProd) {
		return './';
	} else {
		return '/' + appConfig.enName;
	}
};

module.exports = defineConfig({
	lintOnSave: true,
	publicPath: getPulicPath(),
	productionSourceMap: false,
	outputDir: 'dist',
	devServer: {
		port,
		headers: {
			// 'Access-Control-Allow-Origin': '*'
		},
		client: {
			overlay: false,
			webSocketURL: `wss://${ip.address().replace(/\./g, '-')}-${port}.local.mucang.cn/ws`
		}
	},
	css: {
		loaderOptions: {
			less: {
				lessOptions: {
					modifyVars: antdStyle,
					javascriptEnabled: true
				}
			}
		}
	},
	transpileDependencies: ['admin-library'],
	chainWebpack: config => {
		// 移除 prefetch 插件
		config.plugins.delete('preload'); // TODO: need test
		config.plugins.delete('prefetch'); // TODO: need test
		// 不同环境对应不同的map
		if (isLocal || isDev) {
			config.devtool('source-map');
		} else if (isTest) {
			config.devtool('cheap-source-map');
		}

		config.resolve.alias.set('@', resolve('src'));
		config.resolve.alias.set('library', resolve('node_modules/paas-library'));
	},
	configureWebpack: config => {
		config.name = '驾考宝典企业版·木仓科技荣誉出品';
		config.plugins.push(
			new PaasCheckPlugin({
				checkCodePath: resolve('src/background'),
				words: ['@paas/paas-library'],
				checkTypePath: resolve('src'),
				callback: () => {
					const realUrl = resolve(FRAMEWORK_CONFIG_URL);
					const framework = require(realUrl);
					let frameworkStr = '';
					const hosts = {};

					Object.keys(appConfig.domain).forEach(key => {
						// base 那边需要的域名是带 / 结尾的
						hosts[key] = appConfig.domain[key] + '/';
					});

					framework.appList = appConfig.appList;
					framework.initialize.hosts = hosts;

					frameworkStr =
						JSON.stringify(framework, null, '\t').replace(new RegExp(/\[[^,]*\]/g), str =>
							str.replace(/[\s\t]/g, '')
						) + '\n';
					fs.writeFile(realUrl, frameworkStr, 'utf-8', () => {
						console.log('config 环境相关配置写入成功');
					});
				}
			}),
			// 定义全局变量
			new webpack.DefinePlugin({
				ConfigJson: JSON.stringify(getConfigJson()),
				APP: JSON.stringify(appConfig)
			}),
			new styleLintPlugin({
				configFile: path.resolve(__dirname, './.stylelintrc.js'), // 加载配置文件
				files: ['**/*.{vue,css,less}'], // 要检查的扩展名
				lintDirtyModulesOnly: false, // 仅检查有变化的文件
				fix: false, // 是否自动修复
				cache: false, // 是否缓存
				emitWarning: true, // 开发运行时抛出Warning提示
				emitErrors: false // 开发运行时抛出Error提示
			})
		);
		config.watchOptions = {
			aggregateTimeout: 100
		};

		config.cache = {
			// 将缓存类型设置为文件系统, 默认是memory
			type: 'filesystem',
			buildDependencies: {
				// 更改配置文件时，重新缓存
				config: [__filename]
			},
			name: 'AppBuildCache'
		};

		let snapshot = config.snapshot || {};

		snapshot.managedPaths = [/^(.+?[\\/]node_modules)[\\/]((?!@paas)).*[\\/]*/];
		config.snapshot = snapshot;
	}
});
