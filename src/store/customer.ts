import { AgentStore, MethodTypeModel } from 'admin-library';

export class List<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/customer/list.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data?.itemList || data;
		}
	};
}

export class Create<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/customer/create.htm`;
	method: MethodTypeModel = 'POST';
}

export class Update<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/customer/update.htm`;
	method: MethodTypeModel = 'POST';
}

export class Del<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/customer/delete.htm`;
	method: MethodTypeModel = 'POST';
}

export class ChangeBind<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/customer/change-bind.htm`;
	method: MethodTypeModel = 'POST';
}

export class CustomerRelationInfo<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/customer/get-customer-relation-info.htm`;
	method: MethodTypeModel = 'GET';
}


export class CreateReceptionConfig<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/config/create-supervisor-auto-allocate-strategy-config.htm`;
	method: MethodTypeModel = 'POST';
}

export class ViewReceptionConfig<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/config/view-supervisor-auto-allocate-strategy-config.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			const t = JSON.parse(data?.value || '{}');
			return t;
		}
	};
}

export class AddBlack<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/customer/add-blacklist.htm`;
	method: MethodTypeModel = 'POST';
}

export class RemoveBlack<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/customer/remove-blacklist.htm`;
	method: MethodTypeModel = 'POST';
}
