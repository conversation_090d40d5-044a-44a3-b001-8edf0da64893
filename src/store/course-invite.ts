import { AgentStore, MethodTypeModel } from 'admin-library';

export class List<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-lecture-invite/list.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data?.itemList || data;
		}
	};
}

export class Create<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-lecture-invite/create-invite.htm`;
	method: MethodTypeModel = 'POST';
}

export class Cancel<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-lecture-invite/forced-close.htm`;
	method: MethodTypeModel = 'POST';
}
