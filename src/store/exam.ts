import { AgentStore, MethodTypeModel } from 'admin-library';

export class <PERSON>ck<PERSON><PERSON>ord<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/mucang-user-data/query-mock-exam.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data?.itemList || data;
		}
	};
}

export class QuestionsExamPoint<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/mucang-user-data/query-questions.htm`;
	method: MethodTypeModel = 'POST';
	options = {
		dataFilter(data) {
			return data?.itemList || data;
		}
	};
}

export class WrongBook<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/mucang-user-data/query-wrong-book.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data?.itemList || data;
		}
	};
}

export class ExamWrongQuestion<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/mucang-user-data/query-exam-wrong-question.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data?.itemList || data;
		}
	};
}
