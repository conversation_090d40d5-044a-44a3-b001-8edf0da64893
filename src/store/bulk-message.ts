import { AgentStore, MethodTypeModel } from 'admin-library';

export class List<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/bulk-message/list.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data?.itemList || data;
		}
	};
}

export class Create<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/bulk-message/create.htm`;
	method: MethodTypeModel = 'POST';
}

export class Update<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/bulk-message/update.htm`;
	method: MethodTypeModel = 'POST';
}

export class Delete<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/bulk-message/delete.htm`;
	method: MethodTypeModel = 'POST';
}

export class View<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/bulk-message/view.htm`;
	method: MethodTypeModel = 'GET';
}

/**
 * 群发目标列表
 */
export class ContactList<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/wechat-external-contact/list.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			const list = data?.itemList || data;
			return list.map(item => {
				const { contactName, contactRemark } = item;
				let contactMixName = contactName;
				if (contactRemark && contactRemark !== contactName) {
					contactMixName = `${contactName}(${contactRemark})`;
				}
				return {
					...item,
					contactMixName
				};
			});
		}
	};
}

export class Cancel<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/bulk-message/cancel.htm`;
	method: MethodTypeModel = 'POST';
}

export class Reopen<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/bulk-message/resubmit.htm`;
	method: MethodTypeModel = 'POST';
}

export class TagList<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/wechat-tag/list.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data?.itemList || data;
		}
	};
}
