import { AgentStore, MethodTypeModel } from 'admin-library';

export class List<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/settlement/list.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data?.itemList || data;
		}
	};
}

export class Create<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/settlement/create.htm`;
	method: MethodTypeModel = 'POST';
}

export class UnSettlementCourseList<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-course-schedule/un-settlement-list.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data?.itemList || data;
		}
	};
}

export class DetailList<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/settlement-detail/list.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data?.itemList || data;
		}
	};
}

export class Repay<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/settlement-detail/re-pay.htm`;
	method: MethodTypeModel = 'POST';
}
