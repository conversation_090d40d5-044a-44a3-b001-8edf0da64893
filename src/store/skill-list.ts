import { AgentStore, MethodTypeModel } from 'admin-library';

export class List<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-lecturer/get-lecturer-skill-groups.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data?.itemList || data;
		}
	};
}

export class Create<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-lecturer/add-skill-group.htm`;
	method: MethodTypeModel = 'POST';
}

export class Update<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-lecturer/update-skill-group.htm`;
	method: MethodTypeModel = 'POST';
}

export class Del<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-lecturer/remove-skill-group.htm`;
	method: MethodTypeModel = 'POST';
}

export class SkillGroupsList<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-lecturer/get-skill-groups.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data?.itemList || data;
		}
	};
}
