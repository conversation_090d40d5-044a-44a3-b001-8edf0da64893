import { AgentStore, MethodTypeModel } from 'admin-library';
import { FetchStore } from '@simplex/simple-store';

export class WeixinUploadStore<T> extends FetchStore<T> {
	constructor(progressCallback) {
		super({
			baseURL: APP.domain.parrot,
			url: '/api/admin/bulk-message/upload-file.htm',
			method: 'POST',
			headers: { 'Content-Type': 'multipart/form-data' },
			withCredentials: true,
			onUploadProgress(progress) {
				progressCallback && progressCallback(parseInt(String((progress.loaded / progress.total) * 100)));
			}
		});
	}
}

export class SsoUserList<T> extends AgentStore<T> {
	url = `https://sso.kakamobi.cn/api/admin/user/list-users.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data.itemList || data;
		}
	};
}

export class CityJson<T> extends AgentStore<T> {
	url = `https://web-resource.mc-cdn.cn/web/h5-xie/city.json`;
	method: MethodTypeModel = 'GET';
}
