import { AgentStore, MethodTypeModel } from 'admin-library';

export class List<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/student-evaluate/list.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data.itemList || data;
		}
	};
}
export class FollowList<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/student-evaluate/list-follow-record.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data.itemList || data;
		}
	};
}

export class FollowUp<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/student-evaluate/follow-up.htm`;
	method: MethodTypeModel = 'POST';
}

export class UpdateMalicious<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/student-evaluate/update-is-malicious.htm`;
	method: MethodTypeModel = 'POST';
}
