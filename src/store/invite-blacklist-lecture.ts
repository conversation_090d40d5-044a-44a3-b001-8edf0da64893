import { AgentStore, MethodTypeModel } from 'admin-library';

export class List<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-lecturer/black-list.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			const list = data?.itemList || data;
			return list.map(item => {
				const { name, nickName } = item;
				let lectureMixName = name;
				if (name && nickName) {
					lectureMixName = `${name}(${nickName})`;
				}
				return {
					...item,
					lectureMixName
				};
			});
		}
	};
}

export class Add<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-lecture-blacklist/add.htm`;
	method: MethodTypeModel = 'POST';
}

export class Recovery<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-lecture-blacklist/recovery.htm`;
	method: MethodTypeModel = 'POST';
}

export class BlackList<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-lecture-blacklist/list.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data?.itemList || data;
		}
	};
}
