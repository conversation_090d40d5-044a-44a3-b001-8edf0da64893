import { AgentStore, MethodTypeModel } from 'admin-library';

export class MockList<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-course-homework/get-mock-list.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data?.itemList || data;
		}
	};
}

export class PracticeList<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-course-homework/get-practice-data.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data?.itemList || data;
		}
	};
}

export class WrongBook<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-course-homework/get-wrong-book.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data?.itemList || data;
		}
	};
}

export class ExamWrongQuestion<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/mucang-user-data/query-exam-wrong-question.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data?.itemList || data;
		}
	};
}
