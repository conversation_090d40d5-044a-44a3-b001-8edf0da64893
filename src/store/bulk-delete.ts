import { AgentStore, MethodTypeModel } from 'admin-library';

export class List<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-command-task/list.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data?.itemList || data;
		}
	};
}

export class Create<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-command-task/create.htm`;
	method: MethodTypeModel = 'POST';
}

export class Update<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-command-task/update.htm`;
	method: MethodTypeModel = 'POST';
}

export class Delete<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-command-task/delete.htm`;
	method: MethodTypeModel = 'POST';
}

export class Publish<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-command-task/publish.htm`;
	method: MethodTypeModel = 'POST';
}
