import { AgentStore, MethodTypeModel } from 'admin-library';

export class List<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-course-homework/list.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data?.itemList || data;
		}
	};
}

export class Create<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-course-homework/create.htm`;
	method: MethodTypeModel = 'POST';
}

export class Update<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-course-homework/update.htm`;
	method: MethodTypeModel = 'POST';
}

export class Delete<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-course-homework/delete.htm`;
	method: MethodTypeModel = 'POST';
}

export class TagList<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-course-homework/tag-question-resps.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data?.itemList || data;
		}
	};
}

export class ClearHomework<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-course-schedule/clear-homework.htm`;
	method: MethodTypeModel = 'POST';
}

export class SpecialList<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-course-homework/get-special-list.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data?.itemList || data;
		}
	};
}
