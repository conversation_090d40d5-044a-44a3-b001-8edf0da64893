import { AgentStore, MethodTypeModel } from 'admin-library';

export class List<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-daily-bulk-msg/list.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data?.itemList || data;
		}
	};
}
export class Update<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-daily-bulk-msg/enter.htm`;
	method: MethodTypeModel = 'POST';
}

export class Publish<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-daily-bulk-msg/confirm.htm`;
	method: MethodTypeModel = 'POST';
}
