import { AgentStore, MethodTypeModel } from 'admin-library';

export class List<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-course-schedule/list.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data?.itemList || data;
		}
	};
}

export class BatchCreate<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-course-schedule/batch-create.htm`;
	method: MethodTypeModel = 'POST';
}

export class UpdateTime<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-course-schedule/update-course-time.htm`;
	method: MethodTypeModel = 'POST';
}

export class Cancel<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-course-schedule/cancel-course.htm`;
	method: MethodTypeModel = 'POST';
}

export class UpdateLecturer<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-course-schedule/update-lecturer.htm`;
	method: MethodTypeModel = 'POST';
}

export class GetVideo<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-course-schedule/get-playback-url.htm`;
	method: MethodTypeModel = 'GET';
}

export class Inspect<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-course-schedule/acceptance.htm`;
	method: MethodTypeModel = 'POST';
}

export class UpdatePlaybackVisible<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-course-schedule/update-playback-visible.htm`;
	method: MethodTypeModel = 'POST';
}

export class StudentList<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/student/schedulable-student-list.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			const list = data?.itemList || data;
			return list.map(item => {
				const { name, nickName } = item;
				let mixName = name;
				if (name && nickName) {
					mixName = `${name}(${nickName})`;
				}
				return {
					...item,
					mixName
				};
			});
		}
	};
}

export class CourseScheduleList<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-course-schedule/get-course-list.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data?.itemList || data;
		}
	};
}

export class Submit<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-course-schedule/submit.htm`;
	method: MethodTypeModel = 'POST';
}

export class Abend<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-course-schedule/abend.htm`;
	method: MethodTypeModel = 'POST';
}

export class RecommendLecturerList<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/student-profile/intelligent-recommend-lecturer.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data?.itemList || data;
		}
	};
}

export class ViewVideoMachineAudit<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-course-video-machine-audit/view.htm`;
	method: MethodTypeModel = 'GET';
}

export class GetInspectionInfo<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-course-schedule/get-inspection-info.htm`;
	method: MethodTypeModel = 'GET';
}

export class UpdateInspectionInfo<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-course-schedule/update-inspection-info.htm`;
	method: MethodTypeModel = 'POST';
}

export class SuggestContinue<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-course-schedule/suggest-continue.htm`;
	method: MethodTypeModel = 'POST';
}

export class CourseExamineList<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-course-schedule/get-course-examine-info.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data?.itemList || data;
		}
	};
}

export class TeachingRoomList<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/teaching-room/get-teaching-courses.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data?.itemList || data;
		}
	};
}

export class GetRoomPullStream<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/teaching-room/get-pull-stream.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data?.itemList || data;
		}
	};
}

export class TrainAdminAuth<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/teaching-room/administrator-auth.htm`;
	method: MethodTypeModel = 'GET';
}

export class StopTeach<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/teaching-room/stop-teach.htm`;
	method: MethodTypeModel = 'POST';
}

export class GetRoomInfo<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/teaching-room/get-teaching-room-info.htm`;
	method: MethodTypeModel = 'POST';
}

export class UpdateCarTypeAndKemu<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-course-schedule/update-car-type-and-kemu.htm`;
	method: MethodTypeModel = 'POST';
}

export class RecommendSprint<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-course-schedule/recommend-sprint.htm`;
	method: MethodTypeModel = 'POST';
}

export class CancelRecommendSprint<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-course-schedule/cancel-recommend-sprint.htm`;
	method: MethodTypeModel = 'POST';
}

export class GetChatList<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-course-schedule/get-course-teaching-msg.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data?.itemList || data;
		}
	};
}
