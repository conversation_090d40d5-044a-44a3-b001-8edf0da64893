import { AgentStore, MethodTypeModel } from 'admin-library';

export class List<T> extends AgentStore<T> {
  url = `${APP.domain['parrot']}/api/admin/cs-reception-router-config/list.htm`;
  method: MethodTypeModel = 'GET';
  options = {
    dataFilter(data) {
      return data?.itemList || data;
    }
  };
}

export class Del<T> extends AgentStore<T> {
  url = `${APP.domain['parrot']}/api/admin/cs-reception-router-config/delete.htm`;
  method: MethodTypeModel = 'POST';
}

export class Create<T> extends AgentStore<T> {
  url = `${APP.domain['parrot']}/api/admin/cs-reception-router-config/create.htm`;
  method: MethodTypeModel = 'POST';
}

export class Update<T> extends AgentStore<T> {
  url = `${APP.domain['parrot']}/api/admin/cs-reception-router-config/update.htm`;
  method: MethodTypeModel = 'POST';
}

export class GetConditions<T> extends AgentStore<T> {
  url = `${APP.domain['parrot']}/api/admin/cs-reception-router-config/conditions/view.htm`;
  method: MethodTypeModel = 'GET';
}

export class SetConditions<T> extends AgentStore<T> {
  url = `${APP.domain['parrot']}/api/admin/cs-reception-router-config/conditions/update.htm`;
  method: MethodTypeModel = 'POST';
}

export class GetReceptionSeq<T> extends AgentStore<T> {
  url = `${APP.domain['parrot']}/api/admin/config/cs-reception-type-config.htm`;
  method: MethodTypeModel = 'GET';
}
