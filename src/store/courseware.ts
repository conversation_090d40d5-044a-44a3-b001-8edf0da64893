import { AgentStore, MethodTypeModel } from 'admin-library';

/**
 * 列表
 */
export class List<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pts-courseware/list.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data?.itemList || data;
		}
	};
}

export class Create<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pts-courseware/create-courseware.htm`;
	method: MethodTypeModel = 'POST';
}

export class Update<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pts-courseware/update-courseware.htm`;
	method: MethodTypeModel = 'POST';
}

export class Del<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pts-courseware/delete-courseware.htm`;
	method: MethodTypeModel = 'POST';
}

export class ResourceList<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pts-courseware/resource-list.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data?.itemList || data;
		}
	};
}

export class ResourceCreate<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pts-courseware/create-resource.htm`;
	method: MethodTypeModel = 'POST';
}

export class ResourceUpdate<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pts-courseware/update-resource.htm`;
	method: MethodTypeModel = 'POST';
}

export class ResourceDel<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pts-courseware/delete-resource.htm`;
	method: MethodTypeModel = 'POST';
}

export class SyncSpeTag<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}//api/admin/pts-special/sync-tiku-tag.htm`;
	method: MethodTypeModel = 'POST';
}

export class KnowledgeList<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pts-courseware/get-knowledge-list-by-courseware-id.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data?.itemList || data;
		}
	};
}

export class UpdateLevelInfo<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pts-courseware/update-level-info.htm`;
	method: MethodTypeModel = 'POST';
}
