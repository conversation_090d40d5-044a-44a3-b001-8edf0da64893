import { AgentStore, MethodTypeModel } from 'admin-library';

export class List<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/im-bulk-msg/list.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data?.itemList || data;
		}
	};
}

export class Create<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/im-bulk-msg/create.htm`;
	method: MethodTypeModel = 'POST';
}

export class Update<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/im-bulk-msg/update.htm`;
	method: MethodTypeModel = 'POST';
}

export class Cancel<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/im-bulk-msg/cancel.htm`;
	method: MethodTypeModel = 'POST';
}

export class Withdraw<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/im-bulk-msg/msg-withdraw.htm`;
	method: MethodTypeModel = 'POST';
}

export class AccountList<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/im-bulk-msg/get-im-account.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data?.itemList || data;
		}
	};
}
