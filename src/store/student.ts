import { AgentStore, MethodTypeModel } from 'admin-library';

export class List<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/student-profile/list.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data?.itemList || data;
		}
	};
}

export class Create<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/student-profile/create.htm`;
	method: MethodTypeModel = 'POST';
}

export class Del<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/student-profile/delete.htm`;
	method: MethodTypeModel = 'POST';
}

export class AllocateUpdate<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/student-profile/allocate-lecturer.htm`;
	method: MethodTypeModel = 'POST';
}

export class SuspendUpdate<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/student-profile/suspend.htm`;
	method: MethodTypeModel = 'POST';
}

export class TerminateUpdate<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/student-profile/terminate.htm`;
	method: MethodTypeModel = 'POST';
}

export class Recover<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/student-profile/recover.htm`;
	method: MethodTypeModel = 'POST';
}

export class AllocateSupervisorUpdate<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/student-profile/allocate-supervisor.htm`;
	method: MethodTypeModel = 'POST';
}

export class Grad<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/student-profile/grad.htm`;
	method: MethodTypeModel = 'POST';
}

export class UpdateStatus<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/student-profile/update-suggest-exam-status.htm`;
	method: MethodTypeModel = 'POST';
}

export class UpdatePeriod<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/student-profile/update-lesson-period.htm`;
	method: MethodTypeModel = 'POST';
}

export class GetChangeLog<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/student-profile/get-change-log.htm`;
	method: MethodTypeModel = 'GET';
}

export class UpdateScheduleCourseNotify<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/student-profile/update-schedule-course-notify.htm`;
	method: MethodTypeModel = 'POST';
}

export class RemarkCommentList<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/comment/get-student-comment-list.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data?.itemList || data;
		}
	};
}
export class AddRemarkComment<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/comment/add-student-remark-comment.htm`;
	method: MethodTypeModel = 'POST';
}
export class UpdateRemarkComment<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/comment/update-content.htm`;
	method: MethodTypeModel = 'POST';
}

export class GetPhone<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/student-profile/get-phone.htm`;
	method: MethodTypeModel = 'GET';
}

export class GetNotifyConfig<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/student-profile/get-notify-config.htm`;
	method: MethodTypeModel = 'GET';
}

export class SetNotifyConfig<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/student-profile/set-notify-config.htm`;
	method: MethodTypeModel = 'POST';
}

export class ChangeStudyPlan<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/student-profile/change-study-plan.htm`;
	method: MethodTypeModel = 'POST';
}

export class GetLayerConfig<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/config/view-student-category-config.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			const t = JSON.parse(data?.value || '{}');
			return t?.itemList || t;
		}
	};
}

export class SetLayerConfig<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/config/create-student-category-config.htm`;
	method: MethodTypeModel = 'POST';
}

export class viewSummary<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/student-profile/view-study-summary.htm`;
	method: MethodTypeModel = 'GET';
}

export class GetClassType<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/class-type/list-all.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data?.itemList || data;
		}
	};
}

export class ChangeClassType<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/student-profile/change-class-type.htm`;
	method: MethodTypeModel = 'POST';
}

export class StudyPlanList<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/student-profile/study-plan-list.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data?.itemList || data;
		}
	};
}

export class CreateGroup<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/student-profile/create-group.htm`;
	method: MethodTypeModel = 'POST';
}

export class RemoveLecturer<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/student-profile/delete-lecturer.htm`;
	method: MethodTypeModel = 'POST';
}

export class GetVipList<T> extends AgentStore<T> {
	url = `${APP.domain['squirrel']}/api/admin/gift-whitelist-config/list.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data?.itemList || data;
		}
	};
}

export class SendVip<T> extends AgentStore<T> {
	url = `${APP.domain['squirrel']}/api/admin/gift-order/create.htm`;
	method: MethodTypeModel = 'POST';
}

export class WakeUpStudent<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/student-profile/wake-up-student.htm`;
	method: MethodTypeModel = 'POST';
}

export class GetSessionInfo<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/student-profile/get-session-permission-info.htm`;
	method: MethodTypeModel = 'GET';
}
export class ApplyTempGroup<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/student-profile/apply-temp-session-staff.htm`;
	method: MethodTypeModel = 'POST';
}

export class StudentList<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/student-profile/list.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			const list = data?.itemList || data;
			return list
				.map(item => {
					const { name, nickName } = item;
					let mixName = name;
					if (name && nickName) {
						mixName = `${name}(${nickName})`;
					}
					return {
						...item,
						mixName
					};
				})
				.filter(item => {
					return item.learnStatus === 0 || item.learnStatus === 10;
				});
		}
	};
}

export class TransferSubject<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/student-profile/transfer-car-and-kemu.htm`;
	method: MethodTypeModel = 'POST';
}
