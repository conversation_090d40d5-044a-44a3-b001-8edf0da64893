import { AgentStore, MethodTypeModel } from 'admin-library';

export class List<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-account/list.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data?.itemList || data;
		}
	};
}

export class Create<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-account/create.htm`;
	method: MethodTypeModel = 'POST';
}

export class Update<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-account/update.htm`;
	method: MethodTypeModel = 'POST';
}

export class Disable<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-account/disable.htm`;
	method: MethodTypeModel = 'GET';
}

export class Enable<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-account/enable.htm`;
	method: MethodTypeModel = 'GET';
}

export class Assign<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/account-assign/assign.htm`;
	method: MethodTypeModel = 'POST';
}


export class StartLogin<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-account/get-login-qr-code.htm`;
	method: MethodTypeModel = 'POST';
}


export class GetQrcode<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-account/get-qr-code-result.htm`;
	method: MethodTypeModel = 'POST';
}