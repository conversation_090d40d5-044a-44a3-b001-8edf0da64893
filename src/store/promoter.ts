import { AgentStore, MethodTypeModel } from 'admin-library';

export class List<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/employee/list.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data?.itemList || data;
		}
	};
}

export class Create<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/employee/create.htm`;
	method: MethodTypeModel = 'POST';
}

export class Update<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/employee/update.htm`;
	method: MethodTypeModel = 'POST';
}
export class UpdateEmployeeStatus<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/employee/change-status.htm`;
	method: MethodTypeModel = 'POST';
}
export class UpdateLecturerStatus<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-lecturer/change-status.htm`;
	method: MethodTypeModel = 'POST';
}

export class Del<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/employee/delete.htm`;
	method: MethodTypeModel = 'POST';
}

export class StartLogin<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/employee/get-login-qr-code.htm`;
	method: MethodTypeModel = 'GET';
}

export class GetQrcode<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/employee/get-qr-code-result.htm`;
	method: MethodTypeModel = 'GET';
}

export class GetWelcome<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/employee/get-welcome-msg-config.htm`;
	method: MethodTypeModel = 'GET';
}

export class SetWelcome<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/employee/set-welcome-msg-config.htm`;
	method: MethodTypeModel = 'POST';
}

export class RefreshWxId<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/employee/refresh-weixin-id.htm`;
	method: MethodTypeModel = 'POST';
}
