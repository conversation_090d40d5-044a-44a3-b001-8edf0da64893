import { AgentStore, MethodTypeModel } from 'admin-library';

export class List<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/small-class/get-small-classes.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data?.itemList || data;
		}
	};
}

export class Create<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/small-class/create-small-class.htm`;
	method: MethodTypeModel = 'POST';
}

export class Update<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/small-class/update-small-class.htm`;
	method: MethodTypeModel = 'POST';
}

export class Offline<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/small-class/offline-small-class.htm`;
	method: MethodTypeModel = 'POST';
}

export class Online<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/small-class/go-online-small-class.htm`;
	method: MethodTypeModel = 'POST';
}