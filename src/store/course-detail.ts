import { AgentStore, MethodTypeModel } from 'admin-library';

// export class List<T> extends AgentStore<T> {
// 	url = `${APP.domain['parrot']}/api/admin/small-class/get-schedules.htm`;
// 	method: MethodTypeModel = 'GET';
// 	options = {
// 		dataFilter(data) {
// 			return data?.itemList || data;
// 		}
// 	};
// }

export class List<T> extends AgentStore<T> {
	classNo: string | number;
	constructor(classNo: string) {
		super();
		this.url = `${APP.domain['parrot']}/api/admin/small-class/get-schedules.htm?${classNo}`;
	}
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data?.itemList || data;
		}
	};
}

export class Create<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/small-class/create-schedule.htm`;
	method: MethodTypeModel = 'POST';
}

export class Update<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/small-class/update-schedule.htm`;
	method: MethodTypeModel = 'POST';
}

export class Abandon<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/small-class/draft-schedule.htm`;
	method: MethodTypeModel = 'POST';
}