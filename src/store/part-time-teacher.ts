import { AgentStore, MethodTypeModel } from 'admin-library';

export class List<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-lecturer/list.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data?.itemList || data;
		}
	};
}

export class Create<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-lecturer/create.htm`;
	method: MethodTypeModel = 'POST';
}

export class BatchCreate<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-lecturer/create-batch.htm`;
	method: MethodTypeModel = 'POST';
}

export class Update<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-lecturer/update.htm`;
	method: MethodTypeModel = 'POST';
}

export class BatchCreateSkill<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-lecturer/batch-save-skill-group.htm`;
	method: MethodTypeModel = 'POST';
}

export class SetFinanceInfo<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-lecturer/set-finance-info.htm`;
	method: MethodTypeModel = 'POST';
}

export class GetFinanceInfo<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-lecturer/get-finance-info.htm`;
	method: MethodTypeModel = 'GET';
}

export class SetContractStatus<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-lecturer/set-contract-status.htm`;
	method: MethodTypeModel = 'POST';
}

export class SearchAndSetContractStatus<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-lecturer/update-sign-status.htm`;
	method: MethodTypeModel = 'POST';
}

export class LecturerScheduleList<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-lecturer/get-lecturer-future-schedule.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data?.itemList || data;
		}
	};
}

export class ChangeLevel<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-lecturer/add-change-level-task.htm`;
	method: MethodTypeModel = 'POST';
}

export class LevelLogList<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-lecturer/list-change-level-task.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data?.itemList || data;
		}
	};
}

export class ClearSchedule<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-lecturer/clear-lecturer-schedule.htm`;
	method: MethodTypeModel = 'POST';
}

export class ClearScheduleList<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-lecturer/lecturer-schedule-clear-record-list.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data.itemList || data;
		}
	};
}

export class WakeUpLecturer<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-lecturer/wake-up-lecturer.htm`;
	method: MethodTypeModel = 'POST';
}

export class ChangeLayer<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-lecturer/change-layer.htm`;
	method: MethodTypeModel = 'POST';
}

export class LayerLogList<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-lecturer/lecturer-layer-change-log-list.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data?.itemList || data;
		}
	};
}

export class CreateGroup<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-lecturer/create-lecturer-session-group.htm`;
	method: MethodTypeModel = 'POST';
}

export class GetPhone<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-lecturer/get-phone.htm`;
	method: MethodTypeModel = 'GET';
}
