import { AgentStore, MethodTypeModel } from 'admin-library';

export class List<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-wechat-group-manage/list.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data?.itemList || data;
		}
	};
}

export class Create<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-wechat-group-manage/create.htm`;
	method: MethodTypeModel = 'POST';
}

export class Update<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-wechat-group-manage/update.htm`;
	method: MethodTypeModel = 'POST';
}

export class Publish<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-wechat-group-manage/publish.htm`;
	method: MethodTypeModel = 'POST';
}

export class GetGroupCount<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-wechat-group-manage/group-member-list.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data?.itemList || data;
		}
	};
}
