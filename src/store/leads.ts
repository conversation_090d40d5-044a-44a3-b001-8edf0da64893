import { AgentStore, MethodTypeModel } from 'admin-library';

/**
 * 列表
 */
export class List<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/leads/list.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data?.itemList || data;
		}
	};
}

export class Update<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/leads/update.htm`;
	method: MethodTypeModel = 'POST';
}

export class viewSummary<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/leads/view-study-summary.htm`;
	method: MethodTypeModel = 'GET';
}

export class ChangeBind<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/leads/change-bind.htm`;
	method: MethodTypeModel = 'POST';
}

export class GetWecomTag<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/leads/query-wechat-tag-names.htm`;
	method: MethodTypeModel = 'GET';
}
