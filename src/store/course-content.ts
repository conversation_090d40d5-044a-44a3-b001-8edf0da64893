import { AgentStore, MethodTypeModel } from 'admin-library';

export class List<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pts-lesson-plan/list-plan-content.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data?.itemList || data;
		}
	};
}

export class Create<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pts-lesson-plan/insert-plan-content.htm`;
	method: MethodTypeModel = 'POST';
}

export class Update<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pts-lesson-plan/update-plan-content.htm`;
	method: MethodTypeModel = 'POST';
}

export class Delete<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pts-lesson-plan/delete-plan-content.htm`;
	method: MethodTypeModel = 'POST';
}

export class ResetPlan<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pts-lesson-plan/delete-plan.htm`;
	method: MethodTypeModel = 'POST';
}
