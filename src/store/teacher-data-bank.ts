import { AgentStore, MethodTypeModel } from 'admin-library';

export class List<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-material/list.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data?.itemList || data;
		}
	};
}
export class Create<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-material/create.htm`;
	method: MethodTypeModel = 'POST';
}
export class Update<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-material/update.htm`;
	method: MethodTypeModel = 'POST';
}

export class Del<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-material/delete.htm`;
	method: MethodTypeModel = 'POST';
}

/**分组管理 */
export class GroupControlList<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-material-group/list.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data?.itemList || data;
		}
	};
}

export class GroupCreate<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-material-group/create.htm`;
	method: MethodTypeModel = 'POST';
}

export class GroupDel<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-material-group/delete.htm`;
	method: MethodTypeModel = 'POST';
}

export class GroupUpdate<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-material-group/update.htm`;
	method: MethodTypeModel = 'POST';
}


//资料库list
export class BankList<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-material/get-lecturer-material-progress.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data?.itemList || data;
		}
	};
}