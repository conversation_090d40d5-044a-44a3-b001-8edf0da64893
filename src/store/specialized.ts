import { AgentStore, MethodTypeModel } from 'admin-library';

export class List<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-specialized/list.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data?.itemList || data;
		}
	};
}

export class Create<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-specialized/create.htm`;
	method: MethodTypeModel = 'POST';
}

export class Update<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-specialized/update.htm`;
	method: MethodTypeModel = 'POST';
}

export class QueryQuestion<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-specialized/question-query.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data?.itemList || data;
		}
	};
}

export class SpeQuestionList<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-specialized-question/get-specialized-questions.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data?.itemList || data;
		}
	};
}

export class AddQuestion<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-specialized-question/batch-create.htm`;
	method: MethodTypeModel = 'POST';
}

export class RemoveQuestion<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-specialized-question/delete.htm`;
	method: MethodTypeModel = 'POST';
}

export class BatchRemoveQuestion<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-specialized-question/batch-delete.htm`;
	method: MethodTypeModel = 'POST';
}
