import { AgentStore, MethodTypeModel } from 'admin-library';

/**
 * 列表
 */
export class List<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-lesson/list.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data?.itemList || data;
		}
	};
}
/**
 * 查看
 */
export class View<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-lesson/view.htm`;
	method: MethodTypeModel = 'GET';
}

/**
 * 设置课程笔记
 */
export class SetResource<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-lesson/set-resource.htm`;
	method: MethodTypeModel = 'POST';
}

export class Create<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-lesson/create.htm`;
	method: MethodTypeModel = 'POST';
}

export class Update<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-lesson/update.htm`;
	method: MethodTypeModel = 'POST';
}

export class Del<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-lesson/delete.htm`;
	method: MethodTypeModel = 'POST';
}

export class QuesList<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-lesson/question-query.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data?.itemList || data;
		}
	};
}

export class homeworkQuesList<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-lesson/get-homework-list.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data?.itemList || data;
		}
	};
}

export class AddHomeworkQues<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-lesson/add-home-work.htm`;
	method: MethodTypeModel = 'POST';
}

export class DelHomeworkQues<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/pt-lesson/delete-home-work.htm`;
	method: MethodTypeModel = 'POST';
}
