import { TableColumn, TableDateFormat } from 'admin-library';
import { ColumnWidthEnum } from '@/shard/constant';

export const COLUMNS: TableColumn[] = [
	{
		title: '变更前',
		dataIndex: 'oldLayer',
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '变更后',
		dataIndex: 'newLayer',
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '变更原因',
		dataIndex: 'reason',
		width: 160
	},
	{
		title: '操作人',
		dataIndex: 'operatorName',
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '操作时间',
		dataIndex: 'operateTime',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	}
];
