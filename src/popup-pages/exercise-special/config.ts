import { ColumnXtype, TableColumn } from 'admin-library';
import { ColumnWidthEnum } from '@/shard/constant';
import { RESULT_MAP } from './constant';

export const COLUMNS: TableColumn[] = [
	{
		title: '试题Id',
		dataIndex: 'questionId',
		width: 120
	},
	{
		title: '试题题干',
		dataIndex: 'question',
		xtype: ColumnXtype.CUSTOM
	},
	{
		title: '作答状态',
		dataIndex: 'status',
		render: data => {
			return RESULT_MAP[data];
		},
		width: ColumnWidthEnum.TEXT4
	}
];
