<template>
	<pm-effi :controller="controller">
		<pm-table
			:columns="COLUMNS"
			:use-custom-column="true"
			:pagination="false"
			:operations="false"
			:sort-num="false"
			:scroll="{}"
		>
			<template #question="{ record }">
				<question-comp
					:question="record.question"
					:mediaType="record.mediaType"
					:mediaUrl="record.mediaUrl"
					:options="record"
				></question-comp>
			</template>
			<template #buttonsBottom>
				<div class="result-list">
					<div class="item">
						<div class="value">{{ accuracy }}</div>
						<div class="label">正确率</div>
					</div>
					<div class="item">
						<div class="value">{{ rightCount }}/{{ allQuestionCount }}</div>
						<div class="label">答对/总数</div>
					</div>
					<div class="item">
						<div class="value">{{ wrongCount }}</div>
						<div class="label">答错</div>
					</div>
					<div class="item">
						<div class="value">{{ unAnsweredCount }}</div>
						<div class="label">未答</div>
					</div>
				</div>
			</template>
		</pm-table>
	</pm-effi>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue';
import { useRoute } from 'vue-router';
import { ModelController } from 'admin-library';

import QuestionComp from '@/popup-pages/wrong-questions/comps/question/index.vue';
import { COLUMNS } from './config';
import { PracticeListStore } from './store';

export default defineComponent({
	components: { QuestionComp },
	setup() {
		const route = useRoute();
		const query = route.query;

		const components = {};

		const constants = {
			COLUMNS
		};

		const state = reactive({
			accuracy: '--',
			allQuestionCount: 0,
			wrongCount: 0,
			rightCount: 0,
			unAnsweredCount: 0
		});

		const controller = new ModelController({
			table: {
				store: PracticeListStore
			}
		});
		controller.tableRequest();

		controller.table.onRequest.use(params => {
			params = {
				...params,
				homeworkId: query.exerciseId
			};

			return params;
		});

		controller.table.onResponse.use(data => {
			data.data = data.data.questionAnswerInfoDataList;
			try {
				state.allQuestionCount = data.data.length;
				state.wrongCount = data.data.filter(item => item.status === 2).length;
				state.rightCount = data.data.filter(item => item.status === 1).length;
				state.unAnsweredCount = data.data.filter(item => item.status === 3).length;

				if (state.allQuestionCount) {
					let accuracy = (state.rightCount / state.allQuestionCount) * 100;
					accuracy = Number(accuracy.toFixed(1));
					state.accuracy = accuracy + '%';
				}
			} catch (error) {
				console.log(error);
			}
			return data;
		});

		const methods = {};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			controller
		};
	}
});
</script>

<style lang="less" scoped>
.result-list {
	display: flex;
	padding: 10px 0;
	.item {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		margin-right: 10px;
		width: 68px;
		height: 44px;
		background-color: #efefef;
	}
}
</style>
