<template>
	<pm-dialog v-model:visible="visible" :title="title" width="480px">
		<template v-if="visible">
			<m-form
				ref="formRef"
				:model="formState"
				:colon="false"
				:label-col="{ style: { width: '95px' } }"
				autocomplete="off"
			>
				<m-form-item label="车型" name="carType" required>
					<m-select v-model:value="formState.carType" :options="options.carList" placeholder="车型" />
				</m-form-item>
				<m-form-item label="科目" name="kemu" required>
					<m-select v-model:value="formState.kemu" :options="options.kemuList" placeholder="科目" />
				</m-form-item>
				<m-form-item label="审核状态" name="status" required>
					<m-select
						v-model:value="formState.status"
						:options="options.skillStatusList"
						placeholder="审核状态"
					/>
				</m-form-item>
			</m-form>
		</template>

		<template #footer>
			<m-button plain @click="visible = false">取消</m-button>
			<m-button :loading="loading" type="primary" @click="onConfirm">确定</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import Dayjs from 'dayjs';
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { MESSAGE_TYPE, MUtils } from 'admin-library';

import { cloneFromPick, cloneByMerge } from '@/shard/utils';
import { CAR_TYPE_OPTIONS, EXAM_KEMU_OPTIONS } from '@/shard/constant';
import { SKILL_STATUS_OPTIONS } from '@/application/part-time-teacher/constant';
import { CreateStore, UpdateStore } from '../../store';

const DefaultFormState = {
	skillGroupId: '',
	lecturerId: '',
	carType: null,
	kemu: null,
	status: null
};

export default defineComponent({
	emits: ['refresh'],
	setup(props, { emit }) {
		const components = {
			formRef: ref(null)
		};

		const constants = {};

		const state = reactive({
			visible: false,
			title: '',
			loading: false,
			formState: MUtils.deepClone(DefaultFormState),
			options: {
				carList: CAR_TYPE_OPTIONS,
				kemuList: EXAM_KEMU_OPTIONS,
				skillStatusList: SKILL_STATUS_OPTIONS
			}
		});

		const methods = {
			open(row?: any) {
				state.formState = MUtils.deepClone(DefaultFormState);
				state.title = row?.id ? '编辑' : '新增';

				if (row) {
					state.formState = cloneFromPick({ ...row, skillGroupId: row.id }, DefaultFormState);
				}

				if (state.formState.examTime) {
					state.formState.examTime = Dayjs(state.formState.examTime);
				}

				state.visible = true;
			},
			async onConfirm() {
				await components.formRef.value.validate();

				try {
					state.loading = true;
					const params = cloneByMerge(state.formState, DefaultFormState);
					const fetchStore = state.formState.skillGroupId ? UpdateStore : CreateStore;

					if (params.examTime) {
						params.examTime = +new Date(params.examTime);
					}

					await fetchStore.request(params).getData();

					emit('refresh');
					state.visible = false;
					MUtils.toast('保存成功', MESSAGE_TYPE.success);
				} finally {
					state.loading = false;
				}
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods
		};
	}
});
</script>
