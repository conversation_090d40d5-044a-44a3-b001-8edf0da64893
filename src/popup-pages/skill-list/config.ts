import { TableColumn } from 'admin-library';
import { ColumnWidthEnum, CAR_TYPE_MAP, EXAM_KEMU_MAP } from '@/shard/constant';
import { SKILL_STATUS_MAP } from '@/application/part-time-teacher/constant';

export const COLUMNS: TableColumn[] = [
	{
		title: '车型',
		dataIndex: 'carType',
		render: data => {
			return CAR_TYPE_MAP[data];
		},
		width: ColumnWidthEnum.TEXT5
	},
	{
		title: '科目',
		dataIndex: 'kemu',
		render: data => {
			return EXAM_KEMU_MAP[data];
		},
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '审核状态',
		dataIndex: 'status',
		render: data => {
			return SKILL_STATUS_MAP[data];
		},
		width: ColumnWidthEnum.TEXT4
	}
];
