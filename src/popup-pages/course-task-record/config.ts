import { TableColumn } from 'admin-library';
import { STATUS_MAP } from '@/application/courses/constant';

export const COLUMNS: TableColumn[] = [
	{
		title: '#',
		dataIndex: 'id'
	},
	{
		title: '排课数据Id',
		dataIndex: 'courseScheduleId',
		width: 160
	},
	{
		title: '排课状态',
		dataIndex: 'courseScheduleStatus',
		render: data => {
			return STATUS_MAP[data];
		},
		width: 160
	},
	{
		title: '企业任务Id',
		dataIndex: 'taskId',
		width: 160
	},
	{
		title: '云账户任务Id',
		dataIndex: 'taskRef',
		width: 160
	},
	{
		title: '云账户任务状态',
		dataIndex: 'taskStatus',
		width: 160
	},
	{
		title: '云账户任务状态说明',
		dataIndex: 'taskStatusMessage'
	}
];
