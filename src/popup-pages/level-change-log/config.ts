import { TableColumn, TableDateFormat } from 'admin-library';
import { ColumnWidthEnum } from '@/shard/constant';
import { EFFECT_TIME_MAP } from '@/application/part-time-teacher/constant';

export const COLUMNS: TableColumn[] = [
	{
		title: '变更前',
		dataIndex: 'preChangeLevel',
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '变更后',
		dataIndex: 'afterChangeLevel',
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '新等级生效时机',
		dataIndex: 'effectiveChance',
		render: data => {
			return EFFECT_TIME_MAP[data];
		},
		width: 160
	},
	{
		title: '操作人',
		dataIndex: 'operator',
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '操作时间',
		dataIndex: 'operateTime',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	}
];
