<template>
	<pm-dialog v-model:visible="visible" :title="title" width="680px">
		<template v-if="visible">
			<m-form
				ref="formRef"
				:model="formState"
				:colon="false"
				:label-col="{ style: { width: '95px' } }"
				autocomplete="off"
			>
				<m-form-item label="考试时间" name="examTime" required>
					<m-date-picker v-model:value="formState.examTime" valueFormat="YYYY/MM/DD" />
				</m-form-item>
				<m-form-item label="车型" name="carType" required>
					<m-select
						v-model:value="formState.carType"
						:options="options.carList"
						disabled
						placeholder="车型"
					/>
				</m-form-item>
				<m-form-item label="辅导科目" name="kemu" required>
					<m-select v-model:value="formState.kemu" :options="options.kemuList" disabled placeholder="科目" />
				</m-form-item>
				<m-form-item label="分数" name="score">
					<m-input-number v-model:value="formState.score" :min="0" placeholder="分数" />
				</m-form-item>
			</m-form>
		</template>

		<template #footer>
			<m-button plain @click="visible = false">取消</m-button>
			<m-button :loading="loading" type="primary" @click="onConfirm">确定</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import Dayjs from 'dayjs';
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { MESSAGE_TYPE, MUtils } from 'admin-library';

import { cloneFromPick, cloneByMerge } from '@/shard/utils';
import { CAR_TYPE_OPTIONS, EXAM_KEMU_OPTIONS } from '@/shard/constant';
import { CreateStore, UpdateStore } from '../../store';

const DefaultFormState = {
	id: null,
	customerNo: '',
	studentProfileId: '',
	examTime: '',
	carType: '',
	kemu: '',
	score: null
};

export default defineComponent({
	emits: ['refresh'],
	setup(props, { emit }) {
		const components = {
			formRef: ref(null)
		};

		const constants = {};

		const state = reactive({
			visible: false,
			title: '',
			loading: false,
			formState: MUtils.deepClone(DefaultFormState),
			options: {
				carList: CAR_TYPE_OPTIONS,
				kemuList: EXAM_KEMU_OPTIONS
			}
		});

		const methods = {
			open(row?: any) {
				state.formState = MUtils.deepClone(DefaultFormState);
				state.title = row?.id ? '编辑' : '新增';

				if (row) {
					state.formState = cloneFromPick(row, DefaultFormState);
				}

				if (state.formState.examTime) {
					state.formState.examTime = Dayjs(state.formState.examTime);
				}

				state.visible = true;
			},
			async onConfirm() {
				await components.formRef.value.validate();

				try {
					state.loading = true;
					const params = cloneByMerge(state.formState, DefaultFormState);
					const fetchStore = state.formState.id ? UpdateStore : CreateStore;

					if (params.examTime) {
						params.examTime = +new Date(params.examTime);
					}

					await fetchStore.request(params).getData();

					emit('refresh');
					state.visible = false;
					MUtils.toast('保存成功', MESSAGE_TYPE.success);
				} finally {
					state.loading = false;
				}
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods
		};
	}
});
</script>
