import { TableColumn, TableDateFormat } from 'admin-library';
import { ColumnWidthEnum, CAR_TYPE_MAP, EXAM_KEMU_MAP } from '@/shard/constant';

export const COLUMNS: TableColumn[] = [
	{
		title: '考试时间',
		dataIndex: 'examTime',
		type: 'date',
		dateFormat: TableDateFormat.DAY,
		width: ColumnWidthEnum.DATEDAY
	},
	{
		title: '车型',
		dataIndex: 'carType',
		render: data => {
			return CAR_TYPE_MAP[data];
		},
		width: ColumnWidthEnum.TEXT5
	},
	{
		title: '科目',
		dataIndex: 'kemu',
		render: data => {
			return EXAM_KEMU_MAP[data];
		},
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '考试成绩',
		dataIndex: 'score'
	},
	{
		title: '操作时间',
		dataIndex: 'updateTime',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	},
	{
		title: '操作人',
		dataIndex: 'updateUserName',
		width: ColumnWidthEnum.TEXT4
	}
];
