<template>
	<pm-effi :controller="controller">
		<pm-search>
			<pm-search-single
				:span="3"
				:model-value="carType"
				:antdProps="{
					placeholder: '车型'
				}"
				data-index="carType"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="3"
				:model-value="kemu"
				:antdProps="{
					placeholder: '科目'
				}"
				data-index="kemu"
				xtype="SELECT"
			/>
		</pm-search>
		<pm-table
			:columns="COLUMNS"
			:use-custom-column="true"
			:operations-fixed="true"
			:operations-width="120"
			:sort-num="false"
			:scroll="{
				x: 'max-content'
			}"
		>
			<template #beforeCustom>
				<m-button type="primary" @click="onCreate">添加</m-button>
			</template>
			<template #operations="{ record }">
				<m-button type="link" @click="onEdit(record)">编辑</m-button>
				<m-button danger type="link" @click="onDel(record)">删除</m-button>
			</template>
		</pm-table>
	</pm-effi>

	<edit-comp ref="editRef" @refresh="onRefresh"></edit-comp>
</template>

<script lang="ts">
import { defineComponent, ref, reactive, toRefs } from 'vue';
import { useRoute } from 'vue-router';
import { ModelController, MUtils, MESSAGE_TYPE } from 'admin-library';

import EditComp from './comps/edit-dialog/index.vue';
import { confirmMessageBox } from '@/shard/utils';
import { COLUMNS } from './config';
import { CAR_TYPE_STORE, EXAM_KEMU_STORE } from '@/shard/constant';
import { ListStore, DelStore } from './store';
import { ItemResponse } from './types';

export default defineComponent({
	components: {
		EditComp
	},
	setup() {
		const route = useRoute();
		let query = route.query;

		const components = {
			editRef: ref<InstanceType<typeof EditComp>>()
		};

		const constants = {
			COLUMNS
		};

		let kemu = query.kemu ? Number(query.kemu) : query.kemu;
		const state = reactive({
			carType: query.carType,
			kemu
		});

		const controller = new ModelController({
			table: {
				store: ListStore
			},
			search: {
				carType: {
					store: CAR_TYPE_STORE
				},
				kemu: {
					store: EXAM_KEMU_STORE
				}
			}
		});
		controller.tableRequest();

		controller.table.onRequest.use(params => {
			let { customerNo, studentProfileId } = query;
			params = {
				...params,
				customerNo,
				studentProfileId
			};

			return params;
		});

		const methods = {
			onCreate() {
				let kemu = query.kemu ? Number(query.kemu) : query.kemu;
				components.editRef.value.open({ ...query, kemu });
			},
			onEdit(row: ItemResponse) {
				components.editRef.value.open(row);
			},
			async onDel(row: ItemResponse) {
				await confirmMessageBox('确认删除吗？');

				await DelStore.request({ id: row.id }).getData();
				methods.onRefresh();
				MUtils.toast('删除成功', MESSAGE_TYPE.success);
			},

			onRefresh() {
				controller.tableRequest();
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			controller
		};
	}
});
</script>
