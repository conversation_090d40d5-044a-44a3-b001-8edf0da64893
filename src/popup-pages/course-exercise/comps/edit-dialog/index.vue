<template>
	<pm-dialog v-model:visible="visible" :title="title" width="680px">
		<template v-if="visible">
			<m-form
				ref="formRef"
				:model="formState"
				:colon="false"
				:label-col="{ style: { width: '95px' } }"
				autocomplete="off"
			>
				<m-form-item label="类型" name="type" required>
					<m-select
						:disabled="formState.id"
						@change="change"
						:options="options.typeList"
						v-model:value="formState.type"
						:allowClear="true"
						placeholder="类型"
					/>
				</m-form-item>
				<m-form-item label="次数" name="bizValue" v-if="formState.type === 1" required>
					<m-input-number v-model:value="formState.bizValue" :min="1" :max="10" placeholder="次数" />
				</m-form-item>
				<m-form-item label="专项名称" name="bizValue" v-else-if="formState.type === 2" required>
					<m-select
						v-model:value="formState.bizValue"
						:options="options.speList"
						:fieldNames="{ label: 'name', value: 'id' }"
						placeholder="专项名称"
					/>
				</m-form-item>
				<template v-else-if="formState.type === 4">
					<m-form-item label="时间节点" :name="['examScore', 'scopeType']" required>
						<m-select
							v-model:value="formState.examScore.scopeType"
							:options="options.scopeTypeList"
							placeholder="时间节点"
						/>
					</m-form-item>
					<m-form-item label="次数" :name="['examScore', 'times']" required>
						<m-input-number v-model:value="formState.examScore.times" :min="1" />
					</m-form-item>
					<m-form-item label="成绩" :name="['examScore', 'score']" required>
						<m-input-number v-model:value="formState.examScore.score" :min="1" />
					</m-form-item>
				</template>
			</m-form>
		</template>

		<template #footer>
			<m-button plain @click="visible = false">取消</m-button>
			<m-button :loading="loading" type="primary" @click="onConfirm">确定</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { MESSAGE_TYPE, MUtils } from 'admin-library';

import { cloneFromPick, cloneByMerge } from '@/shard/utils';
import { TYPE_OPTIONS, SCOPE_TYPE_OPTIONS } from '../../constant';
import { CreateStore, UpdateStore, SpecialListStore } from '../../store';

const DefaultFormState = {
	courseScheduleId: null,
	id: null,
	type: '',
	bizValue: '',
	examScore: {
		scopeType: null,
		times: null,
		scope: null
	},
	specialSource: 1
};

export default defineComponent({
	emits: ['refresh'],
	setup(props, { emit }) {
		const components = {
			formRef: ref(null)
		};

		const constants = {};

		const state = reactive({
			visible: false,
			title: '',
			loading: false,
			formState: MUtils.deepClone(DefaultFormState),
			options: {
				typeList: TYPE_OPTIONS,
				speList: [],
				scopeTypeList: SCOPE_TYPE_OPTIONS
			}
		});

		const methods = {
			open(row: any, courseId) {
				state.formState = MUtils.deepClone(DefaultFormState);
				state.title = row?.id ? '编辑' : '新增';

				if (row.type === 4) {
					row.examScore = JSON.parse(row.bizValue);
				}

				if (row) {
					state.formState = cloneFromPick(row, DefaultFormState);
				}

				state.visible = true;

				methods.initSpeOptions(courseId);
			},
			change() {
				state.formState.bizValue = '';
			},
			async initSpeOptions(courseId) {
				let res = await SpecialListStore.request({
					limit: 9999,
					courseId
				}).getData();
				if (res) {
					res = res.map(item => {
						item.id = String(item.id);
						return item;
					});
					state.options.speList = res;
					state.formState.specialSource = res[0].specialSource;
				}
			},

			async onConfirm() {
				await components.formRef.value.validate();

				try {
					state.loading = true;
					const params = cloneByMerge(state.formState, DefaultFormState);
					const fetchStore = state.formState.id ? UpdateStore : CreateStore;

					if (state.formState.type === 4) {
						params.bizValue = JSON.stringify(params.examScore);
					}

					if (!state.formState.id) {
						params.homeworkList = {
							type: params.type,
							bizValue: params.bizValue
						};
						params.homeworkList = JSON.stringify([params.homeworkList]);
					}

					await fetchStore.request(params).getData();

					emit('refresh');
					state.visible = false;
					MUtils.toast('保存成功', MESSAGE_TYPE.success);
				} finally {
					state.loading = false;
				}
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods
		};
	}
});
</script>
