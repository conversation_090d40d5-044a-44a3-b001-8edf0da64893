<template>
	<pm-effi :controller="controller">
		<pm-table
			:columns="COLUMNS"
			:use-custom-column="true"
			:pagination="false"
			:operations-fixed="true"
			:operations-width="230"
			:sort-num="false"
			:scroll="{
				x: 'max-content'
			}"
		>
			<template #beforeCustom>
				<m-button type="primary" @click="clearAll">清空</m-button>
				<m-button type="primary" v-if="showCreate" @click="onCreate">添加</m-button>
			</template>
			<template #pot="{ record }">
				<div v-if="record.type === 1">{{ record.bizValue }}</div>
				<div v-else-if="record.type === 2">{{ record.bizName }}</div>
				<div v-else-if="record.type === 4">{{ generateContent(record.bizValue) }}</div>
			</template>
			<template #operations="{ record }">
				<m-button type="link" v-if="record.type === 1 || record.type === 4" @click="onMockExam(record)">
					查看模拟考试记录
				</m-button>
				<m-button type="link" v-else-if="record.type === 2" @click="onSpecial(record)">查看专项训练</m-button>
				<m-button type="link" v-else-if="record.type === 3" @click="onWrongBook(record)">查看错题本</m-button>
				<m-button type="link" @click="onEdit(record)" :disabled="record.type === 3">编辑</m-button>
				<m-button danger type="link" @click="onDel(record)">删除</m-button>
			</template>
		</pm-table>
	</pm-effi>

	<edit-comp ref="editRef" @refresh="onRefresh"></edit-comp>
	<edit-course-table-comp ref="editCourseTableRef" @refresh="onRefresh"></edit-course-table-comp>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { ModelController, MUtils, MESSAGE_TYPE, PaasPostMessage } from 'admin-library';
import { useRoute } from 'vue-router';

import EditCourseTableComp from '@/application/settlement/comps/edit-course-table-dialog/index.vue';
import EditComp from './comps/edit-dialog/index.vue';
import { confirmMessageBox } from '@/shard/utils';
import { COLUMNS, COLUMNS2 } from './config';
import { SCOPE_TYPE_MAP } from './constant';
import { ListStore, DelStore, ClearHomeworkStore } from './store';
import { ItemResponse } from './types';

export default defineComponent({
	components: {
		EditComp,
		EditCourseTableComp
	},
	setup() {
		const route = useRoute();
		const query = route.query;

		const components = {
			editRef: ref<InstanceType<typeof EditComp>>(),
			editCourseTableRef: ref<InstanceType<typeof EditCourseTableComp>>()
		};

		const constants = {
			COLUMNS: query.courseScheduleId ? COLUMNS : COLUMNS2
		};

		const state = reactive({
			showCreate: query.courseScheduleId
		});

		const controller = new ModelController({
			table: {
				store: ListStore
			}
		});
		controller.tableRequest();

		controller.table.onRequest.use(params => {
			let { courseScheduleId, studentProfileId } = query;
			let sd = {};
			if (courseScheduleId) {
				sd = { courseScheduleId };
			} else {
				sd = { studentProfileId };
			}
			params = {
				...params,
				...sd
			};

			return params;
		});

		const methods = {
			parseContent(data) {
				return JSON.parse(data);
			},
			generateContent(data) {
				const content = JSON.parse(data);
				return `${SCOPE_TYPE_MAP[content.scopeType]}有${content.times}次模考成绩≥${content.score}`;
			},
			onWrongBook(row) {
				PaasPostMessage.post('navigation.to', '/#/exercise-wrong-book', {
					title: '查看错题本',
					query: {
						exerciseId: row?.id
					},
					extendData: {
						style: 'width: 60%; height: 90%; margin: 2% auto 0;'
					},
					target: '_popup'
				});
			},
			onSpecial(row) {
				PaasPostMessage.post('navigation.to', '/#/exercise-special', {
					title: '查看专项训练',
					query: {
						exerciseId: row?.id
					},
					extendData: {
						style: 'width: 60%; height: 90%; margin: 2% auto 0;'
					},
					target: '_popup'
				});
			},
			onMockExam(row) {
				PaasPostMessage.post('navigation.to', '/#/exercise-mock-exam', {
					title: '查看模拟考试记录',
					query: {
						exerciseId: row?.id
					},
					extendData: {
						style: 'width: 680px; height: 90%; margin: 2% auto 0;'
					},
					target: '_popup'
				});
			},
			async clearAll() {
				await confirmMessageBox('确认清空所有作业吗？');

				await ClearHomeworkStore.request({ courseId: query.courseScheduleId }).getData();
				methods.onRefresh();
				MUtils.toast('清空成功', MESSAGE_TYPE.success);
			},
			onCreate() {
				components.editRef.value.open({ courseScheduleId: query.courseScheduleId }, query.courseScheduleId);
			},
			onEdit(row: ItemResponse) {
				components.editRef.value.open(row, query.courseScheduleId);
			},
			async onDel(row: ItemResponse) {
				await confirmMessageBox('确认删除吗？');

				await DelStore.request({ id: row.id }).getData();
				methods.onRefresh();
				MUtils.toast('删除成功', MESSAGE_TYPE.success);
			},
			onRefresh() {
				controller.tableRequest();
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			controller
		};
	}
});
</script>
