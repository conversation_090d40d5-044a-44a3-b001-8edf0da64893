import { getMapfromArray, getStorefromArray } from '@/shard/utils';

export const TYPE_OPTIONS = [
	{
		label: '模拟考试',
		value: 1
	},
	{
		label: '专项训练',
		value: 2
	},
	{
		label: '错题本',
		value: 3
	},
	{
		label: '模考成绩',
		value: 4
	}
];
export const TYPE_MAP = getMapfromArray(TYPE_OPTIONS);
export const TYPE_STORE = getStorefromArray(TYPE_OPTIONS);

export const STATUS_OPTIONS = [
	{
		label: '未完成',
		value: 1
	},
	{
		label: '已完成',
		value: 2
	}
];
export const STATUS_MAP = getMapfromArray(STATUS_OPTIONS);
export const STATUS_STORE = getStorefromArray(STATUS_OPTIONS);

export const SCOPE_TYPE_OPTIONS = [
	{
		label: '入学后',
		value: 1
	},
	{
		label: '本节课后',
		value: 2
	}
];
export const SCOPE_TYPE_MAP = getMapfromArray(SCOPE_TYPE_OPTIONS);
