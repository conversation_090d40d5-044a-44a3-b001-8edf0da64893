import { List, Delete, Create, Update, TagList, SpecialList, ClearHomework } from '@/store/course-exercise';
import { ItemResponse } from './types';

export const ListStore = new List<Array<ItemResponse>>({});
export const DelStore = new Delete({});
export const CreateStore = new Create({});
export const UpdateStore = new Update({});

export const TagListStore = new TagList<Array<ItemResponse>>({});
export const SpecialListStore = new SpecialList<Array<ItemResponse>>({});

export const ClearHomeworkStore = new ClearHomework({});
