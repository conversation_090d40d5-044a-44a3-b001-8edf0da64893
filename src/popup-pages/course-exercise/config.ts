import { ColumnXtype, TableColumn, TableDateFormat } from 'admin-library';
import { ColumnWidthEnum } from '@/shard/constant';
import { TYPE_MAP, STATUS_MAP } from './constant';

const _COLUMNS: TableColumn[] = [
	{
		title: '类型',
		dataIndex: 'type',
		render: data => {
			return TYPE_MAP[data];
		}
	},
	{
		title: '属性（次数/专项名称）',
		dataIndex: 'pot',
		xtype: ColumnXtype.CUSTOM,
		width: 200
	},
	{
		title: '状态',
		dataIndex: 'status',
		render: (data, lineData) => {
			if (lineData.type === 3) {
				return '--';
			}
			return STATUS_MAP[data];
		},
		width: ColumnWidthEnum.TEXT3
	},
	{
		title: '创建人',
		dataIndex: 'createUserName',
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '创建时间',
		dataIndex: 'createTime',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	}
];
export const COLUMNS: TableColumn[] = [
	{
		title: '#',
		dataIndex: 'id'
	},
	..._COLUMNS
];

export const COLUMNS2: TableColumn[] = [
	{
		title: '#',
		dataIndex: 'id'
	},
	{
		title: '课程主题',
		dataIndex: 'subject',
		width: ColumnWidthEnum.TEXT4
	},
	..._COLUMNS
];
