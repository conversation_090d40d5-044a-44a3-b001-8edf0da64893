import { ColumnXtype, TableColumn, TableDateFormat } from 'admin-library';
import { formatTimeStr } from '@/shard/utils';
import { ColumnWidthEnum, CAR_TYPE_MAP, KEMU_MAP, SCENE_MAP } from '@/shard/constant';

export const COLUMNS: TableColumn[] = [
	{
		title: '模考时间',
		dataIndex: 'examTime',
		type: 'date',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	},
	{
		title: '科目',
		dataIndex: 'kemu',
		render: data => {
			return KEMU_MAP[data];
		},
		width: ColumnWidthEnum.TEXT2
	},
	{
		title: '车型',
		dataIndex: 'carType',
		render: data => {
			return CAR_TYPE_MAP[data];
		},
		width: ColumnWidthEnum.TEXT5
	},
	{
		title: '用时(秒)',
		dataIndex: 'period',
		render: data => {
			if (data) {
				return formatTimeStr(data, 'mm:ss');
			}
		},
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '场景',
		dataIndex: 'scence',
		render: data => {
			return SCENE_MAP[data];
		},
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '得分',
		dataIndex: 'score',
		width: ColumnWidthEnum.TEXT2
	},
	{
		title: '未答',
		dataIndex: 'noAnswerCount',
		width: ColumnWidthEnum.TEXT2
	},
	{
		title: '答对',
		dataIndex: 'rightCount',
		width: ColumnWidthEnum.TEXT2
	},
	{
		title: '答错',
		dataIndex: 'errorCount',
		xtype: ColumnXtype.CUSTOM,
		width: ColumnWidthEnum.TEXT2
	}
];
