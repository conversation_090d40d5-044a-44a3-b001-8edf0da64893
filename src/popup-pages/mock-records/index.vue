<template>
	<div style="padding-left: 20px"></div>
	<div class="pm-form-search" style="padding: 20px 15px 15px">
		<m-row class="ant-row-top">
			<m-col :span="3" style="padding-right: 4px; padding-left: 4px">
				<m-select
					style="width: 100%"
					v-model:value="searchParams.carType"
					:options="options.carList"
				></m-select>
			</m-col>
			<m-col :span="3" style="padding-right: 4px; padding-left: 4px">
				<m-select style="width: 100%" v-model:value="searchParams.kemu" :options="options.kemuList"></m-select>
			</m-col>
			<m-col :span="3" style="padding-right: 4px; padding-left: 4px">
				<m-select
					style="width: 100%"
					v-model:value="searchParams.sceneCode"
					:options="options.secenList"
					placeholder="场景"
					:allow-clear="true"
				></m-select>
			</m-col>
			<m-col :span="4" style="padding-right: 4px; padding-left: 4px">
				<m-select
					style="width: 100%"
					v-model:value="searchParams.finishExam"
					:options="options.finishExamList"
					placeholder="全部模考记录"
					:allow-clear="true"
				></m-select>
			</m-col>
			<m-col :span="2" class="search-btn-col">
				<div class="search-btn-container">
					<m-button type="primary" @click="onSearch">
						<loading-outlined v-if="loading" />
						<search-outlined v-else />
						查询
					</m-button>
					<m-button style="padding-right: 8px; padding-left: 8px; margin-left: 8px" @click="onReset">
						<undo-outlined />
					</m-button>
				</div>
			</m-col>
		</m-row>
	</div>
	<div class="pm-table-btns ml-15" style="padding-left: 4px; margin-bottom: 0">
		<div>
			<m-radio-group v-model:value="showType" button-style="solid" @change="onTypeChange">
				<m-radio-button value="table">模考记录</m-radio-button>
				<m-radio-button value="chart">模考趋势分析</m-radio-button>
			</m-radio-group>
		</div>
		<div class="pm-table-buttons"></div>
	</div>
	<pm-effi :controller="controller" v-show="showType === 'table'">
		<pm-table
			:columns="COLUMNS"
			:use-custom-column="true"
			:operations-fixed="true"
			:operations-width="80"
			:sort-num="false"
			:scroll="{
				x: 'max-content'
			}"
		>
			<template #errorCount="{ record }">
				<m-button type="link" @click="goAllQuestionPage(record)">
					{{ record.errorCount }}
				</m-button>
			</template>
			<template #operations="{ record }">
				<m-button type="link" @click="goQuestionPage(record)">错题分布</m-button>
			</template>
		</pm-table>
	</pm-effi>
	<div ref="chartRef" style="width: 800px; height: 400px" v-show="showType === 'chart'"></div>
</template>

<script lang="ts">
import Dayjs from 'dayjs';
import { defineComponent, reactive, toRefs, ref, markRaw } from 'vue';
import { useRoute } from 'vue-router';
import { ModelController, PaasPostMessage } from 'admin-library';
import { LoadingOutlined, SearchOutlined, UndoOutlined } from '@ant-design/icons-vue';
import { uniqBy } from 'lodash';
import * as echarts from 'echarts';

import { COLUMNS } from './config';
import { KEMU_OPTIONS, CAR_TYPE_OPTIONS, SCENE_OPTIONS } from '@/shard/constant';
import { FINISH_OPTIONS } from './constant';
import { MockRecordStore } from './store';
import { ItemResponse } from './types';

export default defineComponent({
	components: { LoadingOutlined, SearchOutlined, UndoOutlined },
	setup() {
		const route = useRoute();
		const query = route.query;

		let myChart;

		const components = {
			chartRef: ref(null)
		};

		const constants = {
			COLUMNS
		};

		const state = reactive({
			showType: 'table',
			loading: false,
			searchParams: {
				carType: 'car',
				kemu: 1,
				sceneCode: null,
				finishExam: null
			},
			options: {
				kemuList: KEMU_OPTIONS,
				carList: CAR_TYPE_OPTIONS,
				secenList: SCENE_OPTIONS,
				finishExamList: FINISH_OPTIONS
			}
		});

		const controller = new ModelController({
			table: {
				store: MockRecordStore
			}
		});
		controller.tableRequest();

		controller.table.onRequest.use(params => {
			params = {
				...params,
				...state.searchParams,
				mucangId: query.mucangId
			};

			return params;
		});

		const methods = {
			onSearch() {
				controller.tableRequest();
				methods.onTypeChange();
			},
			onReset() {
				state.searchParams = {
					carType: 'car',
					kemu: 1,
					sceneCode: null,
					finishExam: null
				};
				methods.onSearch();
			},
			onTypeChange() {
				if (state.showType === 'chart') {
					methods.renderCharts();
				}
			},
			goQuestionPage(row: ItemResponse) {
				PaasPostMessage.post('navigation.to', '/#/wrong-questions', {
					title: '错题专项分布',
					query: {
						mucangId: query.mucangId,
						uniqueId: row.uniqueId
					},
					extendData: {
						style: 'width: 80%; height: 600px; margin: 140px auto 0;'
					},
					target: '_popup'
				});
			},
			goAllQuestionPage(row: ItemResponse) {
				PaasPostMessage.post('navigation.to', '/#/wrong-questions', {
					title: '全部错题',
					query: {
						errorqIdList: row.errorqIdList.join(',')
					},
					extendData: {
						style: 'width: 80%; height: 600px; margin: 140px auto 0;'
					},
					target: '_popup'
				});
			},

			async renderCharts() {
				let { createTime, from } = query as {
					createTime?: string;
					from?: string;
				};
				let res = await MockRecordStore.request({
					...state.searchParams,
					mucangId: query.mucangId,
					limit: 9999
				}).getData();
				res = res.reverse();
				res = uniqBy(res, 'examTime');
				const showTickList = (function (len) {
					let interval = Math.ceil(len / 8);
					let list = [];
					for (let i = 0; i < len; i++) {
						if (i % interval === 0) {
							list.push(i);
						}
					}
					return list;
				})(res.length);
				let createTimePoint;
				if (createTime) {
					let index = res.findIndex(item => {
						return item.examTime > createTime;
					});
					if (index === -1) {
						index = res.length - 1;
					}
					let gap;
					if (index === 0) {
						gap = 10;
					} else {
						gap = Math.ceil(640 / res.length / 2);
						gap = Math.max(4, gap);
					}
					createTimePoint = [
						{
							coord: [index, 0],
							symbolOffset: [-gap, 4]
						}
					];
				}
				const data = res.map(item => {
					return [Dayjs(item.examTime).format('YYYY-MM-DD HH:mm:ss'), item.score];
				});
				myChart && myChart.dispose();
				let chartDom = components.chartRef.value;
				myChart = markRaw(echarts.init(chartDom));
				let option = {
					tooltip: {
						trigger: 'axis'
						// formatter: function (params) {
						// 	let { componentType, name, value } = params;
						// 	if (componentType === 'markPoint') {
						// 		return `企微添加时间：<br> ${name}`;
						// 	}
						// 	return `${value[1]}分<br> ${value[0]}`;
						// }
					},
					xAxis: {
						type: 'category',
						boundaryGap: false,
						axisLabel: {
							showMinLabel: true,
							interval: function (idx) {
								return showTickList.findIndex(item => item === idx) > -1;
							},
							formatter: function (value, idx) {
								let showYear = false;
								const index = showTickList.findIndex(item => item === idx);
								let date = Dayjs(value);
								if (index === 0) {
									showYear = true;
								} else {
									const prevIndex = showTickList[index - 1];
									const prevDate = Dayjs(data[prevIndex][0]);
									showYear = prevDate.format('YYYY') !== date.format('YYYY');
								}
								return showYear ? date.format('YYYY-MM-DD') : date.format('MM-DD');
							}
						}
					},
					yAxis: {
						type: 'value'
					},
					series: [
						{
							markPoint: {
								animation: false,
								silent: true,
								symbol: 'circle',
								symbolSize: 8,
								label: {
									show: true,
									backgroundColor: '#ffb5b5',
									formatter: [
										`{a|${from}创建时间}`,
										`{a|${Dayjs(+createTime).format('YYYY-MM-DD HH:mm:ss')}}`
									].join('\n'),
									position: 'bottom',
									offset: [0, 6],
									rich: {
										a: {
											color: '#fff',
											lineHeight: 18
										}
									},
									padding: 4
								},
								itemStyle: {
									color: '#ff0000'
								},
								data: createTimePoint
							},
							markLine: {
								silent: true,
								symbol: 'none',
								label: {
									show: false
								},
								lineStyle: {
									normal: {
										type: 'solid',
										color: '#ff0000'
									}
								},
								data: [
									{
										yAxis: 90
									}
								]
							},
							data: data,
							type: 'line'
						}
					]
				};
				myChart.setOption(option);
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			controller
		};
	}
});
</script>
