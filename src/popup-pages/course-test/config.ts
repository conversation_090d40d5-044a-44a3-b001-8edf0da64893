import { ColumnXtype, TableColumn } from 'admin-library';
import { explodeAnswer } from '@/shard/utils';
import { ColumnWidthEnum } from '@/shard/constant';
import { RESULT_MAP } from './constant';

export const COLUMNS: TableColumn[] = [
	{
		title: '试题Id',
		dataIndex: 'questionId',
		width: 120
	},
	{
		title: '试题题干',
		dataIndex: 'question',
		xtype: ColumnXtype.CUSTOM
	},
	{
		title: '作答状态',
		dataIndex: 'status',
		render: data => {
			return RESULT_MAP[data];
		},
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '作答选项',
		dataIndex: 'userAnswer',
		render: data => {
			const indexMap = { 16: 'A', 32: 'B', 64: 'C', 128: 'D' };
			if (data) {
				return explodeAnswer(data)
					.map(item => indexMap[item])
					.join('，');
			}
		},
		width: ColumnWidthEnum.TEXT4
	}
];
