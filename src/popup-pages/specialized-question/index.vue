<template>
	<m-radio-group v-model:value="studyPlan" button-style="solid" style="padding: 20px 0 0 20px" @change="onRefresh">
		<m-radio-button :value="2">80A</m-radio-button>
		<m-radio-button :value="4">70A</m-radio-button>
		<m-radio-button :value="3">60A</m-radio-button>
		<m-radio-button :value="7">50A</m-radio-button>
		<m-radio-button :value="1">强化方案</m-radio-button>
	</m-radio-group>
	<pm-effi :controller="controller">
		<pm-search>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '知识点Id'
				}"
				data-index="knowledgeId"
				xtype="INPUT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '试题Id'
				}"
				data-index="questionId"
				xtype="INPUT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '题干'
				}"
				data-index="question"
				xtype="INPUT"
			/>
		</pm-search>

		<pm-table
			:columns="COLUMNS"
			:use-custom-column="true"
			:operations-fixed="true"
			:operations-width="120"
			:sort-num="false"
			:scroll="{
				x: 'max-content'
			}"
			:antdProps="{
				rowKey: 'id',
				rowSelection: {
					preserveSelectedRowKeys: true,
					selectedRowKeys: selectedRowKeys,
					onChange: onSelectChange
				}
			}"
		>
			<template #beforeCustom>
				<m-button type="primary" @click="addQeustion">添加</m-button>
				<m-button
					type="primary"
					danger
					:disabled="!selectedRowKeys.length"
					@click="onBatchDelete"
					style="margin-left: 8px;"
				>
					批量删除
				</m-button>
			</template>
			<template #operations="{ record }">
				<m-button danger type="link" @click="onDel(record)">删除</m-button>
			</template>
		</pm-table>
	</pm-effi>

	<edit-question-table-comp ref="editQuestionTableRef" @refresh="onRefresh"></edit-question-table-comp>
</template>

<script lang="ts">
import { defineComponent, reactive, ref, toRefs } from 'vue';
import { ModelController, MUtils, MESSAGE_TYPE } from 'admin-library';
import { useRoute } from 'vue-router';

import EditQuestionTableComp from './comps/edit-question-table-dialog/index.vue';
import { confirmMessageBox } from '@/shard/utils';
import { COLUMNS } from './config';
import { SpeQuestionListStore, RemoveQuestionQuesStore, BatchRemoveQuestionStore } from './store';
import { ItemResponse } from './types';

export default defineComponent({
	components: {
		EditQuestionTableComp
	},
	setup() {
		const route = useRoute();
		const query = route.query;

		const components = {
			editQuestionTableRef: ref<InstanceType<typeof EditQuestionTableComp>>()
		};

		const constants = {
			COLUMNS
		};

		const state = reactive({
			studyPlan: 2,
			selectedRowKeys: []
		});

		const controller = new ModelController({
			table: {
				store: SpeQuestionListStore
			},
			search: {}
		});
		controller.tableRequest();

		controller.table.onRequest.use(params => {
			params = {
				...params,
				specializedId: query.specializedId,
				studyPlan: state.studyPlan
			};

			return params;
		});

		const methods = {
			onSelectChange(keys) {
				state.selectedRowKeys = keys;
			},
			addQeustion() {
				components.editQuestionTableRef.value.open({
					specializedId: query.specializedId,
					studyPlan: state.studyPlan
				});
			},
			async onDel(row: ItemResponse) {
				await confirmMessageBox('确认删除吗？');

				await RemoveQuestionQuesStore.request({ id: row.id }).getData();
				methods.onRefresh();
				MUtils.toast('删除成功', MESSAGE_TYPE.success);
			},
			async onBatchDelete() {
				if (!state.selectedRowKeys.length) {
					MUtils.toast('请先选择要删除的题目', MESSAGE_TYPE.warning);
					return;
				}

				await confirmMessageBox(`确认删除选中的 ${state.selectedRowKeys.length} 道题目吗？`);

				await BatchRemoveQuestionStore.request({
					ids: state.selectedRowKeys.join(',')
				}).getData();

				state.selectedRowKeys = [];
				methods.onRefresh();
				MUtils.toast('批量删除成功', MESSAGE_TYPE.success);
			},

			onRefresh() {
				controller.tableRequest();
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			controller
		};
	}
});
</script>
