import { TableColumn } from 'admin-library';
import { ColumnWidthEnum } from '@/shard/constant';

export const COLUMNS: TableColumn[] = [
	{
		title: '试题Id',
		dataIndex: 'questionId',
		width: 120,
		fixed: 'left'
	},
	{
		title: '试题内容',
		dataIndex: 'question',
		width: 300,
		fixed: 'left'
	},
	{
		title: '章节编号',
		dataIndex: 'chapterIdList',
		render: data => {
			return data.join(',');
		},
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '标签',
		dataIndex: 'tagNameList',
		render: data => {
			return data?.filter(item => !!item).join('、');
		}
	}
];
