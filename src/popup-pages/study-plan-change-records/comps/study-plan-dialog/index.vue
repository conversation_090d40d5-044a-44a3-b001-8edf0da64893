<template>
	<pm-dialog v-model:visible="visible" :title="title" width="680px">
		<template v-if="visible">
			<m-form
				ref="formRef"
				:model="formState"
				:colon="false"
				:label-col="{ style: { width: '95px' } }"
				autocomplete="off"
			>
				<m-form-item label="学习方案" name="studyPlan" required>
					<m-select v-model:value="formState.studyPlan" :options="options.planList" :allowClear="true" />
				</m-form-item>
				<m-form-item label="修改原因" name="remark" required>
					<m-textarea
						v-model:value="formState.remark"
						:rows="3"
						:maxlength="500"
						show-count
						placeholder="请录入教案更换原因，500字以内。"
					/>
				</m-form-item>
			</m-form>
		</template>

		<template #footer>
			<m-button plain @click="visible = false">取消</m-button>
			<m-button :loading="loading" type="primary" @click="onConfirm">确定</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { MESSAGE_TYPE, MUtils } from 'admin-library';

import { cloneFromPick, cloneByMerge } from '@/shard/utils';
import { STUDY_PLAN_OPTIONS, K4_STUDY_PLAN_OPTIONS } from '@/application/student/constant';
import { ChangeStudyPlanStore } from '../../store';

const DefaultFormState = {
	id: null,
	studyPlan: '',
	remark: ''
};

export default defineComponent({
	emits: ['refresh'],
	setup(props, { emit }) {
		const components = {
			formRef: ref(null)
		};

		const constants = {};

		const state = reactive({
			visible: false,
			title: '修改学习方案',
			loading: false,
			formState: MUtils.deepClone(DefaultFormState),
			options: {
				planList: STUDY_PLAN_OPTIONS
			}
		});

		const methods = {
			open(row: any, tutorKemu) {
				state.formState = cloneFromPick(row, DefaultFormState);

				if (Number(tutorKemu) === 20) {
					state.options.planList = K4_STUDY_PLAN_OPTIONS;
				} else {
					state.options.planList = STUDY_PLAN_OPTIONS;
				}

				state.visible = true;
			},

			async onConfirm() {
				await components.formRef.value.validate();

				try {
					state.loading = true;
					const params = cloneByMerge(state.formState, DefaultFormState);
					const fetchStore = ChangeStudyPlanStore;

					await fetchStore.request(params).getData();

					emit('refresh');
					state.visible = false;
					MUtils.toast('保存成功', MESSAGE_TYPE.success);
				} finally {
					state.loading = false;
				}
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods
		};
	}
});
</script>
