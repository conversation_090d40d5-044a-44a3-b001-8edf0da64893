import { TableColumn, TableDateFormat } from 'admin-library';
import { ColumnWidthEnum } from '@/shard/constant';
import { STUDY_PLAN_MAP } from '@/application/student/constant';

export const COLUMNS: TableColumn[] = [
	{
		title: '学习方案',
		dataIndex: 'studyPlan',
		render: data => {
			return STUDY_PLAN_MAP[data];
		}
	},
	{
		title: '创建人',
		dataIndex: 'createUserName',
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '创建时间',
		dataIndex: 'createTime',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	},
	{
		title: '修改原因',
		dataIndex: 'remark'
	}
];
