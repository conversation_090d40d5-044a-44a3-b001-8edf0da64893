<template>
	<pm-effi :controller="controller">
		<pm-table
			:columns="COLUMNS"
			:use-custom-column="true"
			:pagination="false"
			:operations="false"
			:sort-num="false"
			:scroll="{
				x: 'max-content'
			}"
		>
			<template #beforeCustom>
				<m-button type="primary" @click="changeStudyPlan">更换学习方案</m-button>
			</template>
		</pm-table>
	</pm-effi>

	<study-plan-comp ref="studyPlanRef" @refresh="onRefresh"></study-plan-comp>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { ModelController } from 'admin-library';
import { useRoute } from 'vue-router';

import StudyPlanComp from './comps/study-plan-dialog/index.vue';
import { COLUMNS } from './config';
import { ListStore } from './store';

export default defineComponent({
	components: {
		StudyPlanComp
	},
	setup() {
		const route = useRoute();
		const query = route.query;

		const components = {
			studyPlanRef: ref<InstanceType<typeof StudyPlanComp>>()
		};

		const constants = {
			COLUMNS
		};

		const state = reactive({});

		const controller = new ModelController({
			table: {
				store: ListStore
			}
		});
		controller.tableRequest();

		controller.table.onRequest.use(params => {
			params = {
				...params,
				id: query.studentProfileId
			};

			return params;
		});

		const methods = {
			changeStudyPlan() {
				components.studyPlanRef.value.open({ id: query.studentProfileId }, query.tutorKemu);
			},

			onRefresh() {
				controller.tableRequest();
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			controller
		};
	}
});
</script>
