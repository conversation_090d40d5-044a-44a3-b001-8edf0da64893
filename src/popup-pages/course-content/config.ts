import { ColumnXtype, TableColumn, TableDateFormat } from 'admin-library';
import { ColumnWidthEnum } from '@/shard/constant';
import { TYPE_MAP } from './constant';

export const COLUMNS: TableColumn[] = [
	{
		title: '#',
		dataIndex: 'id'
	},
	{
		title: '类型',
		dataIndex: 'type',
		render: data => {
			return TYPE_MAP[data];
		},
		width: ColumnWidthEnum.TEXT5
	},
	{
		title: '专项/课件名称',
		dataIndex: 'content',
		render: data => {
			return data?.name;
		},
		width: 180
	},
	{
		title: '范围&推荐专项',
		dataIndex: 'group',
		xtype: ColumnXtype.CUSTOM
	},
	{
		title: '创建人',
		dataIndex: 'createUserName',
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '创建时间',
		dataIndex: 'createTime',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	},
	{
		title: '最后更新人',
		dataIndex: 'updateUserName',
		width: ColumnWidthEnum.TEXT5
	},
	{
		title: '最后更新时间',
		dataIndex: 'updateTime',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	}
];
