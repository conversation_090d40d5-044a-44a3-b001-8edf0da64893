import { getMapfromArray, getStorefromArray } from '@/shard/utils';

export const TYPE_OPTIONS = [
	{
		label: '讲错题',
		value: 1
	},
	{
		label: '专项讲解',
		value: 2
	},
	{
		label: '临考冲刺',
		value: 3
	}
];
export const TYPE_MAP = getMapfromArray(TYPE_OPTIONS);
export const TYPE_STORE = getStorefromArray(TYPE_OPTIONS);

export const SCOPE_OPTIONS = [
	{
		label: '最近三次考试',
		value: 3
	},
	{
		label: '最近十次考试',
		value: 10
	},
	{
		label: '全部错题',
		value: -1
	}
];
export const SCOPE_MAP = getMapfromArray(SCOPE_OPTIONS);
export const SCOPE_STORE = getStorefromArray(SCOPE_OPTIONS);
