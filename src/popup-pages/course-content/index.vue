<template>
	<pm-effi :controller="controller">
		<pm-table
			:columns="COLUMNS"
			:use-custom-column="true"
			:pagination="false"
			:operations-fixed="true"
			:operations-width="120"
			:sort-num="false"
		>
			<template #beforeCustom>
				<m-button type="primary" @click="onCreate">添加</m-button>
				<m-button type="primary" danger @click="onResetPlan">重置教案</m-button>
			</template>
			<template #group="{ record }">
				<div v-if="record.wrongScope">范围：{{ SCOPE_MAP[record.wrongScope] }}</div>
				<div v-if="record?.wrongItems?.length">
					推荐专项：{{ record.wrongItems?.map(item => item.name).join('，') }}
				</div>
				<div v-if="record?.questionIds?.length">
					题目列表：
					<m-button type="link" @click="goAllQuestionPage(record)">查看</m-button>
				</div>
			</template>
			<template #operations="{ record }">
				<m-button type="link" @click="onEdit(record)">编辑</m-button>
				<m-button danger type="link" @click="onDel(record)">删除</m-button>
			</template>
		</pm-table>
	</pm-effi>

	<edit-comp ref="editRef" @refresh="onRefresh"></edit-comp>
	<edit-course-table-comp ref="editCourseTableRef" @refresh="onRefresh"></edit-course-table-comp>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { ModelController, MUtils, MESSAGE_TYPE, PaasPostMessage } from 'admin-library';
import { useRoute } from 'vue-router';

import EditCourseTableComp from '@/application/settlement/comps/edit-course-table-dialog/index.vue';
import EditComp from './comps/edit-dialog/index.vue';
import { confirmMessageBox } from '@/shard/utils';
import { COLUMNS } from './config';
import { SCOPE_MAP } from './constant';
import { ListStore, DelStore, ResetPlanStore } from './store';
import { ItemResponse } from './types';

export default defineComponent({
	components: {
		EditComp,
		EditCourseTableComp
	},
	setup() {
		const route = useRoute();
		const query = route.query;

		const components = {
			editRef: ref<InstanceType<typeof EditComp>>(),
			editCourseTableRef: ref<InstanceType<typeof EditCourseTableComp>>()
		};

		const constants = {
			COLUMNS,
			SCOPE_MAP
		};

		const state = reactive({});

		const controller = new ModelController({
			table: {
				store: ListStore
			}
		});
		controller.tableRequest();

		controller.table.onRequest.use(params => {
			params = {
				...params,
				courseId: query.courseId
			};

			return params;
		});

		const methods = {
			onCreate() {
				components.editRef.value.open({ courseId: query.courseId }, query.kemu, query.courseId);
			},
			onEdit(row: ItemResponse) {
				components.editRef.value.open(
					{
						...row,
						bizId: row?.content?.id,
						bizIds: row?.wrongItems?.map(item => item.id)
					},
					query.kemu,
					query.courseId
				);
			},
			async onDel(row: ItemResponse) {
				await confirmMessageBox('确认删除吗？');

				await DelStore.request({ id: row.id }).getData();
				methods.onRefresh();
				MUtils.toast('删除成功', MESSAGE_TYPE.success);
			},
			goAllQuestionPage(row: ItemResponse) {
				PaasPostMessage.post('navigation.to', '/#/wrong-questions', {
					title: '错题列表',
					query: {
						errorqIdList: row.questionIds.join(',')
					},
					extendData: {
						style: 'width: 80%; height: 600px; margin: 140px auto 0;'
					},
					target: '_popup'
				});
			},
			async onResetPlan() {
				await confirmMessageBox('确认重置吗？');

				await ResetPlanStore.request({ courseId: query.courseId }).getData();
				methods.onRefresh();
				MUtils.toast('重置成功', MESSAGE_TYPE.success);
			},
			onRefresh() {
				controller.tableRequest();
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			controller
		};
	}
});
</script>
