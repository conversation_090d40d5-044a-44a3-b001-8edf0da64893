<template>
	<pm-dialog v-model:visible="visible" :title="title" width="680px">
		<template v-if="visible">
			<m-form
				ref="formRef"
				:model="formState"
				:colon="false"
				:label-col="{ style: { width: '95px' } }"
				autocomplete="off"
			>
				<m-form-item label="类型" name="type" required>
					<m-select
						:disabled="formState.id"
						@change="change"
						:options="options.typeList"
						v-model:value="formState.type"
						:allowClear="true"
						placeholder="类型"
					/>
				</m-form-item>
				<m-form-item label="范围" name="wrongScope" v-if="formState.type === 1" required>
					<m-select
						:options="options.scopeList"
						v-model:value="formState.wrongScope"
						:allowClear="true"
						placeholder="类型"
					/>
				</m-form-item>
				<m-form-item label="推荐专项" name="bizIds" v-if="formState.type === 1" required :rules="bizIds">
					<m-select
						mode="multiple"
						v-model:value="formState.bizIds"
						:options="options.speList"
						:fieldNames="{ label: 'name', value: 'id' }"
						placeholder="最多支持推荐3个专项"
						:max-count="3"
					/>
				</m-form-item>
				<m-form-item label="专项名称" name="bizId" v-else-if="formState.type === 2" required>
					<m-select
						v-model:value="formState.bizId"
						:options="options.speList"
						:fieldNames="{ label: 'name', value: 'id' }"
						placeholder="专项名称"
					/>
				</m-form-item>
				<m-form-item label="课件名称" name="bizId" v-else-if="formState.type === 3" required>
					<m-select
						v-model:value="formState.bizId"
						:options="options.coursewareList"
						:fieldNames="{ label: 'name', value: 'id' }"
						placeholder="课件名称"
					/>
				</m-form-item>
			</m-form>
		</template>

		<template #footer>
			<m-button plain @click="visible = false">取消</m-button>
			<m-button :loading="loading" type="primary" @click="onConfirm">确定</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { MESSAGE_TYPE, MUtils } from 'admin-library';

import { cloneFromPick, cloneByMerge } from '@/shard/utils';
import { TYPE_OPTIONS, SCOPE_OPTIONS } from '../../constant';
import { SpecialListStore } from '@/popup-pages/course-exercise/store';
import { ListStore as CoursewareListStore } from '@/application/courseware/store';
import { CreateStore, UpdateStore } from '../../store';

const DefaultFormState = {
	courseId: null,
	id: null,
	type: '',
	wrongScope: '',
	bizId: '',
	bizIds: [],
	specialSource: 1
};

export default defineComponent({
	emits: ['refresh'],
	setup(props, { emit }) {
		const components = {
			formRef: ref(null)
		};

		const constants = {
			bizIds: {
				validator(rule, value) {
					if (value?.length > 3) {
						return Promise.reject('最多输入3项');
					}
					return Promise.resolve();
				}
			}
		};

		const state = reactive({
			visible: false,
			title: '',
			loading: false,
			formState: MUtils.deepClone(DefaultFormState),
			options: {
				typeList: TYPE_OPTIONS,
				scopeList: SCOPE_OPTIONS,
				speList: [],
				coursewareList: []
			}
		});

		const methods = {
			open(row: any, kemu, courseId) {
				state.formState = MUtils.deepClone(DefaultFormState);
				state.title = row?.id ? '编辑' : '新增';

				if (row) {
					state.formState = cloneFromPick(row, DefaultFormState);
				}

				state.visible = true;

				methods.initSpeOptions(courseId);
				methods.initCoursewareOptions(kemu);
			},
			change() {
				state.formState.bizId = '';
			},
			async initSpeOptions(courseId) {
				let res = await SpecialListStore.request({
					limit: 9999,
					courseId
				}).getData();
				if (res) {
					state.options.speList = res;
					state.formState.specialSource = res[0].specialSource;
				}
			},
			async initCoursewareOptions(kemu) {
				let res = await CoursewareListStore.request({
					limit: 9999,
					type: 2,
					kemu
				}).getData();
				if (res) {
					state.options.coursewareList = res;
				}
			},

			async onConfirm() {
				await components.formRef.value.validate();

				try {
					state.loading = true;
					const params = cloneByMerge(state.formState, DefaultFormState);
					const fetchStore = state.formState.id ? UpdateStore : CreateStore;

					await fetchStore.request(params).getData();

					emit('refresh');
					state.visible = false;
					MUtils.toast('保存成功', MESSAGE_TYPE.success);
				} finally {
					state.loading = false;
				}
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods
		};
	}
});
</script>
