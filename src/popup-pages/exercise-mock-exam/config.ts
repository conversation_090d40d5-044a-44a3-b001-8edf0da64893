import { TableColumn, TableDateFormat } from 'admin-library';
import { formatTimeStr } from '@/shard/utils';
import { ColumnWidthEnum } from '@/shard/constant';

export const COLUMNS: TableColumn[] = [
	{
		title: '模考结束时间',
		dataIndex: 'examTime',
		type: 'date',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	},
	{
		title: '耗时(秒)',
		dataIndex: 'period',
		render: data => {
			if (data) {
				return formatTimeStr(data, 'mm:ss');
			}
		},
		width: ColumnWidthEnum.TEXT5
	},
	{
		title: '分数',
		dataIndex: 'score'
	}
];
