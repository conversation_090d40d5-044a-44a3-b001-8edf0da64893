<template>
	<pm-effi :controller="controller">
		<pm-table
			:columns="COLUMNS"
			:use-custom-column="true"
			:pagination="false"
			:operations-fixed="true"
			:operations-width="80"
			:sort-num="false"
			:scroll="{
				x: 'max-content'
			}"
		>
			<template #operations="{ record }">
				<m-button
					type="link"
					v-if="record.errorqIdList && record.errorqIdList.length"
					@click="goAllQuestionPage(record)"
				>
					查看错题
				</m-button>
			</template>
		</pm-table>
	</pm-effi>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue';
import { useRoute } from 'vue-router';
import { ModelController, PaasPostMessage } from 'admin-library';

import { COLUMNS } from './config';
import { MockListStore } from './store';
import { ItemResponse } from './types';

export default defineComponent({
	setup() {
		const route = useRoute();
		const query = route.query;

		const components = {};

		const constants = {
			COLUMNS
		};

		const state = reactive({});

		const controller = new ModelController({
			table: {
				store: MockListStore
			}
		});
		controller.tableRequest();

		controller.table.onRequest.use(params => {
			params = {
				...params,
				homeworkId: query.exerciseId
			};

			return params;
		});

		const methods = {
			goAllQuestionPage(row: ItemResponse) {
				PaasPostMessage.post('navigation.to', '/#/wrong-questions', {
					title: '查看错题',
					query: {
						errorqIdList: row.errorqIdList.join(',')
					},
					extendData: {
						style: 'width: 50%; height: 480px; margin: 140px auto 0;'
					},
					target: '_popup'
				});
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			controller
		};
	}
});
</script>

<style lang="less" scoped></style>
