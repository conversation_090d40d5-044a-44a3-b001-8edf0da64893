import { TableColumn, TableDateFormat } from 'admin-library';
import { ColumnWidthEnum } from '@/shard/constant';
import { renderTextWithColor } from '@/shard/utils';
import { RADIO_OPTIONS, ORIGIN_TYPE_MAP } from './constant';

export const COLUMNS: TableColumn[] = [
	{
		title: '#',
		dataIndex: 'id'
	},
	{
		title: '讲师',
		dataIndex: 'lectureMixName'
	},
	{
		title: '操作类型',
		dataIndex: 'blacked',
		render: data => {
			return renderTextWithColor(data, RADIO_OPTIONS, { isTag: true });
		},
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '发起人类型',
		dataIndex: 'blackType',
		render: data => {
			return ORIGIN_TYPE_MAP[data];
		},
		width: ColumnWidthEnum.TEXT5
	},
	{
		title: '原因',
		dataIndex: 'reason'
	},
	{
		title: '发起人',
		dataIndex: 'blackUserName',
		render: (data, lineData) => {
			if (lineData.blackType === 2) {
				return data;
			}
		},
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '发起时间',
		dataIndex: 'blackTime',
		type: 'date',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	}
];
