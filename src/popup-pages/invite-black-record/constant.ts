import { getMapfromArray, getStorefromArray } from '@/shard/utils';
import { ColorEnum } from '@/shard/constant';

export const BLACK_TYPE_OPTIONS = [
	{
		label: '3次拒绝邀约',
		value: 1
	},
	{
		label: '后台操作',
		value: 2
	}
];
export const BLACK_TYPE_MAP = getMapfromArray(BLACK_TYPE_OPTIONS);
export const BLACK_TYPE_STORE = getStorefromArray(BLACK_TYPE_OPTIONS);

export const RADIO_OPTIONS = [
	{
		label: '解除拉黑',
		value: false,
		styleclass: ColorEnum.info
	},
	{
		label: '拉黑',
		value: true,
		styleclass: ColorEnum.warning
	}
];
export const RADIO_MAP = getMapfromArray(RADIO_OPTIONS);
export const RADIO_STORE = getStorefromArray(RADIO_OPTIONS);

export const ORIGIN_TYPE_OPTIONS = [
	{
		label: '系统',
		value: 1
	},
	{
		label: '管理后台',
		value: 2
	}
];
export const ORIGIN_TYPE_MAP = getMapfromArray(ORIGIN_TYPE_OPTIONS);
