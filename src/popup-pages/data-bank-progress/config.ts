import { TableColumn, TableDateFormat } from 'admin-library';
import { ColumnWidthEnum, RADIO_MAP } from '@/shard/constant';
import { TYPE_MAP, READ_STATUS_MAP } from '@/application/teacher-data-bank/constant';

export const COLUMNS: TableColumn[] = [
	{
		title: '资料ID',
		dataIndex: 'id',
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '分组',
		dataIndex: 'groupName',
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '文件名称',
		dataIndex: 'name',
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '文件类型',
		dataIndex: 'type',
		width: ColumnWidthEnum.TEXT4,
		render: data => {
			return TYPE_MAP[data];
		}
	},
	{
		title: '是否必看',
		dataIndex: 'mustSee',
		width: ColumnWidthEnum.TEXT4,
		render: data => {
			return RADIO_MAP[data];
		}
	},
	{
		title: '状态',
		dataIndex: 'status',
		width: ColumnWidthEnum.TEXT4,
		render: data => {
			return READ_STATUS_MAP[data];
		}
	},
	{
		title: '上报时间',
		dataIndex: 'reportTime',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	}
];
