import { TableColumn, TableDateFormat } from 'admin-library';
import { renderTextWithColor, formatDate } from '@/shard/utils';
import { ColumnWidthEnum } from '@/shard/constant';
import { INVITE_TYPE_MAP } from '@/application/course-invite/constant';
import { STATUS_OPTIONS } from '@/popup-pages/course-invite-detail/constant';

export const COLUMNS: TableColumn[] = [
	{
		title: '邀约Id',
		dataIndex: 'inviteId'
	},
	{
		title: '邀约类型',
		dataIndex: 'inviteType',
		render: data => {
			return INVITE_TYPE_MAP[data];
		}
	},
	{
		title: '接受状态',
		dataIndex: 'replyStatus',
		render: data => {
			return renderTextWithColor(data, STATUS_OPTIONS);
		}
	},
	{
		title: '拟上课时间',
		dataIndex: 'courseTime',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	},
	{
		title: '邀约发起人',
		dataIndex: 'startUserName',
		render: (value, lineData) => {
			if (lineData.createOrigin === 0) {
				return '学员';
			} else {
				return lineData.createUserName;
			}
		}
	},
	{
		title: '邀约发起时间',
		dataIndex: 'createTime',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	},
	{
		title: '回应时间',
		dataIndex: 'updateTime',
		render: (data, lineData) => {
			if (lineData.replyStatus === 1 || lineData.replyStatus === 2) {
				return formatDate(data, TableDateFormat.SECONDS);
			}
		},
		width: ColumnWidthEnum.DATESECONDS
	}
];
