<template>
	<pm-effi :controller="controller">
		<pm-search>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '类型'
				}"
				data-index="inviteType"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '接受状态'
				}"
				data-index="replyStatus"
				xtype="SELECT"
			/>
		</pm-search>
		<pm-table :columns="COLUMNS" :use-custom-column="true" :operations="false" :sort-num="false"></pm-table>
	</pm-effi>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue';
import { ModelController } from 'admin-library';
import { useRoute } from 'vue-router';

import { COLUMNS } from './config';
import { INVITE_TYPE_STORE } from '@/application/course-invite/constant';
import { STATUS_STORE } from '@/popup-pages/course-invite-detail/constant';
import { ListStore } from './store';

export default defineComponent({
	setup() {
		const route = useRoute();
		const query = route.query;

		const components = {};

		const constants = {
			COLUMNS
		};

		const state = reactive({});

		const controller = new ModelController({
			table: {
				store: ListStore
			},
			search: {
				inviteType: {
					store: INVITE_TYPE_STORE
				},
				replyStatus: {
					store: STATUS_STORE
				}
			}
		});
		controller.tableRequest();

		controller.table.onRequest.use(params => {
			params = {
				...params,
				lecturerId: query.lecturerId
			};

			return params;
		});

		const methods = {};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			controller
		};
	}
});
</script>
