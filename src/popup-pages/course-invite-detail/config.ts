import { TableColumn, TableDateFormat } from 'admin-library';
import { renderTextWithColor, formatDate } from '@/shard/utils';
import { SKIP_REASON_MAP, STATUS_OPTIONS } from './constant';

export const COLUMNS: TableColumn[] = [
	{
		title: '讲师',
		dataIndex: 'lectureMixName'
	},
	{
		title: '接受状态',
		dataIndex: 'replyStatus',
		render: data => {
			return renderTextWithColor(data, STATUS_OPTIONS);
		}
	},
	{
		title: '跳过原因',
		dataIndex: 'skipReason',
		render: data => {
			return SKIP_REASON_MAP[data]
		}
	},
	{
		title: '发出邀约时间',
		dataIndex: 'createTime',
		dateFormat: TableDateFormat.SECONDS
	},
	{
		title: '回应时间',
		dataIndex: 'updateTime',
		render: (data, lineData) => {
			if (lineData.replyStatus === 1 || lineData.replyStatus === 2) {
				return formatDate(data, TableDateFormat.SECONDS);
			}
		}
	}
];
