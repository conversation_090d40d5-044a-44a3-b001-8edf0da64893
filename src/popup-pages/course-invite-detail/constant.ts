import { getMapfromArray, getStorefromArray } from '@/shard/utils';
import { ColorEnum } from '@/shard/constant';

export const STATUS_OPTIONS = [
	{
		label: '待回应',
		value: 0,
		styleclass: ColorEnum.warning
	},
	{
		label: '接受',
		value: 1,
		styleclass: ColorEnum.success
	},
	{
		label: '拒绝',
		value: 2,
		styleclass: ColorEnum.danger
	},
	{
		label: '未回应',
		value: 10,
		styleclass: ColorEnum.info
	},
	{
		label: '被强制关闭',
		value: 11,
		styleclass: ColorEnum.blue
	},
	{
		label: '已跳过',
		value: 3,
		styleclass: ColorEnum.info
	}
];
export const STATUS_MAP = getMapfromArray(STATUS_OPTIONS);
export const STATUS_STORE = getStorefromArray(STATUS_OPTIONS);

export const SKIP_REASON_OPTIONS = [
	{
		label: '已达每日课程数上限',
		value: 1
	},
	{
		label: '已达带训学员上限',
		value: 2
	}
];
export const SKIP_REASON_MAP = getMapfromArray(SKIP_REASON_OPTIONS);
export const SKIP_REASON_STORE = getStorefromArray(SKIP_REASON_OPTIONS);
