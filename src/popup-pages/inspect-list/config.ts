import { TableColumn, TableDateFormat } from 'admin-library';
import { ColumnWidthEnum } from '@/shard/constant';
import { STATUS_MAP } from '@/application/courses/constant';

export const COLUMNS: TableColumn[] = [
	{
		title: '课程Id',
		dataIndex: 'id'
	},
	{
		title: '课程主题',
		dataIndex: 'subject',
		width: 150
	},
	{
		title: '课程结束时间',
		dataIndex: 'endTime',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.TEXT6
	},
	{
		title: '学员',
		dataIndex: 'studentName',
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '验收结论',
		dataIndex: 'acceptanceStatus',
		render: data => {
			return STATUS_MAP[data];
		},
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '验收备注',
		dataIndex: 'acceptanceNote',
		width: 120
	},
	{
		title: '验收人',
		dataIndex: 'acceptanceUserName',
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '质检备注',
		dataIndex: 'inspectionNote',
		width: 120
	},
	{
		title: '质检人',
		dataIndex: 'inspectionUserName',
		width: ColumnWidthEnum.TEXT4
	}
];
