<template>
	<pm-effi :controller="controller">
		<pm-table
			:columns="COLUMNS"
			:use-custom-column="true"
			:operations="false"
			:sort-num="false"
			:scroll="{
				x: '100%'
			}"
		></pm-table>
	</pm-effi>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue';
import { ModelController } from 'admin-library';
import { useRoute } from 'vue-router';

import { COLUMNS } from './config';
import { ListStore } from './store';

export default defineComponent({
	setup() {
		const route = useRoute();
		const query = route.query;

		const components = {};

		const constants = {
			COLUMNS
		};

		const state = reactive({});

		const controller = new ModelController({
			table: {
				store: ListStore
			}
		});
		controller.tableRequest();

		controller.table.onRequest.use(params => {
			params = {
				...params,
				lecturerId: query.lecturerId
			};

			return params;
		});

		const methods = {};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			controller
		};
	}
});
</script>
