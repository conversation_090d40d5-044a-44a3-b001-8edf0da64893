<template>
	<pm-effi :controller="controller">
		<pm-table
			:columns="COLUMNS"
			:use-custom-column="true"
			:pagination="false"
			:operations-fixed="true"
			:operations-width="60"
			:sort-num="false"
			:scroll="{
				x: 'max-content'
			}"
		>
			<template #beforeCustom>
				<m-button type="primary" @click="onCreate">添加</m-button>
			</template>
			<template #content="{ record }">
				<div style="width: 440px; white-space: pre-wrap">{{ record.content }}</div>
			</template>
			<template #operations="{ record }">
				<m-button v-if="record.type === 2" type="link" @click="onEdit(record)">编辑</m-button>
			</template>
		</pm-table>
	</pm-effi>

	<edit-comp ref="editRef" @refresh="onRefresh"></edit-comp>
</template>

<script lang="ts">
import { defineComponent, ref, reactive, toRefs } from 'vue';
import { useRoute } from 'vue-router';
import { ModelController } from 'admin-library';

import EditComp from './comps/edit-dialog/index.vue';
import { COLUMNS } from './config';
import { RemarkCommentListStore } from './store';
import { ItemResponse } from './types';

export default defineComponent({
	components: {
		EditComp
	},
	setup() {
		const route = useRoute();
		const query = route.query;

		const components = {
			editRef: ref<InstanceType<typeof EditComp>>()
		};

		const constants = {
			COLUMNS
		};

		const state = reactive({});

		const controller = new ModelController({
			table: {
				store: RemarkCommentListStore
			}
		});
		controller.tableRequest();

		controller.table.onRequest.use(params => {
			params = {
				...params,
				studentId: query.studentId
			};

			return params;
		});

		const methods = {
			onCreate() {
				components.editRef.value.open({ studentId: query.studentId });
			},
			onEdit(row: ItemResponse) {
				components.editRef.value.open(row);
			},
			onRefresh() {
				controller.tableRequest();
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			controller
		};
	}
});
</script>
