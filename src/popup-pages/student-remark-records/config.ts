import { TableColumn, ColumnXtype, TableDateFormat } from 'admin-library';
import { ColumnWidthEnum } from '@/shard/constant';
import { TYPE_MAP } from './constant';

export const COLUMNS: TableColumn[] = [
	{
		title: '评语内容',
		dataIndex: 'content',
		xtype: ColumnXtype.CUSTOM,
		width: 430
	},
	{
		title: '评语类型',
		dataIndex: 'type',
		render: data => {
			return TYPE_MAP[data];
		},
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '点评人',
		dataIndex: 'commentUser',
		width: ColumnWidthEnum.TEXT3
	},
	{
		title: '评价时间',
		dataIndex: 'commentTime',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	}
];
