<template>
	<pm-effi :controller="controller">
		<pm-search>
			<pm-search-single
				:span="3"
				:model-value="'car'"
				:antdProps="{
					placeholder: '车型',
					allowClear: false
				}"
				data-index="carType"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="3"
				:model-value="1"
				:antdProps="{
					placeholder: '科目',
					allowClear: false
				}"
				data-index="kemu"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="3"
				:model-value="'101'"
				:antdProps="{
					placeholder: '场景',
					allowClear: false
				}"
				data-index="sceneCode"
				xtype="SELECT"
			/>
		</pm-search>
		<pm-table
			:columns="COLUMNS"
			:use-custom-column="true"
			:pagination="false"
			:operations="false"
			:sort-num="false"
			:scroll="{}"
		>
			<template #question="{ record }">
				<question-comp
					:question="record.question"
					:mediaType="record.mediaType"
					:mediaUrl="record.mediaUrl"
					:options="record"
				></question-comp>
			</template>
		</pm-table>
	</pm-effi>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue';
import { useRoute } from 'vue-router';
import { ModelController } from 'admin-library';

import QuestionComp from '@/popup-pages/wrong-questions/comps/question/index.vue';
import { COLUMNS } from './config';
import { KEMU_STORE, CAR_TYPE_STORE, SCENE_STORE } from '@/shard/constant';
import { WrongBookStore } from './store';

export default defineComponent({
	components: { QuestionComp },
	setup() {
		const route = useRoute();
		const query = route.query;

		const components = {};

		const constants = {
			COLUMNS
		};

		const state = reactive({});

		const controller = new ModelController({
			table: {
				store: WrongBookStore
			},
			search: {
				carType: {
					store: CAR_TYPE_STORE
				},
				kemu: {
					store: KEMU_STORE
				},
				sceneCode: {
					store: SCENE_STORE
				}
			}
		});
		controller.tableRequest();

		controller.table.onRequest.use(params => {
			params = {
				...params,
				homeworkId: query.exerciseId
			};

			return params;
		});

		const methods = {};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			controller
		};
	}
});
</script>

<style lang="less" scoped></style>
