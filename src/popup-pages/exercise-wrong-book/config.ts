import { ColumnXtype, TableColumn, TableDateFormat } from 'admin-library';
import { ColumnWidthEnum } from '@/shard/constant';
import { RESULT_MAP } from './constant';

export const COLUMNS: TableColumn[] = [
	{
		title: '试题Id',
		dataIndex: 'id',
		width: 120
	},
	{
		title: '试题题干',
		dataIndex: 'question',
		xtype: ColumnXtype.CUSTOM
	},
	{
		title: '状态',
		dataIndex: 'status',
		render: data => {
			return RESULT_MAP[data];
		},
		width: ColumnWidthEnum.TEXT3
	},
	{
		title: '答错时间',
		dataIndex: 'wrongTime',
		type: 'date',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	},
	{
		title: '同步时间',
		dataIndex: 'updateTime',
		type: 'date',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	}
];
