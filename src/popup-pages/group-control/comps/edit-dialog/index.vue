<template>
	<pm-dialog v-model:visible="visible" :title="title" width="680px">
		<template v-if="visible">
			<m-form
				ref="formRef"
				:model="formState"
				:colon="false"
				:label-col="{ style: { width: '95px' } }"
				autocomplete="off"
			>
				<m-form-item label="分组名称" name="groupName" required>
					<m-input v-model:value="formState.groupName" placeholder="分组名称" />
				</m-form-item>
				<m-form-item label="显示顺序" name="orderValue" required>
					<m-input-number v-model:value="formState.orderValue" placeholder="显示顺序" :min="1" />
				</m-form-item>
				<m-form-item label="可见范围">
					<m-row justify="space-between" :gutter="16">
						<m-col :span="8">
							<m-form-item label="" name="carType">
								<m-select
									v-model:value="formState.carType"
									:options="options.carList"
									placeholder="车型"
								/>
							</m-form-item>
						</m-col>
						<m-col :span="8">
							<m-form-item label="" name="kemu">
								<m-select
									v-model:value="formState.kemu"
									:options="options.kemuList"
									placeholder="科目"
								/>
							</m-form-item>
						</m-col>
						<m-col :span="8">
							<m-form-item label="" name="skillStatus">
								<m-select
									v-model:value="formState.skillStatus"
									:options="options.skillStatusList"
									placeholder="审核状态"
								/>
							</m-form-item>
						</m-col>
					</m-row>
				</m-form-item>
			</m-form>
		</template>

		<template #footer>
			<m-button plain @click="visible = false">取消</m-button>
			<m-button :loading="loading" type="primary" @click="onConfirm">确定</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { MESSAGE_TYPE, MUtils } from 'admin-library';

import { cloneFromPick, cloneByMerge } from '@/shard/utils';
import { CAR_TYPE_OPTIONS, EXAM_KEMU_OPTIONS } from '@/shard/constant';
import { SKILL_STATUS_OPTIONS } from '@/application/part-time-teacher/constant';
import { CreateStore, UpdateStore } from '../../store';

const DefaultFormState = {
	id: null,
	groupName: '',
	orderValue: '',
	carType: null,
	kemu: null,
	skillStatus: null
};

export default defineComponent({
	emits: ['refresh'],
	components: {},
	setup(props, { emit }) {
		const components = {
			formRef: ref(null)
		};

		const constants = {};

		const state = reactive({
			title: '',
			visible: false,
			loading: false,
			formState: MUtils.deepClone(DefaultFormState),
			options: {
				carList: CAR_TYPE_OPTIONS,
				kemuList: EXAM_KEMU_OPTIONS,
				skillStatusList: SKILL_STATUS_OPTIONS
			}
		});

		const methods = {
			async open(row?: any) {
				state.formState = MUtils.deepClone(DefaultFormState);
				state.title = row?.id ? '编辑' : '新增';

				if (row) {
					state.formState = cloneFromPick(row, DefaultFormState);
				}

				state.visible = true;
			},
			async onConfirm() {
				await components.formRef.value.validate();

				try {
					state.loading = true;
					const params = cloneByMerge(state.formState, DefaultFormState);
					const fetchStore = state.formState.id ? UpdateStore : CreateStore;

					await fetchStore.request(params).getData();
					emit('refresh');
					state.visible = false;
					MUtils.toast('保存成功', MESSAGE_TYPE.success);
				} finally {
					state.loading = false;
				}
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods
		};
	}
});
</script>
