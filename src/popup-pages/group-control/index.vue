<template>
	<pm-effi :controller="controller">
		<pm-table
			:columns="COLUMNS"
			:use-custom-column="true"
			:operations="true"
			:sort-num="false"
			:scroll="{
				x: 'max-content'
			}"
		>
			<template #beforeCustom>
				<m-button type="primary" @click="onCreate">新增</m-button>
			</template>
			<template #scope="{ record }">
				{{ CAR_TYPE_MAP[record.carType] }}{{ EXAM_KEMU_MAP[record.kemu] }}
				&nbsp;
				<span
					v-html="
						renderTextWithColor(record.skillStatus, SKILL_STATUS_OPTIONS, {
							isReturnStr: true
						})
					"
				></span>
			</template>
			<template #operations="{ record }">
				<m-button type="link" @click="onEdit(record)">编辑</m-button>
				<m-button type="link" danger @click="onDel(record)">删除</m-button>
			</template>
		</pm-table>
	</pm-effi>
	<edit-comp ref="editRef" @refresh="onRefresh"></edit-comp>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { MUtils } from 'admin-library';
import { ModelController, MESSAGE_TYPE } from 'admin-library';

import EditComp from './comps/edit-dialog/index.vue';
import { confirmMessageBox, renderTextWithColor } from '@/shard/utils';
import { COLUMNS } from './config';
import { CAR_TYPE_MAP, EXAM_KEMU_MAP } from '@/shard/constant';
import { SKILL_STATUS_OPTIONS } from '@/application/part-time-teacher/constant';
import { ListStore, DelStore } from './store';

export default defineComponent({
	components: {
		EditComp
	},
	setup() {
		const components = {
			editRef: ref<InstanceType<typeof EditComp>>()
		};

		const constants = {
			COLUMNS,
			CAR_TYPE_MAP,
			EXAM_KEMU_MAP,
			SKILL_STATUS_OPTIONS
		};

		const state = reactive({});

		const controller = new ModelController({
			table: {
				store: ListStore
			}
		});
		controller.tableRequest();

		const methods = {
			renderTextWithColor,
			onCreate() {
				components.editRef.value.open();
			},
			onRefresh() {
				controller.tableRequest();
			},
			onEdit(row: any) {
				components.editRef.value.open(row);
			},
			async onDel(row: any) {
				await confirmMessageBox('确认删除这个分组吗？');
				await DelStore.request({ id: row.id }).getData();
				methods.onRefresh();
				MUtils.toast('删除成功', MESSAGE_TYPE.success);
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			controller
		};
	}
});
</script>
