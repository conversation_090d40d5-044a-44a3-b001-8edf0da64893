import { TableColumn } from 'admin-library';
import { ColumnWidthEnum, CAR_TYPE_MAP, KEMU_MAP } from '@/shard/constant';

export const COLUMNS: TableColumn[] = [
	{
		title: '录播课程Id',
		dataIndex: 'lessonId'
	},
	{
		title: '车型',
		dataIndex: 'carType',
		render: data => {
			return CAR_TYPE_MAP[data];
		},
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '科目',
		dataIndex: 'kemu',
		render: data => {
			return KEMU_MAP[data];
		},
		width: ColumnWidthEnum.TEXT3
	},
	{
		title: '课程名称',
		dataIndex: 'lessonName',
		width: 220
	},
	{
		title: '视频观看进度',
		dataIndex: 'progress',
		render: (value, lineData) => {
			const { watchDuration, totalDuration } = lineData;
			if (totalDuration) {
				let progress = (watchDuration / totalDuration) * 100;
				progress = Number(progress.toFixed(2));
				return progress + '%';
			} else {
				return '--';
			}
		},
		width: ColumnWidthEnum.TEXT6
	},
	{
		title: '视频观看时长（秒）',
		dataIndex: 'watchDuration',
		width: 180
	},
	{
		title: '视频观看次数',
		dataIndex: 'watchTimes',
		width: ColumnWidthEnum.TEXT6
	}
];
