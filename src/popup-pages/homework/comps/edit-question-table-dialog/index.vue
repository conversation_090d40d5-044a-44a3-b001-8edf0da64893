<template>
	<pm-dialog v-model:visible="visible" :title="title" width="1280px">
		<pm-effi :controller="controller">
			<pm-search>
				<pm-search-single
					:span="3"
					:antdProps="{
						placeholder: '试题Id(多个用逗号分隔)'
					}"
					data-index="questionIds"
					xtype="INPUT"
				/>
				<pm-search-single
					:span="3"
					:antdProps="{
						placeholder: '知识点Id(多个用逗号分隔)'
					}"
					data-index="knowledgeIds"
					xtype="INPUT"
				/>
			</pm-search>

			<pm-table
				:columns="COLUMNS"
				:use-custom-column="true"
				:operations="false"
				:sort-num="false"
				:scroll="{
					x: 'max-content',
					y: '420px'
				}"
				:antdProps="{
					rowKey: 'questionId',
					rowSelection: {
						preserveSelectedRowKeys: true,
						selectedRowKeys: formState.questionIds,
						onChange: onSelectChange
					}
				}"
			></pm-table>
		</pm-effi>
		<template #footer>
			<m-button plain @click="visible = false">取消</m-button>
			<m-button :loading="loading" type="primary" @click="onConfirm">确定</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue';
import { MESSAGE_TYPE, MUtils, ModelController } from 'admin-library';

import { cloneFromPick, cloneByMerge } from '@/shard/utils';
import { COLUMNS } from './config';
import { QuesListStore, AddHomeworkQuesStore } from './store';

const DefaultFormState = {
	lessonId: undefined,
	questionIds: []
};

export default defineComponent({
	emits: ['refresh'],
	setup(props, { emit }) {
		const components = {};

		const constants = {
			COLUMNS
		};

		const state = reactive({
			visible: false,
			title: '添加课后作业',
			loading: false,
			formState: MUtils.deepClone(DefaultFormState)
		});

		const controller = new ModelController({
			table: {
				store: QuesListStore
			}
		});

		controller.table.onRequest.use(params => {
			params = {
				...params,
				lessonId: state.formState.lessonId
			};

			return params;
		});

		const methods = {
			onSelectChange(keys) {
				state.formState.questionIds = keys;
			},
			open({ lessonId }) {
				state.formState = cloneFromPick({ lessonId }, DefaultFormState);

				controller.tableRequest();

				state.visible = true;
			},
			async onConfirm() {
				if (!state.formState.questionIds?.length) {
					MUtils.toast('请先勾选试题编号', MESSAGE_TYPE.warning);
					return;
				}

				try {
					state.loading = true;
					const params = cloneByMerge(state.formState, DefaultFormState);
					const fetchStore = AddHomeworkQuesStore;

					params.questionIds = params.questionIds.join(',');

					await fetchStore.request(params).getData();

					emit('refresh');
					state.visible = false;
					MUtils.toast('保存成功', MESSAGE_TYPE.success);
				} finally {
					state.loading = false;
				}
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			controller
		};
	}
});
</script>
