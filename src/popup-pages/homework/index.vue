<template>
	<pm-effi :controller="controller">
		<pm-search>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '知识点Id'
				}"
				data-index="knowledgeId"
				xtype="INPUT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '题干'
				}"
				data-index="question"
				xtype="INPUT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '车型'
				}"
				data-index="carType"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '科目'
				}"
				data-index="kemu"
				xtype="SELECT"
			/>
		</pm-search>

		<pm-table
			:columns="COLUMNS"
			:use-custom-column="true"
			:operations-fixed="true"
			:operations-width="120"
			:sort-num="false"
			:scroll="{
				x: 'max-content'
			}"
		>
			<template #beforeCustom>
				<m-button type="primary" @click="addQeustion">添加</m-button>
			</template>
			<template #operations="{ record }">
				<m-button danger type="link" @click="onDel(record)">删除</m-button>
			</template>
		</pm-table>
	</pm-effi>

	<edit-question-table-comp ref="editQuestionTableRef" @refresh="onRefresh"></edit-question-table-comp>
</template>

<script lang="ts">
import { defineComponent, reactive, ref, toRefs } from 'vue';
import { ModelController, MUtils, MESSAGE_TYPE } from 'admin-library';
import { useRoute } from 'vue-router';

import EditQuestionTableComp from './comps/edit-question-table-dialog/index.vue';
import { confirmMessageBox } from '@/shard/utils';
import { COLUMNS } from './config';
import { CAR_TYPE_STORE, KEMU_STORE } from '@/shard/constant';
import { homeworkQuesListStore, DelHomeworkQuesStore } from './store';
import { ItemResponse } from './types';

export default defineComponent({
	components: {
		EditQuestionTableComp
	},
	setup() {
		const route = useRoute();
		const query = route.query;

		const components = {
			editQuestionTableRef: ref<InstanceType<typeof EditQuestionTableComp>>()
		};

		const constants = {
			COLUMNS
		};

		const state = reactive({});

		const controller = new ModelController({
			table: {
				store: homeworkQuesListStore
			},
			search: {
				carType: {
					store: CAR_TYPE_STORE
				},
				kemu: {
					store: KEMU_STORE
				}
			}
		});
		controller.tableRequest();

		controller.table.onRequest.use(params => {
			params = {
				...params,
				lessonId: query.lessonId
			};

			return params;
		});

		const methods = {
			addQeustion() {
				components.editQuestionTableRef.value.open({
					lessonId: query.lessonId
				});
			},
			async onDel(row: ItemResponse) {
				await confirmMessageBox('确认删除吗？');

				await DelHomeworkQuesStore.request({ idList: row.id }).getData();
				methods.onRefresh();
				MUtils.toast('删除成功', MESSAGE_TYPE.success);
			},

			onRefresh() {
				controller.tableRequest();
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			controller
		};
	}
});
</script>
