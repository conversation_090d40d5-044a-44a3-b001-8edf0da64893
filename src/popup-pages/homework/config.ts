import { TableColumn } from 'admin-library';

export const COLUMNS: TableColumn[] = [
	{
		title: '试题Id',
		dataIndex: 'questionId',
		width: 120,
		fixed: 'left'
	},
	{
		title: '知识点',
		dataIndex: 'knowledgeNameList',
		render: data => {
			return data?.join('、');
		},
		fixed: 'left'
	},
	{
		title: '知识点Id',
		dataIndex: 'knowledgeIdList',
		render: data => {
			return data?.join('、');
		},
		width: 100
	},
	{
		title: '题干',
		dataIndex: 'question'
	},
	{
		title: '标签',
		dataIndex: 'tagNameList',
		render: data => {
			return data?.filter(item => !!item).join('、');
		}
	}
];
