<template>
	<div style="position: absolute; top: 20px; right: 20px; bottom: 20px; left: 20px">
		<m-row style="padding-top: 20px; padding-left: 20px">
			<m-tag v-if="roomInfo.status === 3" color="default">已结束</m-tag>
			<m-tag v-else-if="roomInfo.status === 2" color="green">直播中</m-tag>
			<m-tag v-else color="default">未开始</m-tag>
			<m-button type="link" size="small" @click="copy">复制上课链接</m-button>
		</m-row>
		<m-row justify="start" style="padding: 20px; height: calc(100% - 100px)">
			<m-col :span="18" style="height: 100%" :key="videoKey">
				<video style="width: 100%; height: 100%" id="video"></video>
			</m-col>
			<m-col style="padding-left: 20px; height: 100%" :span="6">
				<div style="height: 100%; background-color: #efefef">
					<div
						style="overflow-x: auto; height: calc(100% - 52px)"
						id="messageScrollList"
						ref="messageListRef"
					>
						<m-row
							no-gutters
							align="middle"
							v-for="item in messageList"
							style="padding: 5px 10px"
							:key="item.ID"
						>
							<m-col cols="auto">
								<m-avatar size="36px" :src="item.avatar"></m-avatar>
							</m-col>
							<m-col cols="auto">{{ item.nick || item.from }}：{{ item.payload.text }}</m-col>
						</m-row>
					</div>
					<div style="padding: 10px">
						<m-input-search v-model:value="text" enter-button="发送" @search="sendMessage" />
					</div>
				</div>
			</m-col>
		</m-row>
		<m-row style="padding-left: 20px">
			<m-button class="mr10" type="primary" danger @click="onStop">解散会议室</m-button>
			<m-button type="primary" @click="onWarn">警告讲师</m-button>
		</m-row>
	</div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref, Ref, watch, nextTick } from 'vue';
import { useRoute } from 'vue-router';
import { MESSAGE_TYPE, MUtils } from 'admin-library';
import { onViewHide, onViewShow } from '@paas/paas-library';
import { throttle } from 'lodash';
import TCPlayer from 'tcplayer.js';
import 'tcplayer.js/dist/tcplayer.min.css';
import TIM, { ChatSDK, Message } from 'tim-js-sdk';

import {
	GetRoomPullStreamStore,
	TrainAdminAuthStore,
	StopTeachStore,
	GetRoomInfoStore
} from '@/application/courses/store';
import { toAwait, confirmMessageBox, copyText } from '@/shard/utils';

interface MessageItem {
	ID: string;
	type: string;
	payload: {
		text: string;
	};
	avatar;
	nick: string;
	from: string;
	flow: string;
	sequence: number;
}
interface AuthItem {
	userId: string;
	userSign: string;
	sdkAppId: number;
	nickName: string;
}
interface RoomInfoItem {
	roomId: string;
	status: number;
}

interface IScrollInfo {
	scrollHeight: number;
	scrollLeft: number;
	scrollTop: number;
	scrollWidth: number;
	id: string;
}
enum OperateTypeEnum {
	OPENDEVICE = 1,
	CLOSEDEVICE = 2,
	ROOMSTATUSUPDATE = 3,
	STARTEXAM = 4,
	SUBMITEXAM = 5,
	DISSMISSBYADMIN = 6,
	WARNMASTER = 7
}

function getScrollInfo(selector: string | HTMLElement): Promise<IScrollInfo> {
	if (!selector) {
		return Promise.reject(new Error(`getScrollInfo() get error selector ${typeof selector}.`));
	}

	const dom: HTMLElement | null = typeof selector === 'object' ? selector : document.querySelector(selector);
	if (!dom) {
		return Promise.reject(new Error(`getScrollInfo() can't find ${selector} dom.`));
	}
	return Promise.resolve({
		id: dom.id,
		scrollTop: dom.scrollTop,
		scrollLeft: dom.scrollLeft,
		scrollWidth: dom.scrollWidth,
		scrollHeight: dom.scrollHeight
	});
}

function play(id, sources) {
	const player = TCPlayer(id, {
		sources: sources,
		autoplay: true
	});

	player.on('webrtcevent', event => {
		console.log('EVENT webrtcevent', event.data.code, event.data);
	});
	return player;
}

async function joinTim({ userId, userSig, sdkAppId, roomId, userName, avatar }) {
	async function joinGroup(roomId) {
		return await toAwait(
			instance.joinGroup({
				groupID: roomId
			})
		);
	}
	async function quitGroup(roomId) {
		return await toAwait(instance.quitGroup(roomId));
	}

	const instance = TIM.create({
		SDKAppID: sdkAppId
	});
	let err;
	[err] = await toAwait(
		instance.login({
			userID: userId,
			userSig: userSig
		})
	);
	if (err) {
		throw err;
	}
	await new Promise(resolve => {
		instance.on(TIM.EVENT.SDK_READY, () => {
			resolve(true);
		});
	});

	instance.updateMyProfile({
		nick: userName,
		avatar: avatar
	});

	const [error] = await joinGroup(roomId);
	if (error) {
		if (error.code === 10013) {
			[err] = await toAwait(quitGroup(roomId));
			if (err) {
				throw err;
			}
			[err] = await toAwait(joinGroup(roomId));
			if (err) {
				throw err;
			}
		} else {
			throw error;
		}
	}
	return instance as ChatSDK;
}

const messageList: Ref<MessageItem[]> = ref([]);
let text = ref('');
let videoKey = ref(+new Date());
let isFirst = false;

export default defineComponent({
	setup() {
		const route = useRoute();

		let imInstance = null;
		let player = null;
		let isScrollAtBottom = true;
		const handleMessageListScroll = (e: Event) => {
			const messageContainer = e.target as HTMLElement;
			const bottom = messageContainer.scrollHeight - messageContainer.scrollTop - messageContainer.clientHeight;
			isScrollAtBottom = bottom <= 80;
		};

		const handleScroll = throttle(handleMessageListScroll, 1000);

		const components = {
			messageListRef: ref<any>(null)
		};

		const constants = {};

		const state = reactive({
			roomInfo: {
				roomId: '',
				status: 1
			},
			localUserInfo: { avatar: '', userName: '', userId: '' }
		});

		const methods = {
			async init() {
				await methods.getRoomStatus();
				methods.startVideo();
				methods.startChat();
				components.messageListRef.value?.addEventListener('scroll', handleScroll);
			},
			handleScroll: throttle(handleMessageListScroll, 1000),
			async getRoomStatus() {
				const res = (await GetRoomInfoStore.request({ roomNo: route.query.roomNo }).getData()) as RoomInfoItem;
				res.roomId = String(res.roomId);
				state.roomInfo = res;
				return res;
			},
			async startVideo() {
				const res = await GetRoomPullStreamStore.request({ roomNo: route.query.roomNo }).getData();

				player = play(
					'video',
					res.map(item => {
						return {
							src: item.url
						};
					})
				);
			},
			async startChat() {
				const res = await TrainAdminAuthStore.request({ roomNo: route.query.roomNo }).getData();
				const avatar =
					'https://jiakao-web.mc-cdn.cn/jiakao-web/2024/11/25/14/9969f266ab414d899fbfd2ba4f7bd9cc.png';

				const { userId, userSign: userSig, sdkAppId, nickName: userName } = res as AuthItem;
				state.localUserInfo = {
					userId,
					userName,
					avatar
				};

				const roomId = state.roomInfo.roomId;
				imInstance = await joinTim({ userId, userSig, sdkAppId, roomId, userName, avatar });
				imInstance.on(TIM.EVENT.MESSAGE_RECEIVED, methods.onReceiveMessage);
				const { currentMessageList } = await methods.getMessageList();
				messageList.value = currentMessageList;
			},
			async onStop() {
				await confirmMessageBox('确认解散会议室吗？');

				await StopTeachStore.request({ roomNo: route.query.roomNo }).getData();

				await methods.sendCustionMessage({
					data: JSON.stringify({
						operateType: OperateTypeEnum.DISSMISSBYADMIN
					}),
					description: '',
					extension: 'MCTrainingLiveExt'
				});
				MUtils.toast('操作成功', MESSAGE_TYPE.success);
			},

			async onWarn() {
				await methods.sendCustionMessage({
					data: JSON.stringify({
						operateType: OperateTypeEnum.WARNMASTER
					}),
					description: '',
					extension: 'MCTrainingLiveExt'
				});
				MUtils.toast('操作成功', MESSAGE_TYPE.success);
			},

			copy() {
				const roomNo = route.query.roomNo;
				const roomId = route.query.courseId;
				const host = 'https://laofuzi.kakamobi.com/';
				const url = `${host}personal-training-live/?roomNo=${roomNo}&courseId=${roomId}`;

				const ret = copyText(url);
				if (ret) {
					MUtils.toast('复制成功');
				}
			},

			async sendMessage() {
				if (!imInstance) {
					return;
				}
				const result = text.value;
				const roomId = state.roomInfo.roomId;
				let message = imInstance.createTextMessage({
					to: roomId,
					conversationType: TIM.TYPES.CONV_GROUP,
					payload: {
						text: result
					}
				});
				const [err] = await toAwait(
					imInstance.sendMessage(message, {
						messageControlInfo: {
							excludedFromContentModeration: true
						}
					})
				);
				if (err) {
					MUtils.toast(`发送失败，请稍后重试 ${err.code || ''}`, MESSAGE_TYPE.error);
					return;
				}
				text.value = '';
				const { avatar: avatarUrl, userName, userId } = state.localUserInfo;

				const messageIds = messageList.value.map(message => message.ID);
				if (messageIds.indexOf(message.ID) === -1) {
					messageList.value.push({
						ID: Math.random().toString(),
						type: TIM.TYPES.MSG_TEXT as string,
						payload: {
							text: result
						},
						avatar: avatarUrl,
						nick: userName || userId,
						from: userId,
						flow: 'out',
						sequence: Math.random()
					});
				}
			},

			async sendCustionMessage(payload) {
				if (!imInstance) {
					return;
				}
				const roomId = state.roomInfo.roomId;
				const message = imInstance.createCustomMessage({
					to: roomId,
					conversationType: TIM.TYPES.CONV_GROUP,
					priority: TIM.TYPES.MSG_PRIORITY_NORMAL,
					payload: payload
				});
				return await imInstance.sendMessage(message, {
					messageControlInfo: {
						excludedFromContentModeration: true
					}
				});
			},

			async getMessageList(): Promise<{
				currentMessageList: any[];
				isCompleted: boolean;
				nextReqMessageId: string;
			}> {
				const roomId = state.roomInfo.roomId;
				const result: {
					currentMessageList: any[];
					isCompleted: boolean;
					nextReqMessageId: string;
				} = {
					currentMessageList: [],
					isCompleted: false,
					nextReqMessageId: ''
				};
				const getIMMessageList = async () => {
					const conversationData: {
						conversationID: string;
						nextReqMessageID?: string | undefined;
					} = {
						conversationID: `GROUP${roomId}`
					};
					if (result.nextReqMessageId !== '') {
						conversationData.nextReqMessageID = result.nextReqMessageId;
					}
					const imResponse = await imInstance.getMessageList(conversationData);
					const { messageList, isCompleted, nextReqMessageID } = imResponse.data;
					const filterCurrentMessageList = messageList.filter(
						(item: any) => item.type === TIM.TYPES.MSG_TEXT
					);
					result.currentMessageList.splice(0, 0, ...filterCurrentMessageList);
					result.isCompleted = messageList.length > 0 ? isCompleted : true;
					result.nextReqMessageId = nextReqMessageID;
					if (result.isCompleted) {
						return;
					}
					await getIMMessageList();
				};

				await getIMMessageList();

				return result;
			},

			onReceiveMessage(options: { data: any }) {
				const messages = options.data;

				messages.forEach((message: Message) => {
					// 群组消息
					if (message.type === TIM.TYPES.MSG_TEXT && message.to === state.roomInfo.roomId) {
						const {
							ID,
							payload: { text },
							nick: userName,
							from: userId,
							avatar
						} = message;
						messageList.value.push({
							ID,
							type: TIM.TYPES.MSG_TEXT as string,
							payload: {
								text
							},
							avatar,
							nick: userName || userId,
							from: userId,
							flow: 'in',
							sequence: Math.random()
						});
					}
				});
			},

			async scrollToLatestMessage() {
				const { scrollHeight } = await getScrollInfo('#messageScrollList');
				if (components.messageListRef.value) {
					components.messageListRef.value.scrollTop = scrollHeight;
				}
			}
		};

		if (!isFirst) {
			isFirst = true;
			methods.init();
			onViewShow({
				online: true,
				callback: () => {
					methods.startVideo();
				}
			});
			onViewHide({
				online: true,
				callback: () => {
					player.dispose();
					videoKey.value = +new Date();
				}
			});
		}

		watch(
			messageList,
			async (newMessageList, oldMessageList) => {
				if ((newMessageList as any).length === 0) {
					return;
				}
				const lastMessage = (newMessageList as any).slice(-1);
				const oldLastMessage = (oldMessageList as any).slice(-1);
				const isSendByMe = lastMessage[0].flow === 'out';
				const isNewMessage = lastMessage[0].ID !== oldLastMessage[0]?.ID;
				await nextTick();
				if (isScrollAtBottom) {
					methods.scrollToLatestMessage();
					return;
				}
				if (isSendByMe && isNewMessage) {
					methods.scrollToLatestMessage();
				}
				methods.scrollToLatestMessage();
			},
			{ deep: true }
		);

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			messageList,
			text,
			videoKey
		};
	}
});
</script>
