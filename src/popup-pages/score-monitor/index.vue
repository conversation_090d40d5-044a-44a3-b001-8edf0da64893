<template>
	<pm-effi :controller="controller">
		<pm-table
			:columns="COLUMNS"
			:use-custom-column="true"
			:pagination="false"
			:operations="false"
			:sort-num="false"
			:scroll="{
				x: 'max-content'
			}"
		>
			<template #changeValue="{ record }">
				<span v-if="record.changeValue > 0" class="danger">
					<caret-up-outlined />
					{{ record.changeValue }}
				</span>
				<span v-else-if="record.changeValue < 0" class="success">
					<caret-down-outlined />
					{{ Math.abs(record.changeValue) }}
				</span>
				<span v-else class="info">
					<minus-outlined />
				</span>
			</template>
		</pm-table>
	</pm-effi>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue';
import { CaretUpOutlined, CaretDownOutlined, MinusOutlined } from '@ant-design/icons-vue';
import { ModelController } from 'admin-library';
import { useRoute } from 'vue-router';

import { COLUMNS } from './config';
import { ListStore } from './store';

export default defineComponent({
	components: { CaretUpOutlined, CaretDownOutlined, MinusOutlined },
	setup() {
		const route = useRoute();
		const query = route.query;

		const components = {};

		const constants = {
			COLUMNS
		};

		const state = reactive({});

		const controller = new ModelController({
			table: {
				store: ListStore
			}
		});
		controller.tableRequest();

		controller.table.onRequest.use(params => {
			params = {
				...params,
				studentProfileId: query.studentProfileId
			};

			return params;
		});

		const methods = {};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			controller
		};
	}
});
</script>
