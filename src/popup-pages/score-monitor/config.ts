import { ColumnXtype, TableColumn, TableDateFormat } from 'admin-library';
import { ColumnWidthEnum } from '@/shard/constant';

export const COLUMNS: TableColumn[] = [
	{
		title: '课程Id',
		dataIndex: 'courseScheduleId'
	},
	{
		title: '课程主题',
		dataIndex: 'courseSubject'
	},
	{
		title: '课程开始时间',
		dataIndex: 'courseBeginTime',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	},
	{
		title: '课程结束时间',
		dataIndex: 'courseEndTime',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	},
	{
		title: '课前近五次模考平均分',
		dataIndex: 'beforeAvgScore',
		width: 220
	},
	{
		title: '课后24小时模考最高分',
		dataIndex: 'afterHighScore',
		width: 220
	},
	{
		title: '变动值',
		dataIndex: 'changeValue',
		xtype: ColumnXtype.CUSTOM,
		width: ColumnWidthEnum.TEXT6
	},
	{
		title: '更新时间',
		dataIndex: 'updateTime',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	}
];
