<template>
	<div class="question">
		<div class="question-main">
			{{ question }}
		</div>
		<div class="media-box" v-if="mediaType">
			<img :src="mediaUrl" v-if="mediaType === 1" />
			<video controls :src="mediaUrl" v-if="mediaType === 2"></video>
		</div>
		<div class="options" v-if="optionsList">
			<div v-for="option in optionsList" :key="option.key">
				<span>{{ option.key }}</span>
				、
				<span v-html="option.value"></span>
			</div>
		</div>
	</div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, computed } from 'vue';

const optionArr = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H'];

export default defineComponent({
	props: {
		question: {
			type: Number,
			isRequired: true
		},
		mediaType: {
			type: Number,
			isRequired: false
		},
		mediaUrl: {
			type: String,
			isRequired: false
		},
		options: {
			type: Object,
			isRequired: true
		}
	},
	setup(props) {
		const components = {};

		const constants = {
			optionArr: ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H']
		};

		const state = reactive({});

		const optionsList = computed(() => {
			const options = optionArr
				.map(optionKey => {
					return {
						key: optionKey,
						value: props.options[`option${optionKey}`]
					};
				})
				.filter(item => item.value);

			return options;
		});

		const methods = {};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			...props,
			optionsList
		};
	}
});
</script>

<style lang="less" scoped>
.question {
	.question-main {
		padding: 10px 0;
		font-size: 14px;
	}
	.media-box {
		video,
		img {
			max-width: 320px;
			max-height: 240px;
		}
	}
	.options > div {
		margin: 6px 0;
	}
}
</style>
