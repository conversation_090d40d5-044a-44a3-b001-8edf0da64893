<template>
	<pm-effi :controller="controller">
		<pm-table
			:columns="COLUMNS"
			:use-custom-column="true"
			:pagination="false"
			:operations="false"
			:sort-num="false"
			:scroll="{}"
		>
			<template #buttonsTop v-if="showSearch">
				<div class="pm-form-search" style="padding: 20px 0 15px">
					<m-row class="ant-row-top">
						<m-col :span="3" style="padding-right: 4px; padding-left: 4px">
							<m-select
								style="width: 100%"
								v-model:value="searchParams.carType"
								:options="options.carList"
							></m-select>
						</m-col>
						<m-col :span="3" style="padding-right: 4px; padding-left: 4px">
							<m-select
								style="width: 100%"
								v-model:value="searchParams.kemu"
								:options="options.kemuList"
							></m-select>
						</m-col>
						<m-col :span="3" style="padding-right: 4px; padding-left: 4px">
							<m-select
								style="width: 100%"
								v-model:value="searchParams.sceneCode"
								:options="options.secenList"
							></m-select>
						</m-col>
						<m-col :span="2" class="search-btn-col">
							<div class="search-btn-container">
								<m-button type="primary" @click="onSearch">
									<loading-outlined v-if="loading" />
									<search-outlined v-else />
									查询
								</m-button>
								<m-button
									style="padding-right: 8px; padding-left: 8px; margin-left: 8px"
									@click="onReset"
								>
									<undo-outlined />
								</m-button>
							</div>
						</m-col>
					</m-row>
				</div>
			</template>
			<template #beforeButtons>
				<m-radio-group
					v-model:value="category"
					button-style="solid"
					@change="onTypeChange"
					style="padding-left: 4px; width: 850px"
				>
					<m-radio-button
						v-for="item in categoryList"
						:key="item.questionIdList"
						:value="item.questionIdList"
					>
						{{ item.category }}（{{ item.questionIdList.length }}）
					</m-radio-button>
				</m-radio-group>
			</template>
			<template #question="{ record }">
				<question-comp
					:key="record.questionId"
					:question="record.question"
					:mediaType="record.mediaType"
					:mediaUrl="record.mediaUrl"
					:options="record"
				></question-comp>
			</template>
		</pm-table>
	</pm-effi>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue';
import { useRoute } from 'vue-router';
import { ModelController } from 'admin-library';
import { LoadingOutlined, SearchOutlined, UndoOutlined } from '@ant-design/icons-vue';

import QuestionComp from './comps/question/index.vue';
import { COLUMNS } from './config';
import { KEMU_OPTIONS, CAR_TYPE_OPTIONS, SCENE_OPTIONS } from '@/shard/constant';
import { WrongBookStore, QuestionsExamPointStore, ExamWrongQuestionStore } from './store';

export default defineComponent({
	components: { LoadingOutlined, SearchOutlined, UndoOutlined, QuestionComp },
	setup() {
		const route = useRoute();
		const query = route.query;

		const components = {};

		const constants = {
			COLUMNS
		};

		const state = reactive({
			categoryList: [],
			category: [],
			showSearch: false,
			loading: false,
			searchParams: {
				carType: 'car',
				kemu: 1,
				sceneCode: '101'
			},
			options: {
				kemuList: KEMU_OPTIONS,
				carList: CAR_TYPE_OPTIONS,
				secenList: SCENE_OPTIONS
			}
		});

		const controller = new ModelController({
			table: {
				store: QuestionsExamPointStore
			}
		});

		const methods = {
			onSearch() {
				methods.getCategory();
			},
			onReset() {
				state.searchParams = {
					carType: 'car',
					kemu: 1,
					sceneCode: '101'
				};
				methods.onSearch();
			},
			async getCategory() {
				let res;
				if (query.uniqueId) {
					res = await ExamWrongQuestionStore.request({
						mucangId: query.mucangId,
						uniqueId: query.uniqueId
					}).getData();
				} else {
					state.loading = true;
					res = await WrongBookStore.request({
						mucangId: query.mucangId,
						studyPlan: query.studyPlan,
						...state.searchParams
					}).getData();
				}
				state.loading = false;
				let ids = Array.from(new Set(res.map(item => item.questionIdList).flat()));
				res.unshift({
					id: null,
					category: '全部',
					questionIdList: ids
				});
				state.categoryList = res;
				state.category = ids;
				methods.onTypeChange();
			},
			onTypeChange() {
				controller.tableRequest({ questionIds: state.category.join(',') });
			}
		};

		if (query.errorqIdList) {
			controller.tableRequest({ questionIds: query.errorqIdList });
		} else {
			methods.getCategory();
			if (!query.uniqueId) {
				state.showSearch = true;
			}
		}

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			controller
		};
	}
});
</script>
