<template>
	<pm-dialog v-model:visible="visible" :title="title" width="620px">
		<template v-if="visible">
			<m-form
				ref="formRef"
				:model="formState"
				:colon="false"
				:label-col="{ style: { width: '95px' } }"
				autocomplete="off"
			>
				<m-form-item label="页码" name="orderValue" required>
					<m-input-number v-model:value="formState.orderValue" placeholder="页码" :min="1" />
				</m-form-item>
				<m-form-item label="关联知识点" name="clearReasonType" v-if="showKnowledge">
					<m-select
						v-model:value="formState.knowledgeId"
						:options="options.knowledgeList"
						:fieldNames="{ label: 'name', value: 'id' }"
						placeholder="关联知识点"
					/>
				</m-form-item>
				<m-form-item label="课件图片" name="resource" required>
					<fileUploadComp fileType="image" v-model:value="formState.resource" />
				</m-form-item>
				<m-form-item label="备注" name="remark">
					<m-textarea v-model:value="formState.remark" :maxlength="300" show-count :rows="3" />
				</m-form-item>
			</m-form>
		</template>

		<template #footer>
			<m-button plain @click="visible = false">取消</m-button>
			<m-button :loading="loading" type="primary" @click="onConfirm">确定</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { MESSAGE_TYPE, MUtils } from 'admin-library';

import fileUploadComp from '@/comp/file-upload/index.vue';
import { cloneFromPick, cloneByMerge } from '@/shard/utils';
import { CreateStore, UpdateStore, KnowledgeListStore } from '../../store';

const DefaultFormState = {
	coursewareId: null,
	id: null,
	type: 1,
	orderValue: '',
	resource: null,
	knowledgeId: '',
	remark: ''
};

export default defineComponent({
	components: {
		fileUploadComp
	},
	emits: ['refresh'],
	setup(props, { emit }) {
		const components = {
			formRef: ref(null)
		};

		const constants = {};

		const state = reactive({
			visible: false,
			title: '',
			loading: false,
			formState: MUtils.deepClone(DefaultFormState),
			options: {
				knowledgeList: []
			},
			showKnowledge: false,
		});

		const methods = {
			async initKnowledgeOptions(coursewareId) {
				let res = await KnowledgeListStore.request({
					limit: 9999,
					coursewareId
				}).getData();
				if (res) {
					state.options.knowledgeList = res;
				}
			},
			open(row: any, { showKnowledge, coursewareId }) {
				state.formState = MUtils.deepClone(DefaultFormState);
				state.title = row?.id ? '编辑' : '新增';
				state.showKnowledge = showKnowledge;

				if (row) {
					state.formState = cloneFromPick(row, DefaultFormState);
					if (row.resourceKey) {
						state.formState.resource = {
							showUrl: row.resourceUrl,
							encodedData: row.resourceKey
						};
					}
				}

				state.visible = true;

				methods.initKnowledgeOptions(coursewareId);
			},

			async onConfirm() {
				await components.formRef.value.validate();

				try {
					state.loading = true;
					const params = cloneByMerge(state.formState, DefaultFormState);
					const fetchStore = state.formState.id ? UpdateStore : CreateStore;

					params.resourceKey = params.resource.encodedData;
					delete params.resource;

					await fetchStore.request(params).getData();

					emit('refresh');
					state.visible = false;
					MUtils.toast('保存成功', MESSAGE_TYPE.success);
				} finally {
					state.loading = false;
				}
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods
		};
	}
});
</script>
