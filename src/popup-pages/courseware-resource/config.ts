import { TableColumn } from 'admin-library';
import { renderImageComp } from '@/shard/utils';
import { ColumnWidthEnum } from '@/shard/constant';

export const COLUMNS: TableColumn[] = [
	{
		title: '页码',
		dataIndex: 'orderValue',
		width: ColumnWidthEnum.DATEDAY
	},
	{
		title: '关联知识点',
		dataIndex: 'knowledgeName'
	},
	{
		title: '课件图片',
		dataIndex: 'resourceUrl',
		render: data => {
			return renderImageComp(data);
		},
		width: 150
	},
	{
		title: '备注',
		dataIndex: 'remark',
		width: 240
	}
];
