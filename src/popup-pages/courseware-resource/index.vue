<template>
	<pm-effi :controller="controller">
		<pm-table
			:columns="COLUMNS"
			:use-custom-column="true"
			:operations-fixed="true"
			:operations-width="120"
			:sort-num="false"
			:scroll="{
				x: '100%'
			}"
		>
			<template #beforeCustom>
				<m-button type="primary" @click="onCreate">添加</m-button>
			</template>
			<template #operations="{ record }">
				<m-button type="link" @click="onEdit(record)">编辑</m-button>
				<m-button danger type="link" @click="onDel(record)">删除</m-button>
			</template>
		</pm-table>
	</pm-effi>

	<edit-comp ref="editRef" @refresh="onRefresh"></edit-comp>
</template>

<script lang="ts">
import { defineComponent, ref, reactive, toRefs } from 'vue';
import { useRoute } from 'vue-router';
import { ModelController, MUtils, MESSAGE_TYPE } from 'admin-library';

import EditComp from './comps/edit-dialog/index.vue';
import { confirmMessageBox } from '@/shard/utils';
import { COLUMNS } from './config';
import { ListStore, DelStore } from './store';
import { ItemResponse } from './types';

export default defineComponent({
	components: {
		EditComp
	},
	setup() {
		const route = useRoute();
		const query = route.query;

		const components = {
			editRef: ref<InstanceType<typeof EditComp>>()
		};

		const constants = {
			COLUMNS
		};
		const state = reactive({});

		const controller = new ModelController({
			table: {
				store: ListStore
			}
		});
		controller.tableRequest();

		controller.table.onRequest.use(params => {
			let { coursewareId } = query;
			params = {
				...params,
				coursewareId
			};

			return params;
		});

		const methods = {
			onCreate() {
				let coursewareId = query.coursewareId ? Number(query.coursewareId) : query.coursewareId;
				let showKnowledge = query.coursewareType === '1';
				components.editRef.value.open({ coursewareId }, { showKnowledge, coursewareId });
			},
			onEdit(row: ItemResponse) {
				let coursewareId = query.coursewareId ? Number(query.coursewareId) : query.coursewareId;
				let showKnowledge = query.coursewareType === '1';
				components.editRef.value.open(row, { showKnowledge, coursewareId });
			},
			async onDel(row: ItemResponse) {
				await confirmMessageBox('确认删除吗？');

				await DelStore.request({ id: row.id }).getData();
				methods.onRefresh();
				MUtils.toast('删除成功', MESSAGE_TYPE.success);
			},

			onRefresh() {
				controller.tableRequest();
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			controller
		};
	}
});
</script>
