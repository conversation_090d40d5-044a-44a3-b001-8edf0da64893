<template>
	<pm-dialog v-model:visible="visible" :title="title" width="1280px">
		<template v-if="visible">
			<m-form
				ref="formRef"
				:model="formState"
				:colon="false"
				:label-col="{ style: { width: '120px' } }"
				autocomplete="off"
			>
				<m-form-item label="学员姓名" name="studentId" required>
					<student-select
						@onSelectChange="change"
						v-model:value="formState.studentId"
						data-index="studentId"
					/>
				</m-form-item>
				<m-form-item label="讲师" name="lecturerId" required>
					<teacher-select v-model:value="formState.lecturerId" :showDetail="true" />
				</m-form-item>
				<m-form-item label="课程主题" name="subject" required>
					<m-input v-model:value="formState.subject" placeholder="课程主题" />
				</m-form-item>
				<m-form-item label="时间段">
					<Timeline
						:courses="record.courses"
						:freeDurations="record.freeDurations"
						:select-day="date"
						:showHeaderTick="true"
						:editMode="true"
					/>
				</m-form-item>
				<m-form-item label="课程时间" name="rangeTime" required>
					<m-slider
						v-model:value="formState.rangeTime"
						:tooltip-open="true"
						:tip-formatter="formatter"
						:min="0"
						:max="90"
						range
					/>
					<div v-if="formState.rangeTime">
						{{ formState.rangeTime.map(item => formatter(item)).join('至') }}
					</div>
					<div v-else>--</div>
				</m-form-item>
				<m-form-item
					label="临考冲刺课"
					name="recommendSprint"
					extra="学员正式考试前最后一节课即临考冲刺课，系统将自动推荐教学内容为临考冲刺课件"
				>
					<m-checkbox v-model:checked="formState.recommendSprint"></m-checkbox>
				</m-form-item>
				<m-form-item label="直接发起任务" name="submitDirectly">
					<m-checkbox v-model:checked="formState.submitDirectly">确定上述课程安排都直接发起任务</m-checkbox>
					<div class="danger">
						若勾选「直接发起任务」，课程安排将跳过“草稿”状态。若讲师是兼职讲师，课程安排会直接同步到第三方任务库，请慎重选择！！！
					</div>
				</m-form-item>
				<m-form-item label="未接收提醒" name="notifyBeforeHours" required>
					<m-select v-model:value="formState.notifyBeforeHours" :options="options.beforeHoutsList" />
				</m-form-item>
			</m-form>
		</template>

		<template #footer>
			<m-button plain @click="visible = false">取消</m-button>
			<m-button :loading="loading" type="primary" @click="onConfirm">确定</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { MESSAGE_TYPE, MUtils } from 'admin-library';

import StudentSelect from '@/comp/student-select/index.vue';
import TeacherSelect from '@/comp/teacher-select/index.vue';
import Timeline from '../../comps/timeline/index.vue';
import { cloneFromPick, cloneByMerge, formatTimeStr, toAwait } from '@/shard/utils';
import { BEFORE_HOURS_OPTIONS } from '@/application/courses/constant';
import { BatchCreateStore, SuggestContinueStore } from '@/application/courses/store';

const DefaultFormState = {
	studentId: '',
	lecturerId: '',
	subject: '',
	rangeTime: [],
	notifyBeforeHours: 3,
	recommendSprint: false,
	submitDirectly: false
};

export default defineComponent({
	emits: ['refresh'],
	components: { StudentSelect, TeacherSelect, Timeline },
	setup(props, { emit }) {
		const components = {
			formRef: ref(null)
		};

		const constants = {};

		const state = reactive({
			visible: false,
			title: '添加',
			loading: false,
			formState: MUtils.deepClone(DefaultFormState),
			options: {
				beforeHoutsList: BEFORE_HOURS_OPTIONS
			},
			record: {} as any,
			date: undefined
		});

		const methods = {
			formatter(value) {
				let v = (value + 9 * 6) * 600;
				return formatTimeStr(Math.min(v, 86400 - 1) * 1000, 'HH:mm');
			},
			open(row?: any, date?: string) {
				state.formState = MUtils.deepClone(DefaultFormState);

				if (row) {
					state.formState = cloneFromPick(row, DefaultFormState);
					state.record = row;
				}

				state.date = date;

				state.visible = true;
			},
			change(_val, detail) {
				if (detail.id) {
					state.formState.subject = `${detail.name}-${state.date.replace(/-/g, '')}课程`;
				}
			},
			async onConfirm() {
				await components.formRef.value.validate();

				let [err, res] = await toAwait(
					SuggestContinueStore.request({ studentId: state.formState.studentId }).getData()
				);
				if (!err && !res?.value) {
					const isConfirm = await MUtils.confirm({
						title: '上课效果不及预期提醒',
						content:
							'该学员经过三节课的学习，近五次模考平均分依然没有达到80分，建议申请主管老师介入，调整学员教学方案后再继续排课。',
						type: MESSAGE_TYPE.warning,
						confirmText: '继续排课',
						cancelText: '取消排课'
					});
					if (!isConfirm) {
						return;
					}
				}

				try {
					state.loading = true;
					let params = cloneByMerge(state.formState, DefaultFormState);
					const fetchStore = BatchCreateStore;

					let { subject, recommendSprint } = params;
					let [beginTime, endTime] = state.formState.rangeTime;
					beginTime = methods.formatter(beginTime);
					beginTime = +new Date(`${state.date} ${beginTime}`);
					endTime = methods.formatter(endTime);
					endTime = +new Date(`${state.date} ${endTime}`);
					const courses = [
						{
							beginTime,
							endTime,
							subject,
							recommendSprint
						}
					];
					params.courses = JSON.stringify(courses);
					delete params.rangeTime;

					await fetchStore.request(params).getData();

					emit('refresh');
					state.visible = false;
					MUtils.toast('保存成功', MESSAGE_TYPE.success);
				} finally {
					state.loading = false;
				}
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods
		};
	}
});
</script>

<style>
.ant-slider {
	margin-top: 0;
	height: 20px;
}
.ant-slider-rail,
.ant-slider-step,
.ant-slider-track {
	height: 16px;
}
.ant-slider-handle {
	margin-top: -2px;
	width: 20px;
	height: 20px;
}
</style>
