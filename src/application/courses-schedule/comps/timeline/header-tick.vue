<template>
	<div class="progress-tick">
		<div class="item" v-for="item in timeList" :key="item">
			<span>{{ item }}</span>
		</div>
	</div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue';

const timeList = [
	'9:00',
	'10:00',
	'11:00',
	'12:00',
	'13:00',
	'14:00',
	'15:00',
	'16:00',
	'17:00',
	'18:00',
	'19:00',
	'20:00',
	'21:00',
	'22:00',
	'23:00'
];

export default defineComponent({
	props: {},
	setup(props) {
		const components = {};

		const constants = {
			timeList
		};

		const state = reactive({});

		const methods = {};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			...props
		};
	}
});
</script>

<style lang="less">
.progress-tick {
	display: flex;
	overflow: hidden;
	height: 20px;
	background-color: transparent;
	.item {
		float: left;
		padding-left: 4px;
		height: 100%;
		font-size: 12px;
		text-align: left;
		color: #a5a5a5;
		border: 1px solid #a5a5a5;
		border-right: 0;
		transition: width 0.6s ease;
		line-height: 18px;
		flex: 1;
		&:last-child {
			border-right: 1px solid #a5a5a5;
		}
	}
}
</style>
