<template>
	<header-tick-comp v-if="showHeaderTick" />
	<div class="progress-timeline" :class="{ 'progress-disabled': disabled }">
		<div class="progress-bar progress-bar-free" v-for="item in freeList" :key="item.name" :style="item.style">
			<m-tooltip placement="top">
				<template #title>
					<span>
						可排课时间
						<br />
						{{ item.beginTimeStr }}~{{ item.endTimeStr }}
					</span>
				</template>
				<div style="height: 100%"></div>
			</m-tooltip>
		</div>
		<div
			class="progress-bar progress-bar-item"
			v-for="item in appointmentList"
			:key="item.name"
			:style="item.style"
			:class="[
				item.status === 1 ? 'progress-bar-draft' : 'progress-bar-finalize',
				{ 'progress-bar-highlight': editMode }
			]"
		>
			<m-tooltip placement="top">
				<template #title>
					<span>
						{{ formatDate(item.beginTime) }}
						<br />
						{{ item.subject }}({{ item.beginTimeStr }}至{{ item.endTimeStr }})：{{
							STATUS_MAP[item.status]
						}}
					</span>
				</template>
				<div style="height: 100%"></div>
			</m-tooltip>
		</div>
	</div>
</template>

<script lang="ts">
import Dayjs from 'dayjs';
import { PropType, defineComponent, reactive, toRefs, watch } from 'vue';
import { MUtils } from 'admin-library';
import { STATUS_MAP } from '@/application/courses/constant';

import HeaderTickComp from './header-tick.vue';

function dealTimelineData(userDataList, selectDay) {
	userDataList = userDataList.sort((itemA, itemB) => itemA.beginTime > itemB.endTime);
	const initialValue = [];
	const sumWithInitial = userDataList.reduce((accumulator, currentValue) => {
		let len = initialValue.length;
		let lastItem = initialValue[len - 1];
		if (len === 0) {
			initialValue.push(currentValue);
		} else if (lastItem.endTime === currentValue.beginTime && currentValue.endTime) {
			if (Dayjs(currentValue.endTime).format('HH:mm') !== '00:00') {
				lastItem.endTime = currentValue.endTime;
			}
		} else {
			initialValue.push(currentValue);
		}
		return initialValue;
	}, initialValue);
	userDataList = sumWithInitial;

	const selectDayBeginTime = Dayjs(selectDay + ' ' + '09:00').valueOf();
	const selectDayEndTime = Dayjs(selectDay).add(1, 'day').hour(0).valueOf();
	const betweenTime = selectDayEndTime - selectDayBeginTime;
	const list = userDataList.map(item => {
		// const beginTime = +new Date(selectDay + ' ' + item.beginTime);
		// const endTime = +new Date(selectDay + ' ' + item.endTime);
		const beginTimeStr = Dayjs(item.beginTime).format('HH:mm');
		const endTimeStr = Dayjs(item.endTime).format('HH:mm');
		const w = ((item.endTime - item.beginTime) * 100) / betweenTime;
		const l = ((item.beginTime - selectDayBeginTime) * 100) / betweenTime;
		const style = {
			width: w.toFixed(3) + '%',
			left: l.toFixed(3) + '%'
		};

		return { ...item, beginTimeStr, endTimeStr, style };
	});
	return list;
}
const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];

export default defineComponent({
	components: { HeaderTickComp },
	props: {
		courses: {
			type: Array as PropType<any>,
			isRequired: true
		},
		freeDurations: {
			type: Array as PropType<any>,
			isRequired: true
		},
		showHeaderTick: {
			type: Boolean,
			isRequired: false,
			default: false
		},
		selectDay: {
			type: String,
			isRequired: true
		},
		editMode: {
			type: Boolean,
			isRequired: false
		},
		noSchedule: {
			type: Boolean,
			isRequired: false
		}
	},
	setup(props) {
		const components = {};

		const constants = {
			STATUS_MAP
		};

		const state = reactive({
			disabled: false,
			appointmentList: [],
			freeList: []
		});

		const methods = {
			formatDate(date) {
				const dayOfWeek = Dayjs(date).day();
				const chineseWeekday = weekdays[dayOfWeek];

				return Dayjs(date).format('YYYY年M月D日') + `(${chineseWeekday})`;
			}
		};

		watch(
			() => props.noSchedule,
			() => {
				state.disabled = props.noSchedule;
			},
			{
				immediate: true
			}
		);
		watch(
			() => props.courses,
			() => {
				state.appointmentList = dealTimelineData(MUtils.deepClone(props?.courses || []), props.selectDay);
			},
			{
				immediate: true,
				deep: true
			}
		);
		watch(
			() => props.freeDurations,
			() => {
				state.freeList = dealTimelineData(MUtils.deepClone(props?.freeDurations || []), props.selectDay);
			},
			{
				immediate: true,
				deep: true
			}
		);

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			...props
		};
	}
});

export const timelineDesc = `(橙色<span style="display: inline-block; width: 12px; height: 12px; background: rgba(250, 205, 145, 0.5)"></span>：讲师声明可排课时间；浅蓝色<span style="display: inline-block; width: 12px; height: 12px; background: #b3d9e9"></span>：草稿状态课程安排；深蓝色<span style="display: inline-block; width: 12px; height: 12px; background: #8ab9e6"></span>：为其他状态课程安排；灰色<span style="display: inline-block; width: 12px; height: 12px; background: #d7d7d7"></span>：讲师声明全天没空)`;
</script>

<style lang="less">
.progress-timeline {
	position: relative;
	display: flex;
	overflow: hidden;
	margin: 2px 0;
	height: 28px;
	background-color: transparent;
	&.progress-disabled {
		background-color: #d7d7d7;
	}
	.progress-bar {
		position: absolute;
		overflow: hidden;
		width: 0;
		height: 100%;
	}
	.progress-bar-item {
		border-radius: 28px;
	}
}

.progress-bar-draft {
	background-color: #d4eef9;
}
.progress-bar-finalize {
	background-color: #8ab9e6;
}
.progress-bar-free {
	background-color: rgba(250, 205, 145, 0.35);
}
.progress-bar-highlight {
	background-color: #d9534f !important;
}
</style>
