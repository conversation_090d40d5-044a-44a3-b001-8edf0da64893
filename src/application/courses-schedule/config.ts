import { ColumnXtype, TableColumn } from 'admin-library';
import { ColumnWidthEnum } from '@/shard/constant';
import { STATUS_MAP } from '@/application/promoter/constant';
import { LAYER_MAP, TYPE_MAP } from '@/application/part-time-teacher/constant';

export const COLUMNS: TableColumn[] = [
	{
		title: '讲师',
		dataIndex: 'lecturerName',
		render: (value, lineData) => {
			return `${value}（${lineData.lecturerNickName}）`;
		},
		width: 160
	},
	{
		title: '类型',
		dataIndex: 'lecturerType',
		render: data => {
			return TYPE_MAP[data];
		},
		width: ColumnWidthEnum.TEXT3
	},
	{
		title: '等级',
		dataIndex: 'lecturerLevelName',
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '层级',
		dataIndex: 'lecturerLayer',
		render: data => {
			return LAYER_MAP[data];
		},
		width: ColumnWidthEnum.TEXT3
	},
	{
		title: '上岗状态',
		dataIndex: 'lectureStatus',
		render: data => {
			return STATUS_MAP[data];
		},
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '时间轴',
		dataIndex: 'timeline',
		width: 'max',
		xtype: ColumnXtype.CUSTOM
	}
];
