<template>
	<pm-effi :controller="controller">
		<pm-search>
			<pm-search-single
				:span="3"
				:model-value="2"
				:antdProps="{
					placeholder: '讲师类型'
				}"
				data-index="type"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '等级',
					fieldNames: { label: 'levelName', value: 'levelValue' }
				}"
				data-index="levelValue"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '层级'
				}"
				data-index="layer"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="3"
				:model-value="20"
				:antdProps="{
					placeholder: '上岗状态'
				}"
				data-index="status"
				xtype="SELECT"
			/>
			<pm-search-single v-model="dateRange" :span="4" xtype="DATEPICKER" data-index="date" />

			<pm-search-single :span="6" label="已通过技能">
				<template #custom>
					<pm-search-single
						:antdProps="{
							placeholder: '车型'
						}"
						data-index="carType"
						xtype="SELECT"
					/>
					<pm-search-single
						:antdProps="{
							placeholder: '科目'
						}"
						data-index="tutorKemu"
						xtype="SELECT"
					/>
				</template>
			</pm-search-single>
		</pm-search>

		<pm-table
			:columns="COLUMNS"
			:use-custom-column="true"
			:pagination="false"
			:operations-fixed="true"
			:operations-width="70"
			:sort-num="false"
			:scroll="{
				x: 'max-content'
			}"
			class="table-no-hover"
		>
			<template #headerCell="{ column }">
				<template v-if="column.key === 'timeline'">
					{{ column.title }}
					<span v-html="timelineDesc"></span>
					<br />
					<header-tick-comp />
				</template>
			</template>
			<template #timeline="{ record }">
				<Timeline
					:courses="record.courses"
					:freeDurations="record.freeDurations"
					:noSchedule="record.noSchedule"
					:select-day="formater(dateRange)"
				/>
			</template>
			<!-- <template #operations="{ record }">
				<m-button type="link" @click="onCreate(record)">排课</m-button>
			</template> -->
		</pm-table>
	</pm-effi>

	<edit-comp ref="editRef" @refresh="onRefresh"></edit-comp>
</template>

<script lang="ts">
import Dayjs from 'dayjs';
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { ModelController, YEAR_TIME_FORMAT } from 'admin-library';

import HeaderTickComp from './comps/timeline/header-tick.vue';
import Timeline, { timelineDesc } from './comps/timeline/index.vue';
import EditComp from './comps/edit-dialog/index.vue';
import { COLUMNS } from './config';
import { CAR_TYPE_STORE, EXAM_KEMU_STORE } from '@/shard/constant';
import { TYPE_STORE, LAYER_STORE } from '@/application/part-time-teacher/constant';
import { STATUS_STORE } from '@/application/promoter/constant';
import { ListStore as levelListStore } from '@/application/teacher-level-manage/store';
import { ListStore } from './store';
import { ItemResponse } from './types';

export default defineComponent({
	components: { HeaderTickComp, Timeline, EditComp },
	setup() {
		const components = {
			editRef: ref<InstanceType<typeof EditComp>>()
		};

		const constants = {
			COLUMNS,
			timelineDesc
		};

		const state = reactive({
			dateRange: Dayjs()
		});

		const controller = new ModelController({
			table: {
				store: ListStore
			},
			search: {
				type: {
					store: TYPE_STORE
				},
				levelValue: {
					store: levelListStore
				},
				layer: {
					store: LAYER_STORE
				},
				status: {
					store: STATUS_STORE
				},
				carType: {
					store: CAR_TYPE_STORE
				},
				tutorKemu: {
					store: EXAM_KEMU_STORE
				}
			}
		});
		controller.tableRequest();

		controller.table.onRequest.use(params => {
			let sd = {};
			if (state.dateRange) {
				sd = { date: Dayjs(state.dateRange).format(YEAR_TIME_FORMAT) };
			}

			params = {
				...params,
				...sd
			};

			return params;
		});

		controller.search.levelValue.onRequest.use(params => {
			params = {
				...params,
				limit: 9999
			};

			return params;
		});

		const methods = {
			formater(data) {
				return Dayjs(data).format(YEAR_TIME_FORMAT);
			},
			onCreate(row: ItemResponse) {
				components.editRef.value.open(row, Dayjs(state.dateRange).format(YEAR_TIME_FORMAT));
			},

			onRefresh() {
				controller.tableRequest();
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			controller
		};
	}
});
</script>
