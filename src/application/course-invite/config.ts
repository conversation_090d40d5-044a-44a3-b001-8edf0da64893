import { TableColumn, TableDateFormat } from 'admin-library';
import { formatTimeStr } from '@/shard/utils';
import { ColumnWidthEnum } from '@/shard/constant';
import { CLOSE_REASON_MAP, INVITE_TYPE_MAP, ORIGIN_TYPE_MAP, STATUS_MAP } from './constant';

export const COLUMNS: TableColumn[] = [
	{
		title: '#',
		dataIndex: 'id'
	},
	{
		title: '类型',
		dataIndex: 'inviteType',
		render: data => {
			return INVITE_TYPE_MAP[data];
		},
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '学员ID',
		dataIndex: 'studentId',
		width: ColumnWidthEnum.TEXT5
	},
	{
		title: '学员姓名',
		dataIndex: 'studentName'
	},
	{
		title: '督导',
		dataIndex: 'supervisorName'
	},
	{
		title: '拟上课时间',
		dataIndex: 'courseTime',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	},
	{
		title: '发起人类型',
		dataIndex: 'createOrigin',
		render: data => {
			return ORIGIN_TYPE_MAP[data];
		},
		width: ColumnWidthEnum.TEXT5
	},
	{
		title: '发起人',
		dataIndex: 'startUserName',
		render: (value, lineData) => {
			if (lineData.createOrigin === 0) {
				return lineData.studentName;
			} else {
				return lineData.createUserName;
			}
		},
		width: ColumnWidthEnum.TEXT6
	},
	{
		title: '状态',
		dataIndex: 'inviteStatus',
		render: data => {
			return STATUS_MAP[data];
		},
		width: 150
	},
	{
		title: '关闭原因',
		dataIndex: 'closeReason',
		render: data => {
			return CLOSE_REASON_MAP[data];
		},
		width: 150
	},
	{
		title: '接受人',
		dataIndex: 'lectureMixName',
		width: 200
	},
	{
		title: '发起时间',
		dataIndex: 'createTime',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	},
	{
		title: '邀请关闭时间',
		dataIndex: 'inviteCloseTime',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	},
	{
		title: '耗时',
		dataIndex: 'period',
		render: (data, lineData) => {
			if (lineData.createTime && lineData.inviteCloseTime) {
				return formatTimeStr(lineData.inviteCloseTime - lineData.createTime, 'mm:ss');
			}
		},
		width: ColumnWidthEnum.TEXT6
	}
];
