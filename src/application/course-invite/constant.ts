import { getMapfromArray, getStorefromArray } from '@/shard/utils';

export const INVITE_TYPE_OPTIONS = [
	{
		label: '定向',
		value: 1
	},
	{
		label: '招募',
		value: 2
	}
];
export const INVITE_TYPE_MAP = getMapfromArray(INVITE_TYPE_OPTIONS);
export const INVITE_TYPE_STORE = getStorefromArray(INVITE_TYPE_OPTIONS);

export const STATUS_OPTIONS = [
	{
		label: '开放中-无人接受',
		value: 10
	},
	{
		label: '已关闭-有人接受',
		value: 20
	},
	{
		label: '已关闭-无人接受',
		value: 21
	}
];
export const STATUS_MAP = getMapfromArray(STATUS_OPTIONS);
export const STATUS_STORE = getStorefromArray(STATUS_OPTIONS);

export const ORIGIN_TYPE_OPTIONS = [
	{
		label: '学员',
		value: 0
	},
	{
		label: '管理后台',
		value: 1
	}
];
export const ORIGIN_TYPE_MAP = getMapfromArray(ORIGIN_TYPE_OPTIONS);

export const CLOSE_REASON_OPTIONS =[
	{
		label: '人工强制关闭',
		value: 1
	},
	{
		label: '无可推荐讲师',
		value: 2
	},
	{
		label: '无人接受超时',
		value: 3
	},
	{
		label: '学员主动取消',
		value: 4
	}
]
export const CLOSE_REASON_MAP = getMapfromArray(CLOSE_REASON_OPTIONS);
export const CLOSE_REASON_STORE = getStorefromArray(CLOSE_REASON_OPTIONS);