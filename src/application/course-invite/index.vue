<template>
	<pm-effi :controller="controller">
		<pm-search>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '邀约ID'
				}"
				data-index="id"
				xtype="INPUT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '学员ID'
				}"
				data-index="studentId"
				xtype="INPUT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '学员姓名'
				}"
				data-index="studentName"
				xtype="INPUT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '类型'
				}"
				data-index="inviteType"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '状态'
				}"
				data-index="inviteStatus"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '关闭原因'
				}"
				data-index="closeReason"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="5"
				:antd-props="{
					placeholder: ['发起时间', '结束时间']
				}"
				xtype="RANGEPICKER"
				data-index="startTime|endTime"
			/>
		</pm-search>
		<pm-table
			:columns="COLUMNS"
			:use-custom-column="true"
			:operations-fixed="true"
			:operations-width="130"
			:sort-num="false"
			:scroll="{
				x: 'max-content'
			}"
		>
			<!-- <template #beforeCustom>
				<m-button type="primary" @click="onCreate()">代学员约课</m-button>
			</template> -->
			<template #operations="{ record }">
				<m-button type="link" @click="onView(record)">明细</m-button>
				<m-button type="link" danger @click="onCancel(record)" v-if="record.inviteStatus === 10">
					强制关闭
				</m-button>
			</template>
		</pm-table>
	</pm-effi>

	<edit-comp ref="editRef" @refresh="onRefresh"></edit-comp>
</template>
<script lang="ts">
import { defineComponent, ref, reactive, toRefs } from 'vue';
import { ModelController, PaasPostMessage, MESSAGE_TYPE, MUtils } from 'admin-library';

import EditComp from './comps/edit-dialog/index.vue';
import { confirmMessageBox } from '@/shard/utils';
import { COLUMNS } from './config';
import { CLOSE_REASON_STORE, INVITE_TYPE_STORE, STATUS_STORE } from './constant';
import { ListStore, CancelStore } from './store';
import { ItemResponse } from './types';

export default defineComponent({
	components: {
		EditComp
	},
	setup() {
		const components = {
			editRef: ref<InstanceType<typeof EditComp>>()
		};

		const constants = {
			COLUMNS,
			APP
		};

		const state = reactive({});

		const controller = new ModelController({
			table: {
				store: ListStore
			},
			search: {
				inviteType: {
					store: INVITE_TYPE_STORE
				},
				inviteStatus: {
					store: STATUS_STORE
				},
				closeReason: {
					store: CLOSE_REASON_STORE
				}
			}
		});
		controller.tableRequest();

		const methods = {
			onCreate() {
				components.editRef.value.open();
			},
			onView(row: ItemResponse) {
				PaasPostMessage.post('navigation.to', '/#/course-invite-detail', {
					title: '邀约明细',
					query: {
						inviteId: row?.id
					},
					extendData: {
						style: 'width: 780px; height: 90%; margin: 2% auto 0;'
					},
					target: '_popup'
				});
			},
			async onCancel(row: ItemResponse) {
				await confirmMessageBox('确定要强制关闭这个招募吗？');

				await CancelStore.request({ id: row.id }).getData();
				methods.onRefresh();
				MUtils.toast('关闭成功', MESSAGE_TYPE.success);
			},
			onRefresh() {
				controller.tableRequest();
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			controller
		};
	}
});
</script>
