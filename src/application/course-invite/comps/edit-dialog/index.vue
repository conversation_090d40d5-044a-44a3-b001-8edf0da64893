<template>
	<pm-dialog v-model:visible="visible" :title="title" width="680px">
		<template v-if="visible">
			<m-form
				ref="formRef"
				:model="formState"
				:colon="false"
				:label-col="{ style: { width: '95px' } }"
				autocomplete="off"
			>
				<m-form-item label="类型" name="inviteType">
					<m-select disabled v-model:value="formState.inviteType" :options="options.typeList" />
				</m-form-item>
				<m-form-item label="学员姓名" name="sno" required>
					<student-select :disabled="!!sno" v-model:value="formState.sno" data-index="sno" />
				</m-form-item>
				<m-form-item label="上课日期" name="appointmentDate" required>
					<m-radio-group
						v-model:value="formState.appointmentDate"
						:options="options.dateList"
					></m-radio-group>
				</m-form-item>
				<div class="prompt">至多选择 3 个时间，若最早的时间失败，将由早到晚自动发起下一个时间的招募</div>
				<m-form-item label="上课时间" name="appointmentTimeSpan" :rules="appointmentTimeSpan">
					<m-checkbox-group v-model:value="formState.appointmentTimeSpan">
						<m-checkbox
							style="margin-left: 0; width: 120px"
							v-for="time in options.timeList"
							:key="time"
							:value="time"
						>
							{{ time }}
						</m-checkbox>
					</m-checkbox-group>
				</m-form-item>
			</m-form>
		</template>

		<template #footer>
			<m-button plain @click="visible = false">取消</m-button>
			<m-button :loading="loading" type="primary" @click="onConfirm">确定</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { MESSAGE_TYPE, MUtils } from 'admin-library';
import type { Rule } from 'ant-design-vue/es/form';

import StudentSelect from '@/comp/student-select/index.vue';
import { cloneFromPick, cloneByMerge, formatDate } from '@/shard/utils';
import { CreateStore } from '../../store';
import { INVITE_TYPE_OPTIONS } from '../../constant';

function getDayChinese(data) {
	const now = +new Date();
	const today = formatDate(now, 'YYYY-MM-DD');
	const tomorrow = formatDate(now + oneDayTime * 1, 'YYYY-MM-DD');
	const overmorrow = formatDate(now + oneDayTime * 2, 'YYYY-MM-DD');
	const targetDate = formatDate(data, 'YYYY-MM-DD');
	if (today === targetDate) {
		return '今天';
	} else if (tomorrow === targetDate) {
		return '明天';
	} else if (overmorrow === targetDate) {
		return '后天';
	} else {
		return '';
	}
}
const oneHoursTime = 1000 * 60 * 60;
const oneDayTime = oneHoursTime * 24;
function getDateList(today) {
	const dateList = [today, today + oneDayTime * 1, today + oneDayTime * 2].map(item => {
		return {
			label: `${getDayChinese(item)}(${formatDate(item, 'M月D日')})`,
			value: formatDate(item, 'YYYY-MM-DD')
		};
	});
	return dateList;
}
const timeList = [
	'09:00',
	'09:30',
	'10:00',
	'10:30',
	'11:00',
	'11:30',
	'12:00',
	'12:30',
	'13:00',
	'13:30',
	'14:00',
	'14:30',
	'15:00',
	'15:30',
	'16:00',
	'16:30',
	'17:00',
	'17:30',
	'18:00',
	'18:30',
	'19:00',
	'19:30',
	'20:00',
	'20:30',
	'21:00',
	'21:30',
	'22:00',
	'22:30',
	'23:00'
];
const DefaultFormState = {
	inviteType: 2,
	sno: '',
	appointmentDate: '',
	appointmentTimeSpan: ''
};

export default defineComponent({
	components: {
		StudentSelect
	},
	emits: ['refresh'],
	setup(props, { emit }) {
		const components = {
			formRef: ref(null)
		};

		const constants = {
			appointmentTimeSpan: {
				validator: (_rule: Rule, value: string[]) => {
					if (value.length > 3) {
						return Promise.reject('最多选择3个时间');
					} else if (value.length <= 0) {
						return Promise.reject('最少选择1个时间');
					}
					return Promise.resolve();
				}
			}
		};

		const state = reactive({
			visible: false,
			title: '代学员约课',
			loading: false,
			formState: MUtils.deepClone(DefaultFormState),
			sno: '',
			options: {
				typeList: INVITE_TYPE_OPTIONS,
				dateList: [],
				timeList
			}
		});

		const methods = {
			open(row?: any) {
				state.formState = MUtils.deepClone(DefaultFormState);

				if (row) {
					state.formState = cloneFromPick(row, DefaultFormState);
					state.sno = row.sno;
				}

				state.visible = true;

				state.options.dateList = getDateList(+new Date());
			},
			async onConfirm() {
				await components.formRef.value.validate();

				try {
					state.loading = true;
					const params = cloneByMerge(state.formState, DefaultFormState);
					const fetchStore = CreateStore;
					params.appointmentTimeSpan.sort((a, b) => {
						// 将时间字符串转换为分钟数进行比较
						const aMinutes = parseInt(a.split(':')[0]) * 60 + parseInt(a.split(':')[1]);
						const bMinutes = parseInt(b.split(':')[0]) * 60 + parseInt(b.split(':')[1]);
						return aMinutes - bMinutes;
					});
					params.alternateAppointmentTimeSpan = params.appointmentTimeSpan.slice(1);
					params.appointmentTimeSpan = params.appointmentTimeSpan[0];
					await fetchStore.request(params).getData();
					emit('refresh');
					state.visible = false;
					MUtils.toast('保存成功', MESSAGE_TYPE.success);
				} finally {
					state.loading = false;
				}
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods
		};
	}
});
</script>
<style lang="less">
.prompt {
	margin-left: 20px;
	color: #f40;
}
</style>
