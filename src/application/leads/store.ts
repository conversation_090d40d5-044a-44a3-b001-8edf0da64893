import { List, Update, ChangeBind, GetWecomTag, viewSummary as viewLeadsSummary } from '@/store/leads';
import { viewSummary } from '@/store/student';

import { ItemResponse } from './types';

export const ListStore = new List<Array<ItemResponse>>({});
export const UpdateStore = new Update({});

export const ChangeBindStore = new ChangeBind({});
export const GetWecomTagStore = new GetWecomTag({});

export const viewSummaryStore = new viewSummary<ItemResponse>({});
export const viewLeadsSummaryStore = new viewLeadsSummary<ItemResponse>({});
