<template>
	<pm-dialog v-model:visible="visible" title="学习概况" width="480px">
		<table style="width: 100%" class="table1">
			<thead>
				<tr>
					<td class="label-name">VIP类型</td>
					<td class="">{{ examInfo.vipKemuList }}</td>
				</tr>
				<tr>
					<td class="label-name">挂科次数</td>
					<td class="">{{ examInfo.flunkCount }}</td>
				</tr>
				<tr>
					<td class="label-name">累计做题数</td>
					<td class="">{{ examInfo.totalExercisesCount }}</td>
				</tr>
				<tr>
					<td class="label-name">累计模考次数</td>
					<td class="">{{ examInfo.totalMockExamCount }}</td>
				</tr>
				<tr>
					<td class="label-name">错题本</td>
					<td class="">{{ examInfo.wrongQuestionBook }}</td>
				</tr>
				<tr>
					<td class="label-name">近五次模考平均分</td>
					<td class="">
						{{ examInfo.mockExamAvgScore }}
						<template v-if="examInfo.mockExamAvgScore">
							<span v-if="examInfo.mockExamAvgScoreChange > 0" style="font-size: 11px" class="danger">
								<caret-up-outlined />
								{{ examInfo.mockExamAvgScoreChange }}
							</span>
							<span
								v-else-if="examInfo.mockExamAvgScoreChange < 0"
								style="font-size: 11px"
								class="success"
							>
								<caret-down-outlined />
								{{ Math.abs(examInfo.mockExamAvgScoreChange) }}
							</span>
							<span v-else style="font-size: 11px" class="info">
								<minus-outlined />
							</span>
						</template>
					</td>
				</tr>
				<tr>
					<td class="label-name">预测通过率</td>
					<td class="">{{ examInfo.passRatePrediction }}</td>
				</tr>
			</thead>
		</table>
		<template #footer>
			<m-button type="primary" @click="visible = false">确定</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import Dayjs from 'dayjs';
import { defineComponent, reactive, toRefs } from 'vue';
import { CaretUpOutlined, CaretDownOutlined, MinusOutlined } from '@ant-design/icons-vue';

import { VIP_TYPE_MAP } from '../../constant';
import { viewLeadsSummaryStore, viewSummaryStore } from '../../store';

export default defineComponent({
	components: { CaretUpOutlined, CaretDownOutlined, MinusOutlined },
	setup() {
		const state = reactive({
			visible: false,
			examInfo: {} as any,
			studentProfileId: ''
		});

		const methods = {
			async viewCustomer(row: any) {
				state.examInfo = {};
				state.studentProfileId = row.studentProfileId;
				state.visible = true;

				let studySummary = {} as any;
				if (row.studentProfileId) {
					const res: any = await viewSummaryStore.request({ id: row.studentProfileId }).getData();
					studySummary = res;
				} else {
					const res: any = await viewLeadsSummaryStore.request({ leadsNo: row.leadsNo }).getData();
					studySummary = res;
				}

				if (studySummary.reserveExamTime) {
					studySummary.reserveExamTime = Dayjs(studySummary.reserveExamTime).format('YYYY-MM-DD');
				}

				if (studySummary.passRatePrediction > 0) {
					studySummary.passRatePrediction = studySummary.passRatePrediction + '%';
				}

				if (studySummary.vipKemuList && studySummary.vipKemuList.length) {
					if (studySummary.vipKemuList.indexOf(0) > -1) {
						studySummary.vipKemuList = [0];
					}
					studySummary.vipKemuList = studySummary.vipKemuList
						.map(v => {
							return VIP_TYPE_MAP[v];
						})
						.join('，');
				} else {
					studySummary.vipKemuList = '';
				}
				state.examInfo = studySummary;
			}
		};

		return {
			...toRefs(state),
			...methods
		};
	}
});
</script>

<style lang="less" scoped>
.table1 {
	width: 100%;
	td {
		padding: 5px 0 5px 10px;
		color: rgba(0, 0, 0, 0.85);
		border: 1px solid rgba(0, 0, 0, 0.06);
	}
	.label-name {
		width: 130px;
		background-color: #e9f1fe;
	}
}
</style>
