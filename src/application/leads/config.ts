import { ColumnXtype, TableColumn, TableDateFormat } from 'admin-library';
import { renderCopyComp } from '@/shard/utils';
import { ColumnWidthEnum, CAR_TYPE_MAP, TUROR_KEMU_MAP } from '@/shard/constant';
import { CATEGORY_MAP, CHANNEL_MAP } from './constant';

export const COLUMNS: TableColumn[] = [
	{
		title: '线索编码',
		dataIndex: 'leadsNo',
		fixed: 'left'
	},
	{
		title: '木仓昵称',
		dataIndex: 'mucangNickName',
		fixed: 'left'
	},
	{
		title: '木仓Id',
		dataIndex: 'mucangId',
		render: data => {
			return renderCopyComp(data);
		},
		width: ColumnWidthEnum.TEXT3
	},
	{
		title: '微信昵称',
		dataIndex: 'wechatNickName'
	},
	{
		title: '客户姓名',
		dataIndex: 'name',
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '车型',
		dataIndex: 'tiku',
		render: data => {
			return CAR_TYPE_MAP[data];
		},
		width: ColumnWidthEnum.TEXT5
	},
	{
		title: '辅导科目',
		dataIndex: 'tutorKemu',
		render: data => {
			return TUROR_KEMU_MAP[data];
		},
		width: ColumnWidthEnum.TEXT5
	},
	{
		title: '学习概况',
		dataIndex: 'abstract',
		xtype: ColumnXtype.CUSTOM,
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '预约考试时间',
		dataIndex: 'reserveExamTime',
		xtype: ColumnXtype.CUSTOM,
		width: ColumnWidthEnum.TEXT6
	},
	{
		title: '用户分层',
		dataIndex: 'category',
		render: data => {
			return CATEGORY_MAP[data];
		},
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '客户来源渠道',
		dataIndex: 'leadsChannel',
		render: data => {
			return CHANNEL_MAP[data];
		},
		width: ColumnWidthEnum.TEXT6
	},
	{
		title: 'IM进线时间',
		dataIndex: 'imEntryTime',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	},
	{
		title: '企微标签',
		dataIndex: 'wecomTag',
		xtype: ColumnXtype.CUSTOM,
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '企微添加时间',
		dataIndex: 'weixinAddTime',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	},
	{
		title: '销售人员',
		dataIndex: 'saleEmployeeName',
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '创建时间',
		dataIndex: 'createTime',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	}
];
