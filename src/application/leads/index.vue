<template>
	<pm-effi :controller="controller">
		<pm-search>
			<pm-search-single
				:span="5"
				:antd-props="{
					placeholder: ['创建时间', '结束时间']
				}"
				:model-value="defaultTime"
				xtype="RANGEPICKER"
				data-index="createTimeFrom|createTimeTo"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '微信昵称'
				}"
				data-index="wechatNickName"
				xtype="INPUT"
			/>
			<pm-search-single
				:model-value="mucangId"
				:span="3"
				:antdProps="{
					placeholder: '木仓Id'
				}"
				data-index="mucangId"
				xtype="INPUT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '线索编码'
				}"
				data-index="leadsNo"
				xtype="INPUT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '客户姓名'
				}"
				data-index="name"
				xtype="INPUT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '车型'
				}"
				data-index="tiku"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '辅导科目'
				}"
				data-index="tutorKemu"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '客户来源渠道'
				}"
				data-index="leadsChannel"
				xtype="SELECT"
			/>
			<!-- <pm-search-single
				:span="3"
				:antdProps="{
					mode: 'multiple',
					placeholder: '用户分层'
				}"
				data-index="categories"
				xtype="SELECT"
			/> -->
			<pm-search-single
				:span="3"
				show-search
				optionFilterProp="employeeMixName"
				:antdProps="{
					placeholder: '销售',
					fieldNames: { label: 'employeeMixName', value: 'id' }
				}"
				data-index="saleEmployeeId"
				xtype="SELECT"
			/>
			<!-- <pm-search-single
				:span="6"
				xtype="CASCADER"
				data-index="cityCode"
				:antd-props="{
					'max-tag-count': 'responsive',
					showSearch: true,
					fieldNames: { label: 'name', value: 'adcode', children: 'districts' },
					placeholder: '所属省市区'
				}"
			/> -->
		</pm-search>

		<pm-table
			:columns="COLUMNS"
			:use-custom-column="true"
			:operations-fixed="true"
			:operations-width="120"
			:sort-num="false"
			:scroll="{
				x: 'max-content'
			}"
			:antd-props="{
				customRow: record => {
					return {
						onClick: () => {
							onClick(record);
						}
					};
				}
			}"
			:rowClassName="setInstallClassName"
		>
			<template #abstract="{ record }">
				<m-button type="link" v-if="record.userNo" @click="viewCustomer(record)">查看</m-button>
			</template>
			<template #wecomTag="{ record }">
				<m-button type="link" v-if="record.leadsChannel === 2" @click="viewWecomTag(record)">查看</m-button>
			</template>
			<template #reserveExamTime="{ record }">
				<div v-if="record.reserveExamTime">
					{{ formatDate(record.reserveExamTime, TableDateFormat.DAY) }}
				</div>
				<m-button type="link" v-else-if="record.userNo" @click="onRealExam(record)">添加</m-button>
			</template>

			<template #operations="{ record }">
				<m-button type="link" v-if="record.mucangId" @click="onEditMucangid(record)">改绑</m-button>
				<m-button type="link" @click="onEdit(record)">编辑</m-button>
			</template>
		</pm-table>
	</pm-effi>

	<edit-comp ref="editRef" @refresh="onRefresh"></edit-comp>
	<view-comp ref="viewRef"></view-comp>
	<edit-mucangid-comp ref="editMucangidRef" @refresh="onRefresh"></edit-mucangid-comp>
</template>

<script lang="ts">
import Dayjs from 'dayjs';
import { defineComponent, reactive, toRefs, ref, shallowRef } from 'vue';
import { ModelController, MUtils, PaasPostMessage, TableDateFormat } from 'admin-library';
import { useRoute } from 'vue-router';
import { Store } from '@simplex/simple-store';
import { recentWeek } from '@paas/paas-library';

import EditComp from './comps/edit-dialog/index.vue';
import ViewComp from './comps/view-dialog/index.vue';
import EditMucangidComp from './comps/edit-mucangid-dialog/index.vue';
import { getCityJsonData, CityLevelEnum } from '@/shard/city';
import { COLUMNS } from './config';
import { CAR_TYPE_STORE, EXAM_KEMU_STORE } from '@/shard/constant';
import { CATEGORY_STORE, CHANNEL_STORE } from './constant';
import { ListStore as EmployeeListStore } from '@/application/promoter/store';
import { ListStore, GetWecomTagStore } from './store';
import { ItemResponse } from './types';

export default defineComponent({
	components: {
		EditComp,
		ViewComp,
		EditMucangidComp
	},
	setup() {
		const route = useRoute();
		const query = route.query;
		const components = {
			editRef: ref<InstanceType<typeof EditComp>>(),
			viewRef: ref<InstanceType<typeof ViewComp>>(),
			editMucangidRef: ref<InstanceType<typeof EditMucangidComp>>()
		};

		const constants = {
			COLUMNS,
			isOnline: APP.isOnline,
			TableDateFormat
		};

		const state = reactive({
			selectedId: '',
			defaultTime: !query.mucangId ? recentWeek().map(date => Dayjs(date)) : [],
			mucangId: query.mucangId
		});

		const controller = new ModelController({
			table: {
				store: ListStore
			},
			search: {
				tiku: {
					store: CAR_TYPE_STORE
				},
				tutorKemu: {
					store: EXAM_KEMU_STORE
				},
				leadsChannel: {
					store: CHANNEL_STORE
				},
				categories: {
					store: CATEGORY_STORE
				},
				saleEmployeeId: {
					store: EmployeeListStore
				},
				cityCode: {
					store: new Store([])
				}
			}
		});
		const cityData = shallowRef([]);

		controller.tableRequest();

		controller.table.onRequest.use(params => {
			const { cityCode } = params;

			return {
				...params,
				cityCode: cityCode ? cityCode[cityCode.length - 1] : null
			};
		});

		controller.search.saleEmployeeId.onRequest.use(params => {
			params = {
				...params,
				station: 1,
				limit: 9999
			};

			return params;
		});

		const methods = {
			formatDate(date, template) {
				return Dayjs(date).format(template);
			},
			onClick(recored) {
				state.selectedId = recored.leadsNo;
			},
			setInstallClassName(record) {
				if (record.leadsNo === state.selectedId) {
					return 'selected-row';
				}
			},
			async viewCustomer(row: ItemResponse) {
				components.viewRef.value.viewCustomer({ leadsNo: row.leadsNo });
			},
			async viewWecomTag(row: ItemResponse) {
				const res: any = await GetWecomTagStore.request({ leadsNo: row.leadsNo }).getData();
				let contentStr = '--';
				if (res.tagNames.length) {
					contentStr = res.tagNames
						.map(item => {
							return `<span style="box-sizing: border-box;display: inline-block;white-space: nowrap;padding: 3px 7px;margin-top: 8px;margin-right: 8px;min-width: 96px;text-align: center;color: #0081ff;background-color: #c5e8ff;">${item}</span>`;
						})
						.join('');
				}
				MUtils.alert({
					title: '企微标签',
					content: contentStr
				});
			},
			onRealExam(row: ItemResponse) {
				PaasPostMessage.post('navigation.to', '/#/real-exam-records', {
					title: '学员真实考试记录',
					query: {
						customerNo: row?.userNo,
						carType: row?.tiku,
						kemu: row?.tutorKemu
					},
					extendData: {
						style: 'width: 900px; height: 90%; margin: 2% auto 0;'
					},
					target: '_popup'
				});
			},

			onEdit(row: ItemResponse) {
				components.editRef.value.open(row);
			},
			onEditMucangid(row: ItemResponse) {
				components.editMucangidRef.value.open(row);
			},

			onRefresh() {
				controller.tableRequest();
			},
			async getCityJson() {
				const jsonData = await getCityJsonData(CityLevelEnum.district);

				const list = jsonData?.districts[0]?.districts || [];

				cityData.value = list;

				controller.search.cityCode.updateStore(new Store(list));
			}
		};
		// methods.getCityJson();

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			controller
		};
	}
});
</script>
