<template>
	<pm-dialog v-model:visible="visible" :title="title" width="680px">
		<template v-if="visible">
			<m-form
				ref="formRef"
				:model="formState"
				:colon="false"
				:label-col="{ style: { width: '95px' } }"
				autocomplete="off"
			>
				<m-form-item v-if="!formState.id" label="账号类型" name="accountType" required>
					<m-select
						v-model:value="formState.accountType"
						:options="options.accountTypeList"
						:allowClear="true"
						placeholder="账号类型"
					/>
				</m-form-item>
				<m-form-item label="头像" name="avatarEncodedData" required>
					<fileUploadComp fileType="image" v-model:value="formState.avatarEncodedData" />
				</m-form-item>
				<m-form-item v-if="formState.accountType === 2" label="手机号" name="phone" required :rules="phoneNumber">
					<m-input v-model:value="formState.phone" maxlength="11" :rows="2" />
				</m-form-item>
				<m-form-item label="备注" name="remark" required>
					<m-textarea v-model:value="formState.remark" :rows="2" />
				</m-form-item>
			</m-form>
		</template>

		<template #footer>
			<m-button plain @click="visible = false">取消</m-button>
			<m-button :loading="loading" type="primary" @click="onConfirm">确定</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { MESSAGE_TYPE, MUtils } from 'admin-library';
import fileUploadComp from '@/comp/file-upload/index.vue';
import { cloneFromPick, cloneByMerge } from '@/shard/utils';
import { ACCOUNT_TYPE_OPTIONS, STATUS_OPTIONS } from '../../constant';
import { CreateStore, UpdateStore } from '../../store';
import { phoneNumber } from '@/shard/validate';

const DefaultFormState = {
	id: null,
	accountCode: '',
	accountType: null,
	phone: '',
	remark: '',
	avatarEncodedData: '',
	accountBizType: 1
};

export default defineComponent({
	emits: ['refresh'],
	components: {
		fileUploadComp
	},
	setup(props, { emit }) {
		const components = {
			formRef: ref(null)
		};

		const constants = {
			phoneNumber
		};

		const state = reactive({
			visible: false,
			title: '',
			loading: false,
			formState: MUtils.deepClone(DefaultFormState),
			options: {
				accountTypeList: ACCOUNT_TYPE_OPTIONS,
				statusList: STATUS_OPTIONS
			}
		});

		const methods = {
			open(row?: any) {
				state.formState = MUtils.deepClone(DefaultFormState);
				state.title = row?.id ? '编辑' : '新增';

				if (row) {
					state.formState = cloneFromPick(row, DefaultFormState);
					state.formState.accountType = row.type;
					state.formState.avatarEncodedData = {
						encodedData: row.avatarEncodedData,
						showUrl: row.avatar
					};
				}

				state.visible = true;
			},
			async onConfirm() {
				await components.formRef.value.validate();

				try {
					state.loading = true;
					const params = cloneByMerge(state.formState, DefaultFormState);
					params.avatarEncodedData = params.avatarEncodedData.encodedData;
					const fetchStore = state.formState.id ? UpdateStore : CreateStore;

					await fetchStore.request(params).getData();

					emit('refresh');
					state.visible = false;
					MUtils.toast('保存成功', MESSAGE_TYPE.success);
				} finally {
					state.loading = false;
				}
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods
		};
	}
});
</script>
