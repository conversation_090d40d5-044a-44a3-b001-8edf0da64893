<template>
	<pm-dialog v-model:visible="visible" :title="title" width="680px">
		<template v-if="visible">
			<m-form
				ref="formRef"
				:model="formState"
				:colon="false"
				:label-col="{ style: { width: '95px' } }"
				autocomplete="off"
			>
				<m-form-item v-if="!formState.id" label="选择员工" name="employeeNo" required>
					<m-select
						v-model:value="formState.employeeNo"
						:options="options.employeeList"
						:allowClear="true"
						:fieldNames="{ label: 'employeeName', value: 'employeeNo' }"
						placeholder="选择员工"
					/>
				</m-form-item>
			</m-form>
		</template>

		<template #footer>
			<m-button plain @click="visible = false">取消</m-button>
			<m-button :loading="loading" type="primary" @click="onConfirm">确定</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { MESSAGE_TYPE, MUtils } from 'admin-library';
import { cloneFromPick, cloneByMerge } from '@/shard/utils';
import { AssignStore } from '../../store';
import { ListStore } from '../../../promoter/store';

const DefaultFormState = {
	accountNo: '',
	employeeNo: null
};
export default defineComponent({
	emits: ['refresh'],
	setup(props, { emit }) {
		const components = {
			formRef: ref(null)
		};

		const constants = {};

		const state = reactive({
			visible: false,
			title: '',
			loading: false,
			formState: MUtils.deepClone(DefaultFormState),
			options: {
				employeeList: []
			}
		});

		const methods = {
			async open(row?: any) {
				const res = await ListStore.request({
					station: 1,
					limit: 9999
				}).getData();
				console.log('employeelist---', res)
				state.options.employeeList = res;

				state.formState = MUtils.deepClone(DefaultFormState);
				state.title = '分配员工';

				if (row) {
					state.formState = cloneFromPick(row, DefaultFormState);
				}

				state.visible = true;
			},
			async onConfirm() {
				await components.formRef.value.validate();

				try {
					state.loading = true;
					const params = cloneByMerge(state.formState, DefaultFormState);
					const fetchStore = AssignStore;

					await fetchStore.request(params).getData();

					emit('refresh');
					state.visible = false;
					MUtils.toast('保存成功', MESSAGE_TYPE.success);
				} finally {
					state.loading = false;
				}
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods
		};
	}
});
</script>
