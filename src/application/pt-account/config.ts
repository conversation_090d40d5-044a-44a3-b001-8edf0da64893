import { ColumnWidthEnum } from '@/shard/constant';
import { renderImageComp } from '@/shard/utils';
import { TableColumn, TableDateFormat } from 'admin-library';
import { ACCOUNT_TYPE_MAP, STATUS_MAP } from './constant';

export const COLUMNS: TableColumn[] = [
	{
		title: '账号',
		dataIndex: 'accountNo',
	},
	
	{
		title: '类型',
		dataIndex: 'type',
		render: (data) => {
			return ACCOUNT_TYPE_MAP[data];
		}
	},
	{
		title: '头像',
		dataIndex: 'avatar',
		render: data => {
			return renderImageComp(data);
		},
		width: 150
	},
	{
		title: '状态',
		dataIndex: 'status',
		render: (data) => {
			return STATUS_MAP[data];
		},
		width: ColumnWidthEnum.TEXT3
	},
	{
		title: '员工编号',
		dataIndex: 'employeeNo',
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '员工姓名',
		dataIndex: 'employeeName',
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '备注',
		dataIndex: 'remark',
		width: ColumnWidthEnum.TEXT3
	},
	{
		title: '创建人',
		dataIndex: 'createUserName',
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '创建时间',
		dataIndex: 'createTime',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	},
	{
		title: '最后更新人',
		dataIndex: 'updateUserName',
		width: ColumnWidthEnum.TEXT5
	},
	{
		title: '最后更新时间',
		dataIndex: 'updateTime',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	}
];
