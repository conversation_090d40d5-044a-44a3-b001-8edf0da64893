// export interface ItemResponse {
//     id: string;
//     accountNo:string;
//     type:number;
//     remark:string;
//     avatar:string;
//     avatarEncodedData:string;
//     status:number;
//     accountBizType:number;
//     employeeNo:string;
//     employeeName:string;
//     createTime:number;
//     createUserId:number;
//     createUserName:string;
//     updateTime:number;
//     updateUserId:number;
//     updateUserName:string;
// }

export interface ItemResponse {
	id: number;
	[key: string]: any;
}
