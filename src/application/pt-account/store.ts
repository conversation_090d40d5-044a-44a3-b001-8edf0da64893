import {
	List,
	Create,
	Update,
	Enable,
	Disable,
	Assign,
	StartLogin,
	GetQrcode
} from '@/store/pt-account';

import { ItemResponse } from './types';

export const ListStore = new List<Array<ItemResponse>>({});
export const CreateStore = new Create({});
export const UpdateStore = new Update({});
export const EnableStore = new Enable({});
export const DisableStore = new Disable({});
export const AssignStore = new Assign({});

export const StartLoginStore = new StartLogin<ItemResponse>({});
export const GetQrcodeStore = new GetQrcode<ItemResponse>({});
