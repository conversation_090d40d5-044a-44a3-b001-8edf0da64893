<template>
	<pm-effi :controller="controller">
		<pm-search>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '员工类型'
				}"
				data-index="type"
				xtype="SELECT"
			/>
		</pm-search>

		<pm-table
			:columns="COLUMNS"
			:use-custom-column="true"
			:operations-fixed="true"
			:operations-width="160"
			:sort-num="false"
			:scroll="{
				x: 'max-content'
			}"
		>
			<template #beforeCustom>
				<m-button type="primary" @click="onCreate">添加</m-button>
			</template>

			<template #operations="{ record }">
				<!-- <m-button type="link" @click="onEdit(record)">编辑</m-button> -->
				<m-button type="link" @click="onAssign(record)">分配员工</m-button>
				<m-button v-if="record.status === 2" danger type="link" @click="onEnable(record)">启用</m-button>
				<m-button v-if="record.status === 1" danger type="link" @click="onDisable(record)">禁用</m-button>
				<m-button v-if="record.type === 2" type="link" @click="login(record)">云真机登录</m-button>
			</template>
		</pm-table>
	</pm-effi>
	<pm-dialog v-model:visible="qrcodeVisible" title="登录" width="340px">
		<div v-if="qrcodeImg">
			<img :src="qrcodeImg" width="300" />
		</div>
		<div v-else>登录二维码生成中</div>
		<template #footer>
			<m-button type="primary" @click="cancelLogin">确定</m-button>
		</template>
	</pm-dialog>
	<edit-comp ref="editRef" @refresh="onRefresh"></edit-comp>
	<assign-comp ref="assignRef" @refresh="onRefresh"></assign-comp>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { ModelController, MUtils, MESSAGE_TYPE } from 'admin-library';

import EditComp from './comps/edit-dialog/index.vue';
import AssignComp from './comps/assign-dialog/index.vue';
import { confirmMessageBox, renderTextWithColor } from '@/shard/utils';
import { COLUMNS } from './config';
import { STATUS_OPTIONS } from './constant';
import { ListStore, EnableStore, DisableStore, StartLoginStore, GetQrcodeStore } from './store';
import { ItemResponse } from './types';

let qrcodeTimer = null;

export default defineComponent({
	components: {
		EditComp,
		AssignComp
	},
	setup() {
		const components = {
			editRef: ref<InstanceType<typeof EditComp>>(),
			assignRef: ref<InstanceType<typeof AssignComp>>()
		};

		const constants = {
			COLUMNS,
			STATUS_OPTIONS
		};

		const state = reactive({
			selectedId: 0,
			qrcodeVisible: false,
			qrcodeImg: ''
		});

		const controller = new ModelController({
			table: {
				store: ListStore
			},
			search: {
				// type: {
				// 	store: PTYPE_STORE
				// }
			}
		});
		controller.tableRequest();

		const methods = {
			renderTextWithColor,

			onCreate() {
				components.editRef.value.open();
			},

			onEdit(row: ItemResponse) {
				components.editRef.value.open(row);
			},

			onAssign(row: ItemResponse) {
				components.assignRef.value.open(row);
			},

			async onEnable(row: ItemResponse) {
				await confirmMessageBox('确认启用吗？');

				await EnableStore.request({ id: row.id }).getData();
				methods.onRefresh();
				MUtils.toast('启用成功', MESSAGE_TYPE.success);
			},

			async onDisable(row: ItemResponse) {
				await confirmMessageBox('确认禁用吗？');

				await DisableStore.request({ id: row.id }).getData();
				methods.onRefresh();
				MUtils.toast('禁用成功', MESSAGE_TYPE.success);
			},

			async login(row: ItemResponse) {
				const t = await StartLoginStore.request({ accountNo: row.accountNo }).getData();
				state.qrcodeVisible = true;
				state.qrcodeImg = '';
				const getCode = async () => {
					const v = await GetQrcodeStore.request({ taskNo: t.value }).getData();
					if (v.qrCodeImg) {
						this.qrcodeImg = 'data:image/jpeg;base64,' + v.qrCodeImg;
						clearInterval(qrcodeTimer);
					}
				};
				qrcodeTimer = setInterval(getCode, 2000);
			},

			cancelLogin() {
				state.qrcodeVisible = false;
				clearInterval(qrcodeTimer);
			},

			onRefresh() {
				controller.tableRequest();
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			controller
		};
	}
});
</script>
