import { List, Delete, Create, Update, View, ContactList, Cancel, Reopen, TagList } from '@/store/bulk-message';
import { ItemResponse } from './types';

export const ListStore = new List<Array<ItemResponse>>({});
export const DelStore = new Delete({});
export const CreateStore = new Create({});
export const UpdateStore = new Update({});

export const ViewStore = new View<ItemResponse>({});
export const ContactListStore = new ContactList<Array<ItemResponse>>({});
export const CancelStore = new Cancel({});
export const ReopenStore = new Reopen({});

export const TagListStore = new TagList<Array<ItemResponse>>({});
