<template>
	<div @click="open" style="padding: 0 0 10px">
		<span class="ant-dropdown-link ant-dropdown-trigger cursor-pointer">
			根据标签筛选
			<down-outlined />
		</span>
		<div>
			<span
				class="ant-tag ant-tag-checkable ant-tag-checkable-checked"
				v-for="tagId in selectedTags"
				:key="tagId"
			>
				{{ getTagName(tagId) }}
			</span>
		</div>
	</div>
	<m-form-item-rest>
		<pm-dialog
			v-model:visible="visible"
			:mask="false"
			title="标签筛选"
			width="680px"
			style="height: calc(100vh - 64px)"
			class="sjsj"
			@close="onCancel"
		>
			<div v-if="visible" class="int">
				<div class="sh">
					<div v-for="cate in tagGroup" :key="cate.groupId" style="margin-bottom: 10px">
						<div>{{ cate.groupName }}</div>
						<template v-for="tag in cate.dataList" :key="tag.tagId">
							<a-checkable-tag
								:checked="checkedTags.indexOf(tag.tagId) > -1"
								@change="checked => handleChange(tag.tagId, checked)"
							>
								{{ tag.tagName }}
							</a-checkable-tag>
						</template>
					</div>
				</div>
			</div>

			<template #footer>
				<m-button plain @click="onCancel">取消</m-button>
				<m-button type="primary" @click="onConfirm">确定</m-button>
			</template>
		</pm-dialog>
	</m-form-item-rest>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue';
import { MUtils } from 'admin-library';
import { CheckableTag } from 'ant-design-vue';
import { DownOutlined } from '@ant-design/icons-vue';

export default defineComponent({
	components: { ACheckableTag: CheckableTag, DownOutlined },
	props: {
		tagList: {
			type: Array,
			isRequired: true
		} as any,
		tagGroup: {
			type: Array,
			isRequired: true
		} as any,
		selectedTags: {
			type: Array,
			isRequired: true
		} as any
	},
	emits: ['update:selectedTags'],
	setup(props, { emit }) {
		const components = {};

		const constants = {};

		const state = reactive({
			checkedTags: [],
			loading: false,
			visible: false
		});

		const methods = {
			open() {
				state.visible = true;
			},
			handleChange(tag: string, checked: boolean) {
				const { checkedTags } = state;
				const nextCheckedTags = checked ? [...checkedTags, tag] : checkedTags.filter(t => t !== tag);
				state.checkedTags = nextCheckedTags;
			},
			getTagName(tagId) {
				let tag = props.tagList.find(item => item.tagId === tagId);
				return tag?.tagName;
			},
			onConfirm() {
				const selectedTags = MUtils.deepClone(state.checkedTags);
				state.visible = false;
				emit('update:selectedTags', selectedTags);
			},
			onCancel() {
				state.checkedTags = MUtils.deepClone(props.selectedTags);
				state.visible = false;
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			...toRefs(props)
		};
	}
});
</script>

<style lang="less">
.sjsj {
	.ant-modal-content {
		overflow: hidden;
		height: 100%;
	}
	.ant-modal-body {
		overflow: hidden;
		height: calc(100% - 118px);
	}
	.m-dialog-body {
		overflow: hidden;
		height: 100%;
	}
	.int {
		display: flex;
		flex-direction: column;
		height: 100%;
	}
}
</style>
<style lang="less" scoped>
.sh {
	overflow-y: auto;
}
.ant-tag {
	padding: 3px 7px;
	margin-top: 8px;
	min-width: 96px;
	text-align: center;
	color: #5e5e5e;
	background-color: #f7f7f7;
}
.ant-tag-checkable-checked {
	color: #0081ff;
	background-color: #c5e8ff;
}
</style>
