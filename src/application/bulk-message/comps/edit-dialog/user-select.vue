<template>
	<div @click.stop="open" class="sk-item">
		<slot></slot>
		<div class="sk-right">
			<span class="sk-right" v-if="select.length">
				<span class="user-name elli">{{ getUserName(select[0]) }}</span>
				<span v-if="select.length > 1">等{{ select.length }}人</span>
			</span>
			<right-outlined />
		</div>
	</div>
	<m-form-item-rest>
		<pm-dialog
			v-model:visible="visible"
			:mask="false"
			title="选择客户"
			width="680px"
			style="height: calc(100vh - 64px)"
			class="sjsj"
			:autoFocus="false"
			@close="onCancel"
		>
			<div v-if="visible" class="int">
				<div>
					<TagSelectComp :tagList="tagList" :tagGroup="tagGroup" v-model:selectedTags="selectedTags" />
					<m-row justify="space-between" align="middle">
						<m-col>
							<m-checkbox v-if="!searchText" v-model:checked="checkAll" @change="onCheckAllChange">
								全部客户
							</m-checkbox>
						</m-col>
						<m-col>
							<m-input-search
								v-model:value="searchTextTemp"
								@search="onSearch"
								style="width: calc(100% - 40px)"
								enter-button
							/>
							<m-button style="margin-left: 8px" @click="onReset">
								<template #icon><undo-outlined /></template>
							</m-button>
						</m-col>
					</m-row>
					<m-divider style="margin: 10px 0; height: 1px; background-color: #f2f7ff" />
				</div>
				<div class="sh">
					<DynamicScroller
						class="recycle"
						:items="
							contactOptions.filter(item => {
								return (
									!searchText ||
									item.contactMixName.toLowerCase().indexOf(searchText.toLowerCase()) >= 0
								);
							})
						"
						:min-item-size="34"
						key-field="contactId"
					>
						<template #="{ item, index, active }">
							<DynamicScrollerItem
								:item="item"
								:active="active"
								:size-dependencies="[item.tagIds]"
								:data-index="index"
							>
								<label class="ant-checkbox-wrapper" :for="item.contactId">
									<span
										class="ant-checkbox"
										:class="{ 'ant-checkbox-checked': checkedUsers.includes(item.contactId) }"
									>
										<input
											type="checkbox"
											class="ant-checkbox-input"
											:id="item.contactId"
											:value="item.contactId"
											@change="onCheckChange"
											:checked="checkedUsers.includes(item.contactId)"
										/>
										<span class="ant-checkbox-inner"></span>
									</span>
									<span class="ant-cont">
										<span>
											<span>{{ item.contactMixName }}</span>
											<span class="tag" v-for="tag in item.tagIds" :key="tag.tagId">
												{{ tag.tagName }}
											</span>
										</span>
										<span>{{ formatDate(item.addTime, 'MM-DD') }}</span>
									</span>
								</label>
							</DynamicScrollerItem>
						</template>
					</DynamicScroller>
				</div>
			</div>

			<template #footer>
				<span>(已选择{{ checkedUsers.length }}位客户)</span>
				<m-button :loading="loading" type="primary" style="margin-left: 8px" @click="onConfirm">确定</m-button>
			</template>
		</pm-dialog>
	</m-form-item-rest>
</template>

<script lang="ts">
import Dayjs from 'dayjs';
import { defineComponent, reactive, toRefs, watch, computed } from 'vue';
import { MUtils } from 'admin-library';
import { RightOutlined, UndoOutlined } from '@ant-design/icons-vue';
import { DynamicScroller, DynamicScrollerItem } from 'vue-virtual-scroller';
import 'vue-virtual-scroller/dist/vue-virtual-scroller.css';

import TagSelectComp from './tag-select.vue';

export default defineComponent({
	components: {
		DynamicScroller,
		DynamicScrollerItem,
		RightOutlined,
		UndoOutlined,
		TagSelectComp
	},
	props: {
		options: {
			type: Array,
			isRequired: true
		} as any,
		tagList: {
			type: Array,
			isRequired: true
		} as any,
		tagGroup: {
			type: Array,
			isRequired: true
		},
		select: {
			type: Array,
			isRequired: true
		}
	},
	emits: ['update:select', 'update:tag'],
	setup(props, { emit }) {
		const components = {};

		const constants = {};

		const state = reactive({
			contactOptions: MUtils.deepClone(props.options),
			checkedUsers: MUtils.deepClone(props.select),
			selectedTags: [],
			searchText: '',
			searchTextTemp: '',
			loading: false,
			visible: false
		});

		const checkAll = computed(() => {
			let val = state.checkedUsers;
			return val.length > 0 && val.length === state.contactOptions.length;
		});

		const methods = {
			formatDate(date, template) {
				return Dayjs(date).format(template);
			},
			open() {
				state.visible = true;
			},
			onCheckAllChange(e: any) {
				Object.assign(state, {
					checkedUsers: e.target.checked ? state.contactOptions.map(item => item.contactId) : []
				});
			},
			getUserName(contactId) {
				let user = state.contactOptions.find(item => item.contactId === contactId);
				return user?.contactMixName;
			},
			onSearch() {
				if (state.searchTextTemp) {
					state.searchText = state.searchTextTemp;
				}
			},
			onReset() {
				state.searchText = state.searchTextTemp = '';
			},
			onConfirm() {
				const select = MUtils.deepClone(state.checkedUsers);
				state.visible = false;
				emit('update:select', select);
				emit('update:tag', state.selectedTags);
			},
			onCancel() {
				state.checkedUsers = MUtils.deepClone(props.select);
			},
			onTagChange() {
				if (state.selectedTags.length) {
					state.contactOptions = MUtils.deepClone(
						props.options.filter(item => {
							return item.tagIds.some(sItem => state.selectedTags.includes(sItem.tagId));
						})
					);
					state.checkedUsers = state.checkedUsers.filter(
						item => state.contactOptions.map(item => item.contactId).indexOf(item) > -1
					);
				} else {
					state.contactOptions = MUtils.deepClone(props.options);
				}
			},
			onCheckChange(e) {
				let { checked, id } = e.target;
				if (checked) {
					state.checkedUsers.push(id);
				} else {
					let index = state.checkedUsers.indexOf(id);
					state.checkedUsers.splice(index, 1);
				}
			}
		};

		watch(
			() => state.selectedTags,
			() => {
				methods.onTagChange();
			}
		);

		return {
			...toRefs(state),
			checkAll,
			...components,
			...constants,
			...methods,
			...toRefs(props)
		};
	}
});
</script>

<style lang="less">
.recycle {
	overflow-x: hidden;
	overflow-y: auto;
	max-height: 100%;
}
.sjsj {
	.ant-modal-content {
		overflow: hidden;
		height: 100%;
	}
	.ant-modal-body {
		overflow: hidden;
		height: calc(100% - 118px);
	}
	.m-dialog-body {
		overflow: hidden;
		height: 100%;
	}
	.int {
		display: flex;
		flex-direction: column;
		height: 100%;
	}
}
</style>
<style lang="less" scoped>
.sk-item {
	display: flex;
	justify-content: space-between;
	padding: 6px;
	margin: 4px 0;
	background-color: #dfdfdf;
	cursor: pointer;
	.sk-right {
		display: flex;
		justify-content: center;
		align-items: center;
	}
	.user-name {
		max-width: 212px;
		vertical-align: middle;
	}
}
.sh {
	flex: 1;
	height: 0;
}
.ant-checkbox-wrapper {
	display: flex;
	padding: 2px 0;
	.ant-checkbox + span {
		display: flex;
		justify-content: space-between;
		padding: 4px 8px;
		margin-left: 4px;
		background-color: #f7f7f7;
		flex: 1;
	}
	.tag {
		display: inline-block;
		padding: 0 4px;
		margin-left: 8px;
		text-align: center;
		color: #0081ff;
		background-color: #c5e8ff;
	}
}
</style>
