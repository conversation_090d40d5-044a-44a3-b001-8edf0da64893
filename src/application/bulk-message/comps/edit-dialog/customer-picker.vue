<template>
	<m-select
		mode="multiple"
		@click.stop="open"
		:value="select"
		:fieldNames="{ label: 'contactMixName', value: 'contactId' }"
		:options="allContactOptions"
		:open="false"
		max-tag-count="responsive"
		removeIcon=" "
	/>
	<m-form-item-rest>
		<pm-dialog v-model:visible="visible" title="分别发送给" width="420px">
			<div v-if="visible" class="int">
				<div class="pty">
					<div class="sl-item" @click="selectMode = 'all'">
						<span>全部客户</span>
						<check-outlined v-if="selectMode === 'all'" />
					</div>
					<div class="sl-item" @click="selectMode = 'part'">
						<span>按条件筛选的客户</span>
						<check-outlined v-if="selectMode === 'part'" />
					</div>
					<div v-if="selectMode === 'part'">
						<div>
							<UserSelectComp
								:options="allContactOptions"
								:tagList="tagList"
								:tagGroup="tagGroup"
								v-model:select="posSelect"
								v-model:tag="posSelectTag"
							>
								<div>
									<user-add-outlined />
									发送给
								</div>
							</UserSelectComp>
						</div>
						<div>
							<UserSelectComp
								:options="allContactOptions"
								:tagList="tagList"
								:tagGroup="tagGroup"
								v-model:select="oppSelect"
							>
								<div>
									<user-delete-outlined />
									不发送给
								</div>
							</UserSelectComp>
						</div>
					</div>
				</div>
			</div>

			<template #footer>
				<m-button :loading="loading" :disabled="disabled" type="primary" @click="onConfirm">确定</m-button>
			</template>
		</pm-dialog>
	</m-form-item-rest>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, watch, computed } from 'vue';
import { MUtils, MESSAGE_TYPE } from 'admin-library';
import { CheckOutlined } from '@ant-design/icons-vue';
import { UserAddOutlined } from '@ant-design/icons-vue';
import { UserDeleteOutlined } from '@ant-design/icons-vue';

import UserSelectComp from './user-select.vue';
import { ContactListStore, TagListStore } from '../../store';

function groupBy(list, key, label) {
	const newArr = [];
	list.forEach(item => {
		const value = item[key];
		let obj = newArr.find(item => item[key] === value);

		if (!obj) {
			obj = {
				[key]: item[key],
				[label]: item[label],
				dataList: []
			};
			newArr.push(obj);
		}
		obj.dataList.push(item);
	});
	return newArr;
}

let tagGroup = [];
let tagList = [];

export default defineComponent({
	components: {
		CheckOutlined,
		UserAddOutlined,
		UserDeleteOutlined,
		UserSelectComp
	},
	props: {
		value: {
			type: String,
			isRequired: true
		},
		wecomUserId: {
			type: String,
			isRequired: true
		},
		tagIds: {
			type: Array
		},
		contactPool: {
			type: Array
		}
	},
	emits: ['update:value', 'update:tagIds'],
	setup(props, { emit }) {
		const components = {};

		const constants = {};

		const state = reactive({
			selectMode: '',
			select: [],

			posSelectTag: [],
			posSelect: [],
			oppSelect: [],

			allContactOptions: [],
			tagGroup: [],
			tagList: [],

			loading: false,
			visible: false
		});

		const disabled = computed(() => {
			if (state.selectMode === 'all') {
				return false;
			} else {
				return !(state.posSelect.length || state.oppSelect.length);
			}
		});

		const methods = {
			async open() {
				if (!props.wecomUserId) {
					MUtils.toast('请先选择一个企微账号', MESSAGE_TYPE.warning);
					return;
				}
				if (state.allContactOptions.length === 0) {
					await methods.initTagGroup();
					methods.initContactListOptions();
				}
				state.visible = true;
			},
			async initContactListOptions() {
				state.loading = true;

				let res = await ContactListStore.request({
					ownerId: props.wecomUserId,
					contactType: 1,
					limit: 9999
				}).getData();

				if (state.tagList.length === 0) {
					await methods.initTagGroup();
				}

				if (props.contactPool && props.contactPool.length) {
					res = res.filter(item => props.contactPool.indexOf(item.contactId) > -1);
				}

				res = res.map(item => {
					let { contactMixName, contactId, addTime, tagIds } = item;
					tagIds = tagIds.split(',').filter(item => item);
					tagIds = tagIds.map(item => {
						return state.tagList.find(sItem => sItem.tagId === item);
					});
					tagIds = tagIds
						.filter(item => item)
						.map(({ tagName, tagId }) => {
							return { tagName, tagId };
						});

					return {
						contactMixName,
						contactId,
						addTime,
						tagIds
					};
				});
				state.loading = false;
				state.allContactOptions = res;
			},
			async initTagGroup() {
				if (tagList.length) {
					state.tagList = tagList;
					state.tagGroup = tagGroup;
					return;
				}
				const res = await TagListStore.request({ limit: 9999 }).getData();
				state.tagList = tagList = res;
				state.tagGroup = tagGroup = groupBy(res, 'groupId', 'groupName');
			},

			onConfirm() {
				let select = [];
				if (state.selectMode === 'all') {
					select = MUtils.deepClone(state.allContactOptions.map(item => item.contactId));
				} else {
					if (state.posSelect.length && state.oppSelect.length) {
						select = MUtils.deepClone(state.posSelect.filter(item => state.oppSelect.indexOf(item) === -1));
					} else if (state.posSelect.length) {
						select = MUtils.deepClone(state.posSelect);
					} else if (state.oppSelect.length) {
						select = MUtils.deepClone(
							state.allContactOptions
								.map(item => item.contactId)
								.filter(item => state.oppSelect.indexOf(item) === -1)
						);
					}
				}

				if (select.length === 0) {
					MUtils.toast(`无满足条件客户，请重新选择`, MESSAGE_TYPE.error);
					return;
				}

				state.select = select;
				emit('update:value', state.select);
				emit('update:tagIds', state.posSelectTag);

				state.visible = false;
			}
		};
		watch(
			() => props.wecomUserId,
			(val, oldVal) => {
				if (val === oldVal && val === undefined) {
					return;
				}
				Object.assign(state, {
					selectMode: '',
					select: MUtils.deepClone(props.value),

					posSelectTag: [],
					posSelect: [],
					oppSelect: [],

					allContactOptions: [],
					tagGroup: [],
					tagList: []
				});
				methods.initContactListOptions();
			},
			{ immediate: true }
		);
		methods.initTagGroup();

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			...props,
			disabled
		};
	}
});
</script>

<style lang="less" scoped>
.pty {
	display: flex;
	flex-direction: column;
	padding: 10px;
	background: #efefef;
	.sl-item {
		display: flex;
		justify-content: space-between;
		cursor: pointer;
	}
}
</style>
