<template>
	<m-form ref="formRef" :model="messageDate" :colon="false" autocomplete="off">
		<m-form-item name="messageType" required>
			<m-radio-group v-model:value="messageDate.messageType" @change="onTypeChange" style="width: 100%">
				<template v-for="item in options.messageType">
					<m-radio-button
						:value="item.value"
						:key="item.value"
						v-if="!(item.value === 7 && receiverType === 1)"
					>
						{{ item.label }}
					</m-radio-button>
				</template>
			</m-radio-group>
		</m-form-item>
		<m-form-item
			name="textContent"
			v-if="messageDate.messageType === 1 || messageDate.messageType === 7"
			label="文本"
			:labelCol="{ span: 0 }"
			:required="!(messageDate.messageType === 1 && receiverType !== 1)"
		>
			<m-textarea
				v-model:value="messageDate.textContent"
				:rows="4"
				placeholder="录入要配置的纯文本消息，消息长度最多4000个字节（企微官方限制）"
			/>
		</m-form-item>
		<m-form-item
			name="fileKeyObj"
			v-else-if="messageDate.messageType === 2"
			extra="图片大小不超过10MB，JPG、PNG、JPEG格式"
			label="图片"
			:labelCol="{ span: 0 }"
			required
		>
			<fileUploadComp fileType="image" :maxSize="10" v-model:value="messageDate.fileKeyObj" />
		</m-form-item>
		<m-form-item
			name="fileKeyObj"
			v-else-if="messageDate.messageType === 3"
			extra="大小不超过10MB，MP4格式"
			label="视频"
			:labelCol="{ span: 0 }"
			required
		>
			<weixinFileUploadComp fileType="video" :maxSize="10" v-model:value="messageDate.fileKeyObj" />
		</m-form-item>
		<m-form-item
			name="fileKeyObj"
			v-else-if="messageDate.messageType === 4"
			extra="大小不超过10MB，DOC/DOCX/TXT/PDF格式"
			label="文件"
			:labelCol="{ span: 0 }"
			required
		>
			<weixinFileUploadComp fileType="file" :maxSize="10" v-model:value="messageDate.fileKeyObj" />
		</m-form-item>
		<m-form-item v-else-if="messageDate.messageType === 5">
			<div style="display: flex">
				<div style="width: 140px">
					<m-form-item
						name="fileKeyObj"
						extra="封面图(建议上传正方形，5MB以内)"
						label="封面图"
						:labelCol="{ span: 0 }"
						required
					>
						<fileUploadComp fileType="image" :maxSize="5" v-model:value="messageDate.fileKeyObj" />
					</m-form-item>
				</div>
				<div style="flex: 1">
					<m-form-item label="链接" name="linkUrl" required>
						<m-input v-model:value="messageDate.linkUrl" placeholder="以http或https开头" />
					</m-form-item>
					<m-form-item label="标题" name="linkTitle" required>
						<m-input v-model:value="messageDate.linkTitle" :maxlength="20" placeholder="最多20字" />
					</m-form-item>
					<m-form-item label="描述" name="linkDesc" required>
						<m-input v-model:value="messageDate.linkDesc" :maxlength="20" placeholder="最多20字" />
					</m-form-item>
				</div>
			</div>
		</m-form-item>
		<m-form-item v-else-if="messageDate.messageType === 6">
			<div style="display: flex">
				<div style="width: 140px">
					<m-form-item
						name="fileKeyObj"
						extra="封面图(建议尺寸520*416)"
						label="封面图"
						:labelCol="{ span: 0 }"
						required
					>
						<fileUploadComp fileType="image" :maxSize="10" v-model:value="messageDate.fileKeyObj" />
					</m-form-item>
				</div>
				<div style="flex: 1">
					<m-form-item label="标题" name="title" required>
						<m-input
							v-model:value="messageDate.title"
							:maxlength="64"
							placeholder="输入小程序标题，最多64个字"
						/>
					</m-form-item>
					<m-form-item label="appId" name="appId" required :rules="noBlank">
						<m-input v-model:value="messageDate.appId" placeholder="小程序appid" />
					</m-form-item>
					<m-form-item label="page" name="page" required :rules="noBlank">
						<m-input v-model:value="messageDate.page" placeholder="小程序page路径" />
					</m-form-item>
				</div>
			</div>
		</m-form-item>
		<m-form-item v-if="messageDate.messageType === 1 && receiverType !== 1">
			<m-checkbox v-model:checked="messageDate.isAltAll">@所有人</m-checkbox>
		</m-form-item>
	</m-form>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref, watch } from 'vue';
import { MUtils } from 'admin-library';

import { noBlank } from '@/shard/validate';
import fileUploadComp from '@/comp/file-upload/index.vue';
import weixinFileUploadComp from '@/comp/weixin-file-upload/index.vue';
import { MESSAGE_TYPE_OPTIONS } from '../../constant';

export default defineComponent({
	components: {
		fileUploadComp,
		weixinFileUploadComp
	},
	props: {
		value: {
			type: Object,
			isRequired: true
		},
		receiverType: {
			type: Number,
			isRequired: true
		}
	},
	emits: ['update:value'],
	setup(props, { emit }) {
		const components = {
			formRef: ref(null)
		};

		const constants = {
			options: {
				messageType: MESSAGE_TYPE_OPTIONS
			},
			noBlank
		};

		let value = MUtils.deepClone(props.value);
		let messageType = value.messageType,
			messageContent,
			fileKeyObj;
		if (value && value.messageContent) {
			messageContent = value.messageContent;
			fileKeyObj = null;
			if (messageType === 2) {
				let { attachmentUrl, fileKey, ...rest } = messageContent;
				messageContent = rest;
				if (fileKey) {
					fileKeyObj = {
						showUrl: attachmentUrl,
						encodedData: fileKey
					};
				}
			} else if (messageType === 3 || messageType === 4) {
				let { attachmentUrl, fileKey, mediaId, ...rest } = messageContent;
				messageContent = rest;
				if (fileKey) {
					fileKeyObj = {
						showUrl: attachmentUrl,
						encodedData: fileKey,
						mediaId: mediaId
					};
				}
			} else if (messageType === 5) {
				let { linkCover, fileKey, ...rest } = messageContent;
				messageContent = rest;
				if (fileKey) {
					fileKeyObj = {
						showUrl: linkCover,
						encodedData: fileKey
					};
				}
			} else if (messageType === 6) {
				let { imgUrl, fileKey, ...rest } = messageContent;
				messageContent = rest;
				if (fileKey) {
					fileKeyObj = {
						showUrl: imgUrl,
						encodedData: fileKey
					};
				}
			}
		}
		const state = reactive({
			messageDate: MUtils.deepClone({ messageType, fileKeyObj, ...messageContent })
		});

		const methods = {
			onTypeChange() {
				state.messageDate.fileKeyObj = null;
			},
			async validate() {
				return components.formRef.value.validate();
			}
		};

		watch(
			() => state.messageDate,
			() => {
				let {
					messageType,
					textContent,
					fileKeyObj,
					linkUrl,
					linkTitle,
					linkDesc,
					title,
					appId,
					page,
					isAltAll
				} = state.messageDate;
				if (messageType === 1 || messageType === 7) {
					messageContent = { textContent };
					if (messageType === 1 && props.receiverType !== 1) {
						messageContent.isAltAll = isAltAll;
					}
				} else if (messageType === 2 || messageType === 3 || messageType === 4) {
					messageContent = { mediaId: fileKeyObj?.mediaId, fileKey: fileKeyObj?.encodedData };
				} else if (messageType === 5) {
					messageContent = { linkUrl, linkTitle, linkDesc, fileKey: fileKeyObj?.encodedData };
				} else if (messageType === 6) {
					messageContent = { fileKey: fileKeyObj?.encodedData, title, appId, page };
				} else {
					messageContent = {};
				}
				emit('update:value', MUtils.deepClone({ messageType, messageContent }));
			},
			{
				deep: true
			}
		);

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			...props
		};
	}
});
</script>

<style lang="less" scoped>
.file-uploader {
	position: relative;
	display: flex;
	justify-content: center;
	align-items: center;
	overflow: hidden;
	width: 102px;
	height: 102px;
	.btns {
		position: absolute;
		bottom: 0;
		left: 0;
		display: none;
		padding-top: 4px;
		width: 100%;
		height: 26px;
		background-color: rgba(0, 0, 0, 0.5);
	}
	&:hover {
		.btns {
			display: block;
		}
	}
	.anticon {
		margin: 0 4px;
		width: 16px;
		font-size: 16px;
		color: rgba(255, 255, 255, 0.85);
		cursor: pointer;
	}
}
</style>
