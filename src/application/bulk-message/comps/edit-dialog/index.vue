<template>
	<pm-dialog v-model:visible="visible" :title="title" width="680px">
		<template v-if="visible">
			<m-form
				ref="formRef"
				:model="formState"
				:colon="false"
				:label-col="{ style: { width: '107px' } }"
				autocomplete="off"
			>
				<m-form-item label="企微账号" name="promoterNo" required>
					<m-select
						show-search
						optionFilterProp="employeeMixName"
						@change="onEventEmployeeNoChange"
						v-model:value="formState.promoterNo"
						:options="options.employeeOptions"
						:fieldNames="{ label: 'employeeMixName', value: 'employeeNo' }"
					/>
				</m-form-item>
				<m-form-item label="群发会话类型" name="receiverType" required>
					<m-select
						@change="onEventReceiverTypeChange"
						v-model:value="formState.receiverType"
						:options="options.receiverType"
					/>
				</m-form-item>
				<m-form-item label="群发对象" name="receiverList" v-if="formState.receiverType === 1" required>
					<CustomerPickComp
						:wecomUserId="wecomUserId"
						v-model:value="formState.receiverList"
						v-model:tagIds="formState.tagIds"
					/>
				</m-form-item>
				<m-form-item label="群发对象" name="receiverList" v-else required>
					<m-select
						mode="multiple"
						show-search
						optionFilterProp="contactMixName"
						v-model:value="formState.receiverList"
						:options="options.contactOptions"
						:fieldNames="{ label: 'contactMixName', value: 'contactId' }"
						max-tag-count="responsive"
						removeIcon=" "
						@click="onGroupSelectClick"
					>
						<template #dropdownRender="{ menuNode: menu }">
							<div @mousedown.prevent>
								<div
									style="padding: 4px 8px"
									class="ant-checkbox-wrapper"
									@mousedown.prevent="
										onCheckAllClick(options.contactOptions.length === formState.receiverList.length)
									"
								>
									<span
										class="ant-checkbox"
										:class="{
											'ant-checkbox-checked':
												options.contactOptions.length === formState.receiverList.length
										}"
									>
										<input type="checkbox" class="ant-checkbox-input" />
										<span class="ant-checkbox-inner"></span>
									</span>
									<span>全选</span>
								</div>
							</div>
							<m-divider style="margin: 4px 0" />
							<v-nodes :vnodes="menu" />
						</template>
					</m-select>
				</m-form-item>
				<m-form-item label="推送时间" name="sendTime" required>
					<m-date-picker
						v-model:value="formState.sendTime"
						:show-time="{ format: 'HH:mm' }"
						format="YYYY-MM-DD HH:mm"
						valueFormat="YYYY-MM-DD HH:mm"
					/>
				</m-form-item>
				<m-form-item label="推送内容来源" name="contentResource">
					<m-radio-group v-model:value="formState.contentResource" @change="onEventResourceChange">
						<m-radio :value="false">自定义录入</m-radio>
						<m-radio :value="true">从素材库选择</m-radio>
					</m-radio-group>
				</m-form-item>
				<m-form-item v-if="formState.contentResource" label="群发素材库" name="materialId">
					<m-select
						show-search
						optionFilterProp="title"
						@change="onEventMaterialIdChange"
						v-model:value="formState.materialId"
						:options="options.tmplList.filter(item => item.type === formState.receiverType)"
						:fieldNames="{ label: 'title', value: 'id' }"
					/>
				</m-form-item>
				<m-form-item label="推送内容">
					<messagegGoupComp
						v-if="formState.id"
						:key="formState.receiverType"
						ref="messageRef"
						v-model:value="formState.messageData"
						:receiverType="formState.receiverType"
					/>
					<template v-else>
						<div v-for="(item, index) in formState.msgList" :key="item.uid">
							<m-row justify="space-between" align="middle">
								<m-col></m-col>
								<m-col>
									<div style="font-size: 14px; line-height: 1.6; text-align: center">
										消息{{ index + 1 }}
									</div>
								</m-col>
								<m-col>
									<m-button
										v-if="formState.msgList.length > 1"
										danger
										type="link"
										size="small"
										@click="removeItem(index)"
										:disabled="formState.materialId"
									>
										删除
									</m-button>
								</m-col>
							</m-row>
							<m-divider style="margin: 10px 0; height: 1px; background-color: #f2f7ff" />
							<messagegGoupComp
								:key="formState.receiverType"
								ref="messageRef"
								v-model:value="item.messageData"
								:receiverType="formState.receiverType"
							/>
						</div>
						<m-button
							style="margin-top: 0; margin-bottom: 8px; width: 100%"
							type="dashed"
							@click="addItem"
							:disabled="formState.materialId"
						>
							<template #icon><PlusOutlined /></template>
							继续添加
						</m-button>
					</template>
				</m-form-item>
			</m-form>
		</template>

		<template #footer>
			<m-button plain @click="visible = false">取消</m-button>
			<m-button :loading="loading" type="primary" @click="onConfirm">确定</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import Dayjs from 'dayjs';
import { defineComponent, reactive, toRefs, ref, computed } from 'vue';
import { MESSAGE_TYPE, MUtils } from 'admin-library';
import { PlusOutlined } from '@ant-design/icons-vue';
import getUid from 'ant-design-vue/es/vc-upload/uid';
import { isArray } from 'lodash';

import { cloneFromPick, cloneByMerge } from '@/shard/utils';
import CustomerPickComp from './customer-picker.vue';
import messagegGoupComp from './message-group.vue';
import { RECEIVER_TYPE_OPTIONS } from '../../constant';
import { ListStore as EmployeeListStore } from '@/application/promoter/store';
import { ListStore as TmplListStore } from '@/application/bulk-message-tmpl/store';
import { CreateStore, UpdateStore, ContactListStore } from '../../store';

const VNodes = defineComponent({
	props: {
		vnodes: {
			type: Object,
			required: true
		}
	},
	render() {
		return this.vnodes;
	}
});

const DefaultFormState = {
	id: null,
	promoterNo: '',
	receiverType: 1,
	receiverList: [],
	tagIds: [],
	sendTime: null,
	contentResource: false,
	materialId: null,
	messageData: {
		messageType: 1,
		messageContent: {}
	},
	msgList: [
		{
			messageData: {
				messageType: 1,
				messageContent: {}
			}
		}
	]
};

export default defineComponent({
	components: {
		VNodes,
		PlusOutlined,
		CustomerPickComp,
		messagegGoupComp
	},
	emits: ['refresh'],
	setup(props, { emit }) {
		const components = {
			formRef: ref(null),
			messageRef: ref(null)
		};

		const constants = {};

		const state = reactive({
			visible: false,
			title: '',
			loading: false,
			formState: MUtils.deepClone(DefaultFormState),
			options: {
				employeeOptions: [],
				receiverType: RECEIVER_TYPE_OPTIONS,
				contactOptions: [],
				tmplList: []
			}
		});

		const wecomUserId = computed(() => {
			const { promoterNo } = state.formState;
			let employee = state.options.employeeOptions.find(item => {
				return item.employeeNo === promoterNo;
			});
			return employee?.wecomUserId;
		});

		const getValidate = () => {
			let list = [];

			if (isArray(components.messageRef.value)) {
				list = components.messageRef.value.map(item => item.validate());
			} else {
				list = [components.messageRef.value.validate()];
			}
			return list;
		};

		const methods = {
			onCheckAllClick(value) {
				Object.assign(state.formState, {
					receiverList: !value ? state.options.contactOptions.map(item => item.contactId) : []
				});
			},
			removeItem(index) {
				state.formState.msgList.splice(index, 1);
			},
			async addItem() {
				await Promise.all(getValidate());

				state.formState.msgList.push({
					messageData: {
						messageType: 1,
						messageContent: {}
					},
					uid: getUid()
				});
			},
			async open(row?: any) {
				state.formState = MUtils.deepClone(DefaultFormState);
				state.title = row?.id ? '编辑' : '新增';

				if (row?.contentData) {
					let contentData = JSON.parse(row.contentData);

					state.formState = cloneFromPick(
						{
							...row,
							msgList: contentData.map(item => {
								return {
									messageData: item,
									uid: getUid()
								};
							})
						},
						DefaultFormState
					);
				} else if (row) {
					let { messageType, messageContent } = row;
					let messageData = { messageType, messageContent };
					if (messageData.messageContent) {
						messageData.messageContent = JSON.parse(messageData.messageContent);
					}
					if (row.id) {
						state.formState = cloneFromPick(
							{
								...row,
								messageData
							},
							DefaultFormState
						);
					} else {
						state.formState = cloneFromPick(
							{
								...row,
								msgList: [{ messageData, uid: getUid() }]
							},
							DefaultFormState
						);
					}
				}

				if (state.formState.sendTime) {
					state.formState.sendTime = Dayjs(state.formState.sendTime);
				}
				if (state.formState.receiverList) {
					state.formState.receiverList = state.formState.receiverList.map(item => item.receiverId);
				}

				state.visible = true;

				methods.initTmplListOptions();
				await methods.initEmployeeOptions();
				if (state.formState.receiverType !== 1) {
					methods.initContactListOptions();
				}
			},

			onEventEmployeeNoChange() {
				if (state.formState.receiverType !== 1) {
					methods.initContactListOptions();
				}

				state.formState.receiverList = [];
				state.formState.tagIds = [];
			},

			onEventReceiverTypeChange() {
				methods.initContactListOptions();

				state.formState.receiverList = [];
				state.formState.tagIds = [];
				state.formState.contentResource = false;
				state.formState.materialId = null;

				let messageData = {
					messageType: 1,
					messageContent: {}
				};
				if (state.formState.id) {
					state.formState.messageData = messageData;
				} else {
					state.formState.msgList = [{ messageData, uid: getUid() }];
				}
			},
			onEventResourceChange(contentResource) {
				if (!contentResource) {
					state.formState.materialId = null;
				}
			},
			onEventMaterialIdChange(materialId) {
				let tmplData = state.options.tmplList.find(item => {
					return item.id === materialId;
				});

				let contentData = JSON.parse(tmplData.contentData);
				state.formState.msgList = [];
				setTimeout(() => {
					state.formState.msgList = contentData.map(item => {
						return {
							messageData: item,
							uid: getUid()
						};
					});
				}, 100);
			},
			onGroupSelectClick() {
				if (!state.formState.promoterNo) {
					MUtils.toast('请先选择一个企微账号', MESSAGE_TYPE.warning);
				}
			},

			async initEmployeeOptions() {
				const res = await EmployeeListStore.request({
					enableAutomaticHosting: true,
					limit: 9999,
					status: 20
				}).getData();
				if (res) {
					state.options.employeeOptions = res;
				}
			},

			async initContactListOptions() {
				const { receiverType } = state.formState;
				if (wecomUserId.value && receiverType) {
					let res = await ContactListStore.request({
						ownerId: wecomUserId.value,
						contactType: receiverType,
						limit: 9999
					}).getData();
					if (res) {
						state.options.contactOptions = res;
					}
				}
			},
			async initTmplListOptions() {
				let res = await TmplListStore.request({
					limit: 9999
				}).getData();
				if (res) {
					state.options.tmplList = res;
				}
			},

			async onConfirm() {
				await Promise.all([components.formRef.value.validate(), ...getValidate()]);

				try {
					state.loading = true;
					const params = cloneByMerge(state.formState, DefaultFormState);
					const fetchStore = state.formState.id ? UpdateStore : CreateStore;

					if (params.sendTime) {
						params.sendTime = +new Date(params.sendTime);
					}
					if (params.tagIds) {
						params.tagIds = params.tagIds.join(',');
					}

					if (state.formState.id) {
						let { messageType, messageContent } = params.messageData;
						delete params.messageData;

						params.messageType = messageType;
						params.messageContent = JSON.stringify(messageContent);
					} else {
						params.msgList = JSON.stringify(params.msgList.map(item => item.messageData));
					}

					await fetchStore.request(params).getData();

					emit('refresh');
					state.visible = false;
					MUtils.toast('保存成功', MESSAGE_TYPE.success);
				} finally {
					state.loading = false;
				}
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			wecomUserId
		};
	}
});
</script>
