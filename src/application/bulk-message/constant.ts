import { getMapfromArray, getStorefromArray } from '@/shard/utils';

export const RECEIVER_TYPE_OPTIONS = [
	{
		label: '个人',
		value: 1
	},
	{
		label: '群',
		value: 2
	}
];
export const RECEIVER_TYPE_MAP = getMapfromArray(RECEIVER_TYPE_OPTIONS);
export const RECEIVER_TYPE_STORE = getStorefromArray(RECEIVER_TYPE_OPTIONS);

export const MESSAGE_TYPE_OPTIONS = [
	{
		label: '文本',
		value: 1
	},
	{
		label: '图片',
		value: 2
	},
	{
		label: '视频',
		value: 3
	},
	{
		label: '文件',
		value: 4
	},
	{
		label: '链接',
		value: 5
	},
	{
		label: '小程序',
		value: 6
	},
	{
		label: '群公告',
		value: 7
	}
];
export const MESSAGE_TYPE_MAP = getMapfromArray(MESSAGE_TYPE_OPTIONS);
export const MESSAGE_TYPE_STORE = getStorefromArray(MESSAGE_TYPE_OPTIONS);

export const STATUS_OPTIONS = [
	{
		label: '待执行',
		value: 1
	},
	{
		label: '执行中',
		value: 2
	},
	{
		label: '执行完成',
		value: 3
	},
	{
		label: '已取消',
		value: 4
	},
	{
		label: '发送完成',
		value: 5
	}
];
export const STATUS_MAP = getMapfromArray(STATUS_OPTIONS);
export const STATUS_STORE = getStorefromArray(STATUS_OPTIONS);

export const TYPE_OPTIONS = [
	{
		label: '普通消息',
		value: 1
	},
	{
		label: '学员自动提醒消息',
		value: 2
	},
	{
		label: '群自动化消息',
		value: 3
	},
	{
		label: '客户跟进自动化消息',
		value: 4
	},
	{
		label: '讲师评价自动消息',
		value: 5
	},
	{
		label: '每日群发消息',
		value: 6
	}
];
export const TYPE_MAP = getMapfromArray(TYPE_OPTIONS);
export const TYPE_STORE = getStorefromArray(TYPE_OPTIONS);

export const SUB1_TYPE_OPTIONS = [
	{
		label: '上课提醒',
		value: 1
	},
	{
		label: '课后作业提醒',
		value: 2
	},
	{
		label: '作业未完成提醒',
		value: 3
	},
	{
		label: '考前提醒',
		value: 4
	}
];
export const SUB1_TYPE_MAP = getMapfromArray(SUB1_TYPE_OPTIONS);
export const SUB1_TYPE_STORE = getStorefromArray(SUB1_TYPE_OPTIONS);

export const SUB2_TYPE_OPTIONS = [
	{
		label: '添加企微2~5天',
		value: 5
	},
	{
		label: '添加企微6~90天',
		value: 6
	}
];
export const SUB2_TYPE_MAP = getMapfromArray(SUB2_TYPE_OPTIONS);
export const SUB2_TYPE_STORE = getStorefromArray(SUB2_TYPE_OPTIONS);
