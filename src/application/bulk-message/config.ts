import { TableColumn, ColumnXtype, TableDateFormat } from 'admin-library';
import { ColumnWidthEnum } from '@/shard/constant';
import { RECEIVER_TYPE_MAP, STATUS_MAP, MESSAGE_TYPE_MAP, TYPE_MAP } from './constant';

export const COLUMNS: TableColumn[] = [
	{
		title: '#',
		dataIndex: 'id',
		fixed: 'left'
	},
	{
		title: '员工姓名',
		dataIndex: 'promoterName',
		fixed: 'left'
	},
	{
		title: '会话类型',
		dataIndex: 'receiverType',
		render: data => {
			return RECEIVER_TYPE_MAP[data];
		},
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '接收对象',
		dataIndex: 'receiverList',
		xtype: ColumnXtype.CUSTOM,
		width: 140
	},
	{
		title: '群发标签',
		dataIndex: 'sendTagList',
		render: data => {
			return data.map(item => item.tagName).join('，');
		},
		width: 140
	},
	{
		title: '消息类型',
		dataIndex: 'messageType',
		render: data => {
			return MESSAGE_TYPE_MAP[data];
		},
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '推送内容',
		dataIndex: 'messageContent',
		xtype: ColumnXtype.CUSTOM,
		width: 220
	},
	{
		title: '推送时间',
		dataIndex: 'sendTime',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	},
	{
		title: '推送状态',
		dataIndex: 'status',
		render: data => {
			return STATUS_MAP[data];
		},
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '推送统计',
		dataIndex: 'receiverData',
		xtype: ColumnXtype.CUSTOM,
		width: ColumnWidthEnum.TEXT5
	},
	{
		title: '业务类型',
		dataIndex: 'bizType',
		render: data => {
			return TYPE_MAP[data];
		},
		width: ColumnWidthEnum.TEXT5
	},
	{
		title: '目标覆盖人群数',
		dataIndex: 'targetCoverageCount',
		width: 160
	},
	{
		title: '实际覆盖人群数',
		dataIndex: 'lecturerName',
		render: (value, lineData) => {
			return lineData.receiverList?.length;
		},
		width: 160
	},
	{
		title: '覆盖率',
		dataIndex: 'coverage',
		render: (value, lineData) => {
			const count = lineData.receiverList?.length;
			const target = lineData.targetCoverageCount;
			if (target) {
				let coverage = (count / target) * 100;
				coverage = Number(coverage.toFixed(2)) || 0;
				return coverage + '%';
			}
		},
		width: ColumnWidthEnum.TEXT3
	},
	{
		title: '任务创建时间',
		dataIndex: 'createTime',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	},
	{
		title: '创建人',
		dataIndex: 'createUserName',
		width: ColumnWidthEnum.TEXT4
	}
];
