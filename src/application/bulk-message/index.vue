<template>
	<pm-effi :controller="controller">
		<pm-search>
			<pm-search-single
				v-model="promoterNo"
				:span="3"
				show-search
				optionFilterProp="employeeMixName"
				:antdProps="{
					placeholder: '员工',
					fieldNames: { label: 'employeeMixName', value: 'employeeNo' }
				}"
				data-index="promoterNo"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '状态'
				}"
				data-index="status"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="3"
				v-model="bizTypeValue"
				:antdProps="{
					placeholder: '业务类型'
				}"
				data-index="bizType"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="3"
				v-show="bizTypeValue === 6"
				v-model="bizSubTypeValue"
				:antdProps="{
					placeholder: '业务子类型'
				}"
				data-index="bizSubType2"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="3"
				v-show="bizTypeValue !== 6"
				v-model="bizSubTypeValue"
				:disabled="bizTypeValue !== 2"
				:antdProps="{
					placeholder: '业务子类型'
				}"
				data-index="bizSubType1"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '会话类型'
				}"
				data-index="receiverType"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="5"
				:antd-props="{
					placeholder: ['发送时间', '结束时间']
				}"
				:model-value="defaultTime"
				xtype="RANGEPICKER"
				data-index="sendTimeBegin|sendTimeEnd"
			/>
		</pm-search>
		<pm-table
			:columns="COLUMNS"
			:use-custom-column="true"
			:operations-fixed="true"
			:operations-width="130"
			:sort-num="false"
			:scroll="{
				x: 'max-content'
			}"
			:antd-props="{
				customRow: record => {
					return {
						onClick: () => {
							onClick(record);
						}
					};
				}
			}"
			:rowClassName="setInstallClassName"
		>
			<template #beforeCustom>
				<m-button type="primary" @click="onCreate">添加</m-button>
			</template>
			<template #receiverList="{ record }">
				<m-button type="link" v-if="record.receiverList.length > 5" @click="onViewDetail(record.receiverList)">
					{{ record.receiverList.length }}个联系人
				</m-button>
				<div v-else style="max-width: 300px">
					{{ record.receiverList.map(item => item.receiverName).join('，') }}
				</div>
			</template>
			<template #messageContent="{ record }">
				<div v-if="record.messageType === 1 || record.messageType === 7">
					{{ parseContent(record.messageContent).textContent }}
					<span
						class="primary"
						v-if="
							record.messageType === 1 &&
							record.receiverType !== 1 &&
							parseContent(record.messageContent).isAltAll
						"
					>
						@所有人
					</span>
				</div>
				<div v-else-if="record.messageType === 2">
					<m-image :src="parseContent(record.messageContent).attachmentUrl" style="width: 50px" />
				</div>
				<div
					v-else-if="record.messageType === 3 || record.messageType === 4"
					class="elli primary cursor-pointer w200"
				>
					<a :href="parseContent(record.messageContent).attachmentUrl" target="_blank">
						{{ parseContent(record.messageContent).attachmentUrl }}
					</a>
				</div>
				<div v-else-if="record.messageType === 5">
					封面：
					<m-image :src="parseContent(record.messageContent).linkCover" style="width: 50px" />
					<br />
					<div class="elli cursor-pointer w200">
						<span>链接：</span>
						<a :href="parseContent(record.messageContent).linkUrl" target="_blank">
							{{ parseContent(record.messageContent).linkUrl }}
						</a>
					</div>
					标题：
					{{ parseContent(record.messageContent).linkTitle }}
					<br />
					描述：
					{{ parseContent(record.messageContent).linkDesc }}
				</div>
				<div v-else-if="record.messageType === 6">
					封面：
					<m-image :src="parseContent(record.messageContent).imgUrl" style="width: 50px" />
					<br />
					标题：
					{{ parseContent(record.messageContent).title }}
					<br />
					appId：
					{{ parseContent(record.messageContent).appId }}
					<br />
					page：
					{{ parseContent(record.messageContent).page }}
				</div>
				<div v-else></div>
			</template>
			<template #receiverData="{ record }">
				<div class="w120" v-if="record.status === 3 || record.status === 5">
					<span
						class="cursor-pointer success"
						v-if="
							record.receiverList.filter(item => item.status === 4).length === record.receiverList.length
						"
						@click="onViewDetail(record.receiverList, 4)"
					>
						全部成功
					</span>
					<span
						class="cursor-pointer danger"
						v-else-if="
							record.receiverList.filter(item => item.status === 3).length === record.receiverList.length
						"
						@click="onViewDetail(record.receiverList, 3)"
					>
						全部失败
					</span>
					<template v-else>
						<span
							class="cursor-pointer"
							v-if="record.receiverList.filter(item => item.status === 2).length"
							@click="onViewDetail(record.receiverList, 2)"
						>
							发送中：
							<span class="warning">
								{{ record.receiverList.filter(item => item.status === 2).length }}
							</span>
							<br />
						</span>
						<span class="cursor-pointer" @click="onViewDetail(record.receiverList, 3)">
							发送失败：
							<span class="danger">
								{{ record.receiverList.filter(item => item.status === 3).length }}
							</span>
						</span>
						<br />
						<span class="cursor-pointer" @click="onViewDetail(record.receiverList, 4)">
							发送成功：
							<span class="success">
								{{ record.receiverList.filter(item => item.status === 4).length }}
							</span>
						</span>
					</template>
				</div>
			</template>

			<template #operations="{ record }">
				<m-button type="link" @click="onEdit(record)" v-if="record.status === 1">编辑</m-button>
				<m-button type="link" @click="onCopy(record)">复制</m-button>
				<m-button type="link" v-if="record.status === 1 || record.status === 4" danger @click="onDel(record)">
					删除
				</m-button>
				<m-button type="link" v-if="record.status === 1" @click="onCancel(record)">取消任务</m-button>
				<m-button type="link" v-if="record.status === 4" @click="onReopen(record)">重新推送</m-button>
			</template>
		</pm-table>
	</pm-effi>

	<edit-comp ref="editRef" @refresh="onRefresh"></edit-comp>
</template>

<script lang="ts">
import Dayjs from 'dayjs';
import { defineComponent, ref, reactive, toRefs, createVNode, watch } from 'vue';
import { MESSAGE_TYPE, MUtils, ModelController } from 'admin-library';
import { useRoute } from 'vue-router';

import EditComp from './comps/edit-dialog/index.vue';
import { confirmMessageBox, openTableDialog } from '@/shard/utils';
import { COLUMNS } from './config';
import { STATUS_STORE, RECEIVER_TYPE_STORE, TYPE_STORE, SUB1_TYPE_STORE, SUB2_TYPE_STORE } from './constant';
import { ListStore as EmployeeListStore } from '@/application/promoter/store';
import { ListStore, DelStore, CancelStore, ReopenStore } from './store';
import { ItemResponse } from './types';

export default defineComponent({
	components: {
		EditComp
	},
	setup() {
		const route = useRoute();
		const query = route.query;

		const components = {
			editRef: ref<InstanceType<typeof EditComp>>()
		};

		const constants = {
			COLUMNS
		};

		let bizType;
		if (query.bizType) {
			bizType = +query.bizType;
		}
		let subBizType;
		if (query.subBizType) {
			subBizType = +query.subBizType;
		}
		let defaultTime;
		if (query.sendTimeBegin && query.sendTimeEnd) {
			defaultTime = [+query.sendTimeBegin, +query.sendTimeEnd].map(item => Dayjs(item));
		}

		const state = reactive({
			selectedId: '',
			promoterNo: query.promoterNo || null,
			bizTypeValue: bizType,
			bizSubTypeValue: subBizType,
			defaultTime: defaultTime
		});

		const controller = new ModelController({
			table: {
				store: ListStore
			},
			search: {
				promoterNo: {
					store: EmployeeListStore
				},
				status: {
					store: STATUS_STORE
				},
				bizType: {
					store: TYPE_STORE
				},
				bizSubType1: {
					store: SUB1_TYPE_STORE
				},
				bizSubType2: {
					store: SUB2_TYPE_STORE
				},
				receiverType: {
					store: RECEIVER_TYPE_STORE
				}
			}
		});
		controller.tableRequest();

		controller.search.promoterNo.onRequest.use(params => {
			params = {
				...params,
				enableAutomaticHosting: true,
				limit: 9999
			};

			return params;
		});
		controller.table.onRequest.use(params => {
			delete params.bizSubType1;
			delete params.bizSubType2;
			let sd = { bizSubType: state.bizSubTypeValue };

			params = {
				...params,
				...sd
			};

			return params;
		});

		watch(
			() => state.bizTypeValue,
			() => {
				if (state.bizTypeValue !== 2) {
					state.bizSubTypeValue = null;
				} else {
					// state.bizSubTypeValue = 2;
				}
			}
		);

		const methods = {
			onClick(recored) {
				state.selectedId = recored.id;
			},
			setInstallClassName(record) {
				if (record.id === state.selectedId) {
					return 'selected-row';
				}
			},
			onCreate() {
				components.editRef.value.open();
			},
			async onCancel(row: ItemResponse) {
				await confirmMessageBox('确认取消任务吗？');

				await CancelStore.request({ id: row.id }).getData();
				methods.onRefresh();
				MUtils.toast('取消任务成功', MESSAGE_TYPE.success);
			},
			async onReopen(row: ItemResponse) {
				await confirmMessageBox('确认重新打开吗？');

				await ReopenStore.request({ id: row.id }).getData();
				methods.onRefresh();
				MUtils.toast('重新打开成功', MESSAGE_TYPE.success);
			},
			async onDel(row: ItemResponse) {
				await confirmMessageBox('确认删除吗？');

				await DelStore.request({ id: row.id }).getData();
				methods.onRefresh();
				MUtils.toast('删除成功', MESSAGE_TYPE.success);
			},

			async onEdit(row: ItemResponse) {
				components.editRef.value.open(row);
			},

			async onCopy(row: ItemResponse) {
				components.editRef.value.open({ ...row, id: null });
			},

			parseContent(data) {
				return JSON.parse(data);
			},

			onViewDetail(content, filteredValue?) {
				function getResult(status) {
					if (status === 2) {
						return `<span class="warning">
				            发送中
				        </span>`;
					} else if (status === 3) {
						return `<span class="danger">
				            失败
				        </span>`;
					} else if (status === 4) {
						return `<span class="success">
				            成功
				        </span>`;
					}
				}
				openTableDialog({
					dataSource: content,
					columns: [
						{
							title: '用户',
							dataIndex: 'receiverName',
							key: 'receiverName'
						},
						{
							title: '结果',
							dataIndex: 'status',
							key: 'status',
							filters: [
								{
									text: `发送中(${content.filter(item => item.status === 2).length})`,
									value: 2
								},
								{
									text: `失败(${content.filter(item => item.status === 3).length})`,
									value: 3
								},
								{
									text: `成功(${content.filter(item => item.status === 4).length})`,
									value: 4
								}
							],
							onFilter: (value, record) => record.status === value,
							filterMultiple: false,
							customRender: ({ text }) => {
								return createVNode('span', {
									innerHTML: getResult(text)
								});
							},
							defaultFilteredValue: filteredValue ? [String(filteredValue)] : null
						}
					]
				});
			},
			onRefresh() {
				controller.tableRequest();
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			controller
		};
	}
});
</script>
