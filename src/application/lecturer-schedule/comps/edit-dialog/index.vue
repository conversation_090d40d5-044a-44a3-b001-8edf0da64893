<template>
	<pm-dialog v-model:visible="visible" :title="title" width="680px">
		<template v-if="visible">
			<m-form
				ref="formRef"
				:model="formState"
				:colon="false"
				:label-col="{ style: { width: '95px' } }"
				autocomplete="off"
			>
				<m-form-item label=" ">
					<div>
						<span class="danger">请注意：</span>
						原则上不能替讲师清空已提交的「可排课时间」，如有特殊情况必须清空，请上传该讲师申请清空的聊天记录截图、填写清空说明
					</div>
				</m-form-item>
				<m-form-item label="截图" name="resourceEncode">
					<fileUploadComp fileType="image" v-model:value="formState.resourceEncode" />
				</m-form-item>
				<m-form-item label="清空原因" name="clearReasonType" required>
					<m-select
						v-model:value="formState.clearReasonType"
						:options="options.typeList"
						:allowClear="true"
						:disabled="formState.id"
					/>
				</m-form-item>
				<m-form-item label="清空说明" name="remark" required>
					<m-textarea v-model:value="formState.remark" :maxlength="300" show-count :rows="3" />
				</m-form-item>
			</m-form>
		</template>

		<template #footer>
			<m-button plain @click="visible = false">取消</m-button>
			<m-button :loading="loading" type="primary" @click="onConfirm">确定</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { MESSAGE_TYPE, MUtils } from 'admin-library';

import fileUploadComp from '@/comp/file-upload/index.vue';
import { cloneFromPick, cloneByMerge } from '@/shard/utils';
import { TYPE_OPTIONS } from '@/application/schedule-clear-record/constant';

import { ClearScheduleStore } from '../../store';

const DefaultFormState = {
	lecturerId: null,
	scheduleDate: '',
	resourceEncode: '',
	clearReasonType: '',
	remark: ''
};

export default defineComponent({
	components: {
		fileUploadComp
	},
	emits: ['refresh'],
	setup(props, { emit }) {
		const components = {
			formRef: ref(null)
		};

		const constants = {};

		const state = reactive({
			visible: false,
			title: '清空确认',
			loading: false,
			formState: MUtils.deepClone(DefaultFormState),
			options: {
				typeList: TYPE_OPTIONS
			}
		});

		const methods = {
			open(row?: any) {
				state.formState = MUtils.deepClone(DefaultFormState);

				if (row) {
					state.formState = cloneFromPick(row, DefaultFormState);
				}

				state.visible = true;
			},

			async onConfirm() {
				await components.formRef.value.validate();

				try {
					state.loading = true;
					const params = cloneByMerge(state.formState, DefaultFormState);
					const fetchStore = ClearScheduleStore;

					params.resourceEncode = params.resourceEncode.encodedData;

					await fetchStore.request(params).getData();

					emit('refresh');
					state.visible = false;
					MUtils.toast('保存成功', MESSAGE_TYPE.success);
				} finally {
					state.loading = false;
				}
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods
		};
	}
});
</script>
