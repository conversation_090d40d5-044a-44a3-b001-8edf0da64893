<template>
	<pm-effi :controller="controller">
		<pm-search>
			<pm-search-single>
				<template #custom>
					<teacher-select
						v-model:value="lecturerId"
						@onSelectChange="onLecturerChange"
						@onSearchLecturer="onSearchLecturer"
						data-index="lecturerId"
						comp-type="search"
					/>
				</template>
			</pm-search-single>
		</pm-search>

		<pm-table
			:columns="COLUMNS"
			:use-custom-column="true"
			:pagination="false"
			:operations-fixed="true"
			:operations-width="180"
			:sort-num="false"
			:scroll="{
				x: 'max-content'
			}"
			class="table-no-hover"
		>
			<template #beforeButtons>
				<div class="mt10" style="display: flex">
					<div>讲师信息：</div>
					<template v-if="lecturerData.id">
						<div>类型：{{ TYPE_MAP[lecturerData.type] }}；</div>
						<div>层级：{{ LAYER_MAP[lecturerData.layer] }}；</div>
						<div>等级：{{ lecturerData.levelName }}；</div>
						<div>主管老师：{{ lecturerData.dockingPersonMixName }}；</div>
						<div>上岗状态：{{ STATUS_MAP[lecturerData.status] }}；</div>
						<div>
							已通过技能：{{
								renderSkillList(lecturerData.skillGroups.filter(item => item.status === 2))
							}}；
						</div>
					</template>
				</div>
			</template>
			<template #headerCell="{ column }">
				<template v-if="column.key === 'timeline'">
					{{ column.title }}
					<span v-html="timelineDesc"></span>
					<br />
					<header-tick-comp />
				</template>
			</template>
			<template #timeline="{ record }">
				<Timeline
					:courses="record.courseScheduleList"
					:freeDurations="record.scheduleDurationList"
					:noSchedule="record.noSchedule"
					:select-day="record.date"
				/>
			</template>
			<template #operations="{ record }">
				<!-- <m-button type="link" @click="onCreate(record)">排课</m-button> -->
				<m-button type="link" @click="onClearSchedule(record)">清空可排课时间</m-button>
			</template>
		</pm-table>
	</pm-effi>

	<edit-comp ref="editRef" @refresh="onRefresh"></edit-comp>
	<clear-schedule-comp ref="clearScheduleRef" @refresh="onRefresh"></clear-schedule-comp>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { ModelController } from 'admin-library';
import { useRoute } from 'vue-router';
import { Store } from '@simplex/simple-store';

import TeacherSelect from '@/comp/teacher-select/index.vue';
import HeaderTickComp from '@/application/courses-schedule/comps/timeline/header-tick.vue';
import Timeline, { timelineDesc } from '@/application/courses-schedule/comps/timeline/index.vue';
import EditComp from '@/application/courses-schedule/comps/edit-dialog/index.vue';
import ClearScheduleComp from './comps/edit-dialog/index.vue';
import { COLUMNS } from './config';
import { CAR_TYPE_MAP, EXAM_KEMU_MAP } from '@/shard/constant';
import { STATUS_MAP } from '@/application/promoter/constant';
import { LAYER_MAP, TYPE_MAP } from '@/application/part-time-teacher/constant';
import { ListStore } from './store';
import { ItemResponse } from './types';

export default defineComponent({
	components: {
		TeacherSelect,
		HeaderTickComp,
		Timeline,
		EditComp,
		ClearScheduleComp
	},
	setup() {
		const route = useRoute();
		const query = route.query;
		const components = {
			editRef: ref<InstanceType<typeof EditComp>>(),
			clearScheduleRef: ref<InstanceType<typeof ClearScheduleComp>>()
		};

		const constants = {
			COLUMNS,
			LAYER_MAP,
			TYPE_MAP,
			STATUS_MAP,
			timelineDesc
		};

		let studentId;
		if (query.studentId) {
			studentId = +query.studentId;
		}
		let lecturerId;
		if (query.lecturerId) {
			lecturerId = +query.lecturerId;
		}
		const state = reactive({
			lecturerId,
			lecturerList: [],
			lecturerData: {} as ItemResponse
		});

		const controller = new ModelController({
			autoSearch: !!lecturerId,
			table: {
				store: ListStore
			},
			search: {
				lecturerId: {
					store: new Store([])
				}
			}
		});

		controller.search.lecturerId.onResponse.use(data => {
			state.lecturerList = data.data;
			return data;
		});

		const methods = {
			onCreate(row: ItemResponse) {
				components.editRef.value.open(
					{
						lecturerId: state.lecturerId,
						studentId,
						courses: row.courseScheduleList,
						freeDurations: row.scheduleDurationList
					},
					row.date
				);
			},
			async onLecturerChange(_val, detail) {
				state.lecturerData = detail;
			},
			async onSearchLecturer(list) {
				controller.search.lecturerId.updateStore(new Store(list));
			},
			onClearSchedule(row: ItemResponse) {
				components.clearScheduleRef.value.open({ lecturerId: state.lecturerId, scheduleDate: row.date });
			},
			renderSkillList(list) {
				return list
					.map(item => {
						let text = '';
						if (item.carType) {
							text += CAR_TYPE_MAP[item.carType];
						}
						if (item.kemu) {
							text += EXAM_KEMU_MAP[item.kemu];
						}
						return text;
					})
					.join('、');
			},

			onRefresh() {
				controller.tableRequest();
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			controller
		};
	}
});
</script>
