<template>
	<pm-effi :controller="controller">
		<pm-search>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '客户编码'
				}"
				data-index="customerNo"
				xtype="INPUT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '客户木仓Id'
				}"
				data-index="customerMucangId"
				xtype="INPUT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '学员姓名'
				}"
				data-index="studentName"
				xtype="INPUT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '订单状态'
				}"
				data-index="status"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="5"
				:antd-props="{
					placeholder: ['生效时间', '结束时间']
				}"
				:model-value="defaultTime"
				xtype="RANGEPICKER"
				data-index="startEffectiveTime|endEffectiveTime"
			/>
			<pm-search-single
				:span="5"
				:antd-props="{
					placeholder: ['订单支付时间', '结束时间']
				}"
				xtype="RANGEPICKER"
				data-index="startOrderPaidTime|endOrderPaidTime"
			/>
		</pm-search>

		<pm-table
			:columns="COLUMNS"
			:use-custom-column="true"
			:operations-fixed="true"
			:operations-width="60"
			:sort-num="false"
			:scroll="{
				x: 'max-content'
			}"
		>
			<template #operations="{ record }">
				<m-button v-if="record.status === 10" type="link" @click="onSetVip(record)">申请发VIP</m-button>
			</template>
		</pm-table>
	</pm-effi>
	<set-vip-comp ref="setVipRef" @refresh="onRefresh"></set-vip-comp>
</template>

<script lang="ts">
import Dayjs from 'dayjs';
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { ModelController } from 'admin-library';
import { recentWeek } from '@paas/paas-library';

import SetVipComp from '@/application/student/comps/set-vip-dialog/index.vue';
import { COLUMNS } from '@/application/protocol/config';
import { STATUS_STORE } from '@/application/protocol/constant';
import { ListStore } from './store';

export default defineComponent({
	components: {
		SetVipComp
	},
	setup() {
		const components = {
			setVipRef: ref<InstanceType<typeof SetVipComp>>()
		};

		const constants = {
			COLUMNS
		};

		const state = reactive({
			defaultTime: recentWeek().map(date => Dayjs(date))
		});

		const controller = new ModelController({
			table: {
				store: ListStore
			},
			search: {
				status: {
					store: STATUS_STORE
				}
			}
		});
		controller.tableRequest();

		const methods = {
			onSetVip(row) {
				components.setVipRef.value.open({
					mucangId: row.customerMucangId
				});
			},

			onRefresh() {
				controller.tableRequest();
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			controller
		};
	}
});
</script>
