<template>
	<pm-dialog v-model:visible="visible" :title="title" width="1280px">
		<pm-effi :controller="controller">
			<pm-search ref="searchRef" v-if="settlementDetail">
				<pm-search-single
					:span="3"
					:antdProps="{
						placeholder: '课程Id'
					}"
					data-index="courseScheduleId"
					xtype="INPUT"
				/>
				<pm-search-single
					:span="3"
					:antdProps="{
						placeholder: '学员称呼'
					}"
					data-index="studentName"
					xtype="INPUT"
				/>
				<pm-search-single
					:span="3"
					:antdProps="{
						placeholder: '结算状态'
					}"
					data-index="status"
					xtype="SELECT"
				/>
				<pm-search-single
					:span="3"
					:antdProps="{
						placeholder: '讲师姓名'
					}"
					data-index="lecturerName"
					xtype="INPUT"
				/>
			</pm-search>
			<pm-search ref="searchRef" v-else>
				<pm-search-single
					:span="5"
					:antd-props="{
						placeholder: ['课程开始时间', '结束时间']
					}"
					xtype="RANGEPICKER"
					data-index="beginTime|endTime"
				/>
			</pm-search>
			<pm-table
				:columns="COLUMNS"
				:use-custom-column="true"
				:pagination="settlementDetail"
				:operations="settlementDetail"
				:operations-fixed="true"
				:sort-num="false"
				:scroll="{
					x: 'max-content',
					y: '420px'
				}"
				:antdProps="{
					rowKey: 'courseScheduleId',
					rowSelection: rowSelection
				}"
			>
				<template #operations="{ record }">
					<m-button type="link" @click="onRepay(record)" v-if="record.status === 3">重新支付</m-button>
				</template>
			</pm-table>
		</pm-effi>
		<template #footer>
			<template v-if="!settlementDetail">
				<m-button plain @click="visible = false">取消</m-button>
				<m-button :loading="loading" type="primary" @click="onConfirm">发起结算</m-button>
			</template>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, computed, ref } from 'vue';
import { MESSAGE_TYPE, MUtils, ModelController } from 'admin-library';

import { cloneByMerge, confirmMessageBox } from '@/shard/utils';
import { COLUMNS } from './config';
import { STATUS_STORE } from '../../constant';
import { UnSettlementCourseListStore, DetailListStore, CreateStore, RepayStore } from './store';

const DefaultFormState = {
	courseScheduleIdList: [],
	type: 1
};

export default defineComponent({
	emits: ['refresh'],
	setup(props, { emit }) {
		const components = {
			searchRef: ref()
		};

		const constants = {
			COLUMNS
		};

		const state = reactive({
			visible: false,
			title: '',
			loading: false,
			formState: MUtils.deepClone(DefaultFormState),
			settlementDetail: false,
			settlementId: null,
			excludeToday: false
		});

		const rowSelection = computed(() => {
			let rowSelection = {
				preserveSelectedRowKeys: true,
				selectedRowKeys: state.formState.courseScheduleIdList,
				onChange: methods.onSelectChange
			};
			return state.settlementDetail ? null : rowSelection;
		});

		const controller = new ModelController({
			table: {
				store: null
			},
			search: {
				status: {
					store: STATUS_STORE
				}
			}
		});

		controller.table.onRequest.use(params => {
			let sd = {};
			if (state.settlementDetail) {
				sd = { settlementId: state.settlementId };
			} else {
				sd = { excludeToday: state.excludeToday, limit: 9999 };
			}
			params = {
				...params,
				...sd
			};

			return params;
		});

		const methods = {
			onSelectChange(keys) {
				state.formState.courseScheduleIdList = keys;
			},

			open(row: any) {
				state.formState = MUtils.deepClone(DefaultFormState);

				state.settlementDetail = row.settlementDetail;
				state.settlementId = row.settlementId;
				state.excludeToday = row.excludeToday;

				state.title = state.settlementDetail ? '查看' : '预览结算单';
				let nextStore = state.settlementDetail ? DetailListStore : UnSettlementCourseListStore;
				controller.table.updateStore(nextStore);

				state.visible = true;

				setTimeout(() => {
					components.searchRef.value.reset();
				}, 100);
			},
			async onRepay(row) {
				await confirmMessageBox('确认重新发起吗？');

				await RepayStore.request({ id: row.id }).getData();
				emit('refresh');
				state.visible = false;
				MUtils.toast('发起成功', MESSAGE_TYPE.success);
			},

			async onConfirm() {
				if (!state.formState.courseScheduleIdList?.length) {
					MUtils.toast('请先勾选课程');
					return;
				}
				await confirmMessageBox('确认对这笔结算单发起支付吗？确定后开始OA审批');

				try {
					state.loading = true;
					const params = cloneByMerge(state.formState, DefaultFormState);
					const fetchStore = CreateStore;

					params.courseScheduleIdList = params.courseScheduleIdList.join(',');

					await fetchStore.request(params).getData();

					emit('refresh');
					state.visible = false;
					MUtils.toast('保存成功', MESSAGE_TYPE.success);
				} finally {
					state.loading = false;
				}
			}
		};

		return {
			...toRefs(state),
			rowSelection,
			...components,
			...constants,
			...methods,
			controller
		};
	}
});
</script>
