import { TableColumn, TableDateFormat } from 'admin-library';
import { renderCopyComp } from '@/shard/utils';
import { ColumnWidthEnum } from '@/shard/constant';
import { PAY_STATUS_MAP } from '../../constant';

export const COLUMNS: TableColumn[] = [
	{
		title: '课程Id',
		dataIndex: 'courseScheduleId',
		fixed: 'left',
		width: ColumnWidthEnum.TEXT3
	},
	{
		title: '课程主题',
		fixed: 'left',
		dataIndex: 'title'
	},
	{
		title: '学员称呼',
		dataIndex: 'studentName',
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '学员木仓Id',
		dataIndex: 'studentMucangId',
		render: data => {
			return renderCopyComp(data);
		},
		width: ColumnWidthEnum.TEXT5
	},
	{
		title: '结算项状态',
		dataIndex: 'status',
		render: data => {
			return PAY_STATUS_MAP[data];
		},
		width: ColumnWidthEnum.TEXT5
	},
	{
		title: '支付失败原因',
		dataIndex: 'payFailReason',
		width: ColumnWidthEnum.TEXT6
	},
	{
		title: '结算金额(元)',
		dataIndex: 'fee',
		width: ColumnWidthEnum.TEXT6
	},
	{
		title: '讲师',
		dataIndex: 'lecturerName',
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '特殊结算备注',
		dataIndex: 'remark',
		width: ColumnWidthEnum.TEXT6
	},
	{
		title: '课程开始时间',
		dataIndex: 'courseBeginTime',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	},
	{
		title: '课程结束时间',
		dataIndex: 'courseEndTiime',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	}
];
