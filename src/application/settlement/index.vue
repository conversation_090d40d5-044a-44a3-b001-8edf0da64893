<template>
	<pm-effi :controller="controller">
		<pm-search>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '申请单编号'
				}"
				data-index="oaNumber"
				xtype="INPUT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '结算状态'
				}"
				data-index="status"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="5"
				:antd-props="{
					placeholder: ['创建时间', '结束时间']
				}"
				xtype="RANGEPICKER"
				data-index="createTimeFrom|createTimeTo"
			/>
		</pm-search>
		<pm-table
			:columns="COLUMNS"
			:use-custom-column="true"
			:operations-fixed="false"
			:operations-width="70"
			:sort-num="false"
			:scroll="{
				x: 'max-content'
			}"
		>
			<template #beforeCustom>
				<m-button type="primary" @click="startSettlement()">开始结算</m-button>
			</template>
			<template #oaId="{ record }">
				<div class="primary cursor-pointer">
					<a
						:href="`${APP.domain['oa']}/km/review/km_review_main/kmReviewMain.do?method=view&fdId=${record.oaId}`"
						target="_blank"
					>
						{{ record.oaId }}
					</a>
				</div>
			</template>

			<template #operations="{ record }">
				<m-button type="link" @click="onView(record)">查看</m-button>
			</template>
		</pm-table>
	</pm-effi>

	<edit-comp ref="editRef" @refresh="onRefresh"></edit-comp>

	<pm-dialog v-model:visible="visible" title="开始结算" width="480px">
		<template v-if="visible">
			<m-form ref="formRef" :colon="false" :label-col="{ style: { width: '95px' } }" autocomplete="off">
				<m-form-item label="结算单类型" name="costType">
					<m-select v-model:value="formState.costType" :options="options.costTypeList" />
				</m-form-item>
				<m-form-item label="结算范围" name="excludeToday">
					<m-radio-group v-model:value="formState.excludeToday">
						<m-radio style="height: 32px; line-height: 32px" :value="true">
							今天之前的课程（不含今天）
						</m-radio>
						<m-radio style="height: 32px; line-height: 32px" :value="false">今天和之前的课程</m-radio>
					</m-radio-group>
				</m-form-item>
			</m-form>
		</template>

		<template #footer>
			<m-button plain @click="visible = false">取消</m-button>
			<m-button :loading="loading" type="primary" @click="onConfirm">确定</m-button>
		</template>
	</pm-dialog>
	<edit-course-table-comp ref="editCourseTableRef" @refresh="onRefresh"></edit-course-table-comp>
</template>
<script lang="ts">
import { defineComponent, ref, reactive, toRefs } from 'vue';
import { ModelController } from 'admin-library';

import EditCourseTableComp from './comps/edit-course-table-dialog/index.vue';
import { COLUMNS } from './config';
import { STATUS_STORE, COST_TYPE_OPTIONS } from './constant';
import { ListStore } from './store';
import { ItemResponse } from './types';

export default defineComponent({
	components: {
		EditCourseTableComp
	},
	setup() {
		const components = {
			editCourseTableRef: ref<InstanceType<typeof EditCourseTableComp>>()
		};

		const constants = {
			COLUMNS,
			APP
		};

		const state = reactive({
			visible: false,
			loading: false,
			formState: {
				costType: 1,
				excludeToday: true
			},
			options: {
				costTypeList: COST_TYPE_OPTIONS
			}
		});

		const controller = new ModelController({
			table: {
				store: ListStore
			},
			search: {
				status: {
					store: STATUS_STORE
				}
			}
		});
		controller.tableRequest();

		const methods = {
			startSettlement() {
				state.formState = {
					costType: 1,
					excludeToday: true
				};
				state.visible = true;
			},
			onConfirm() {
				state.visible = false;
				components.editCourseTableRef.value.open({
					settlementDetail: false,
					excludeToday: state.formState.excludeToday
				});
			},
			onView(row: ItemResponse) {
				state.visible = false;
				components.editCourseTableRef.value.open({
					settlementDetail: true,
					settlementId: row.id
				});
			},
			onRefresh() {
				controller.tableRequest();
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			controller
		};
	}
});
</script>
