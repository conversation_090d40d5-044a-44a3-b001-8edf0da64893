import { getMapfromArray, getStorefromArray } from '@/shard/utils';

export const COST_TYPE_OPTIONS = [
	{
		label: '课时费结算',
		value: 1
	}
	// {
	// 	label: '奖金结算',
	// 	value: 2
	// }
];
export const COST_TYPE_MAP = getMapfromArray(COST_TYPE_OPTIONS);

export const STATUS_OPTIONS = [
	{
		label: '待审核',
		value: 0
	},
	{
		label: '结算中',
		value: 1
	},
	{
		label: '结算完成',
		value: 2
	},
	{
		label: '审核拒绝',
		value: 3
	}
];
export const STATUS_MAP = getMapfromArray(STATUS_OPTIONS);
export const STATUS_STORE = getStorefromArray(STATUS_OPTIONS);

export const PAY_STATUS_OPTIONS = [
	{
		label: '待支付',
		value: 0
	},
	{
		label: '支付中',
		value: 1
	},
	{
		label: '支付成功',
		value: 2
	},
	{
		label: '支付失败',
		value: 3
	},
	{
		label: '已废弃',
		value: 4
	}
];
export const PAY_STATUS_MAP = getMapfromArray(PAY_STATUS_OPTIONS);
