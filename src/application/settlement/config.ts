import { ColumnXtype, TableColumn, TableDateFormat } from 'admin-library';
import { ColumnWidthEnum } from '@/shard/constant';
import { COST_TYPE_MAP, STATUS_MAP } from './constant';

export const COLUMNS: TableColumn[] = [
	{
		title: '#',
		dataIndex: 'id',
		fixed: 'left'
	},
	{
		title: 'OA流程Id',
		dataIndex: 'oaId',
		xtype: ColumnXtype.CUSTOM,
		fixed: 'left'
	},
	{
		title: '申请单编号',
		dataIndex: 'oaNumber'
	},
	{
		title: '结算单类型',
		dataIndex: 'type',
		render: data => {
			return COST_TYPE_MAP[data];
		},
		width: ColumnWidthEnum.TEXT5
	},
	{
		title: '状态',
		dataIndex: 'status',
		render: data => {
			return STATUS_MAP[data];
		},
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '创建人',
		dataIndex: 'createUserName',
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '结算项总数',
		dataIndex: 'size',
		width: ColumnWidthEnum.TEXT5
	},
	{
		title: '非特殊结算数',
		dataIndex: 'normalSize',
		width: ColumnWidthEnum.TEXT6
	},
	{
		title: '特殊结算数',
		dataIndex: 'abnormalSize',
		width: ColumnWidthEnum.TEXT5
	},
	{
		title: '创建时间',
		dataIndex: 'createTime',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	}
];
