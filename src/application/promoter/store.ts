import {
	List,
	Del,
	Create,
	Update,
	UpdateEmployeeStatus,
	UpdateLecturerStatus,
	StartLogin,
	GetQrcode,
	GetWelcome,
	SetWelcome,
	RefreshWxId
} from '@/store/promoter';
import { SsoUserList } from '@/store/common';
import { ItemResponse } from '@/shard/types';

export const ListStore = new List<Array<ItemResponse>>({});
export const DelStore = new Del({});
export const CreateStore = new Create({});
export const UpdateStore = new Update({});
export const UpdateEmployeeStatusStore = new UpdateEmployeeStatus({});
export const UpdateLecturerStatusStore = new UpdateLecturerStatus({});

export const StartLoginStore = new StartLogin<ItemResponse>({});
export const GetQrcodeStore = new GetQrcode<ItemResponse>({});

export const GetWelcomeStore = new GetWelcome<ItemResponse>({});
export const SetWelcomeStore = new SetWelcome({});

export const SsoUserListStore = new SsoUserList<Array<ItemResponse>>({});

export const RefreshWxIdStore = new RefreshWxId({});
