import { getMapfromArray, getStorefromArray } from '@/shard/utils';
import { ColorEnum } from '@/shard/constant';

export const PTYPE_OPTIONS = [
	{
		label: '全职',
		value: 1
	},
	{
		label: '兼职',
		value: 2
	}
];
export const PTYPE_MAP = getMapfromArray(PTYPE_OPTIONS);
export const PTYPE_STORE = getStorefromArray(PTYPE_OPTIONS);

export const STATION_OPTIONS = [
	{
		label: '销售',
		value: 1
	},
	{
		label: '督导',
		value: 5
	},
	{
		label: '讲师',
		value: 6
	}
];
export const STATION_MAP = getMapfromArray(STATION_OPTIONS);
export const STATION_STORE = getStorefromArray(STATION_OPTIONS);

export const STATUS_OPTIONS = [
	{
		label: '待上岗',
		value: 10,
		styleclass: ColorEnum.warning
	},
	{
		label: '已上岗',
		value: 20,
		styleclass: ColorEnum.success
	},
	{
		label: '留岗察看',
		value: 21,
		styleclass: ColorEnum.danger
	},
	{
		label: '已离职',
		value: 30,
		styleclass: ColorEnum.info
	}
];
export const STATUS_MAP = getMapfromArray(STATUS_OPTIONS);
export const STATUS_STORE = getStorefromArray(STATUS_OPTIONS);
