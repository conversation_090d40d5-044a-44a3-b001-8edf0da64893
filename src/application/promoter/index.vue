<template>
	<pm-effi :controller="controller">
		<pm-search>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '员工姓名'
				}"
				data-index="employeeName"
				xtype="INPUT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '对外昵称'
				}"
				data-index="nickName"
				xtype="INPUT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '员工Id'
				}"
				data-index="id"
				xtype="INPUT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '员工类型'
				}"
				data-index="type"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '员工岗位'
				}"
				data-index="station"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '上岗状态'
				}"
				data-index="status"
				xtype="SELECT"
			/>
		</pm-search>

		<pm-table
			:columns="COLUMNS"
			:use-custom-column="true"
			:operations-fixed="true"
			:operations-width="240"
			:sort-num="false"
			:scroll="{
				x: 'max-content'
			}"
			:antd-props="{
				customRow: record => {
					return {
						onClick: () => {
							onClick(record);
						}
					};
				}
			}"
			:rowClassName="setInstallClassName"
		>
			<template #beforeCustom>
				<m-button type="primary" @click="onCreate">添加</m-button>
			</template>
			<template #wecomUserId="{ record }">
				<div v-if="record.wecomUserId">
					{{ record.wecomUserId }}
				</div>
				<m-button v-else type="link" @click="onRefreshWxUserId(record.employeeNo)">获取并更新</m-button>
			</template>
			<template #status="{ record }">
				<span
					class="cursor-pointer"
					@click="onEditStatus(record)"
					v-html="
						renderTextWithColor(record.status, STATUS_OPTIONS, {
							isTag: true,
							inverse: false,
							isReturnStr: true
						})
					"
				></span>
			</template>

			<template #operations="{ record }">
				<m-button type="link" @click="onEdit(record)">编辑</m-button>
				<m-button v-if="record.station === 5" type="link" @click="toStudent(record)">Ta的学员</m-button>
				<m-button type="link" @click="setWelcome(record)">配置欢迎语</m-button>
				<m-button v-if="record.enableAutomaticHosting" type="link" @click="login(record)">云真机登录</m-button>
				<!-- <m-button danger type="link" @click="onDel(record)">删除</m-button> -->
			</template>
		</pm-table>
	</pm-effi>

	<pm-dialog v-model:visible="qrcodeVisible" title="登录" width="340px">
		<div v-if="qrcodeImg">
			<img :src="qrcodeImg" width="300" />
		</div>
		<div v-else>登录二维码生成中</div>
		<template #footer>
			<m-button type="primary" @click="cancelLogin">确定</m-button>
		</template>
	</pm-dialog>
	<edit-comp ref="editRef" @refresh="onRefresh"></edit-comp>
	<edit-welcome-comp ref="editWelcomeRef"></edit-welcome-comp>
	<edit-status-comp ref="editStatusRef" @refresh="onRefresh"></edit-status-comp>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { ModelController, MUtils, MESSAGE_TYPE, PaasPostMessage } from 'admin-library';

import EditComp from './comps/edit-dialog/index.vue';
import EditWelcomeComp from './comps/edit-welcome-dialog/index.vue';
import EditStatusComp from './comps/edit-status-dialog/index.vue';
import { confirmMessageBox, renderTextWithColor } from '@/shard/utils';
import { COLUMNS } from './config';
import { PTYPE_STORE, STATION_STORE, STATUS_STORE, STATUS_OPTIONS } from './constant';
import { ListStore, DelStore, StartLoginStore, GetQrcodeStore, RefreshWxIdStore } from './store';
import { ItemResponse } from './types';

let qrcodeTimer = null;

export default defineComponent({
	components: {
		EditComp,
		EditWelcomeComp,
		EditStatusComp
	},
	setup() {
		const components = {
			editRef: ref<InstanceType<typeof EditComp>>(),
			editWelcomeRef: ref<InstanceType<typeof EditWelcomeComp>>(),
			editStatusRef: ref<InstanceType<typeof EditStatusComp>>()
		};

		const constants = {
			COLUMNS,
			STATUS_OPTIONS
		};

		const state = reactive({
			selectedId: 0,
			qrcodeVisible: false,
			qrcodeImg: ''
		});

		const controller = new ModelController({
			table: {
				store: ListStore
			},
			search: {
				type: {
					store: PTYPE_STORE
				},
				station: {
					store: STATION_STORE
				},
				status: {
					store: STATUS_STORE
				}
			}
		});
		controller.tableRequest();

		const methods = {
			renderTextWithColor,
			onClick(recored) {
				state.selectedId = recored.id;
			},
			setInstallClassName(record) {
				if (record.id === state.selectedId) {
					return 'selected-row';
				}
			},
			onCreate() {
				components.editRef.value.open();
			},

			onEdit(row: ItemResponse) {
				components.editRef.value.open(row);
			},
			async onRefreshWxUserId(employeeNo) {
				await RefreshWxIdStore.request({ employeeNo }).getData();
				methods.onRefresh();
				MUtils.toast('更新成功', MESSAGE_TYPE.success);
			},
			toStudent(row) {
				PaasPostMessage.post('navigation.to', '/#/membership-student', {
					title: 'Ta的学员',
					query: {
						supervisorId: row?.id
					},
					target: '_tab'
				});
			},
			async setWelcome(row) {
				components.editWelcomeRef.value.open({ employeeId: row.id });
			},
			onEditStatus(row: ItemResponse) {
				components.editStatusRef.value.open({
					...row,
					employeeId: row.id,
					remark: row.latestStatusChangeRemark
				});
			},

			async login(row: ItemResponse) {
				const t = await StartLoginStore.request({ promoterNo: row.employeeNo }).getData();
				state.qrcodeVisible = true;
				state.qrcodeImg = '';
				const getCode = async () => {
					const v = await GetQrcodeStore.request({ taskNo: t.value }).getData();
					if (v.qrCodeImg) {
						this.qrcodeImg = 'data:image/jpeg;base64,' + v.qrCodeImg;
						clearInterval(qrcodeTimer);
					}
				};
				qrcodeTimer = setInterval(getCode, 2000);
			},

			cancelLogin() {
				state.qrcodeVisible = false;
				clearInterval(qrcodeTimer);
			},

			async onDel(row: ItemResponse) {
				await confirmMessageBox('确认删除吗？');

				await DelStore.request({ id: row.id }).getData();
				methods.onRefresh();
				MUtils.toast('删除成功', MESSAGE_TYPE.success);
			},

			onRefresh() {
				controller.tableRequest();
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			controller
		};
	}
});
</script>
