import { TableColumn, ColumnXtype, TableDateFormat } from 'admin-library';
import { renderCopyComp } from '@/shard/utils';
import { ColumnWidthEnum, RADIO_MAP } from '@/shard/constant';
import { PTYPE_MAP, STATION_MAP } from './constant';

export const COLUMNS: TableColumn[] = [
	{
		title: '员工Id',
		dataIndex: 'id',
		fixed: 'left'
	},
	{
		title: '员工姓名',
		dataIndex: 'employeeMixName',
		fixed: 'left'
	},
	{
		title: '木仓Id',
		dataIndex: 'mucangId',
		render: data => {
			return renderCopyComp(data);
		},
		width: ColumnWidthEnum.TEXT3
	},
	{
		title: '企微userId',
		dataIndex: 'wecomUserId',
		xtype: ColumnXtype.CUSTOM,
		width: ColumnWidthEnum.TEXT6
	},
	{
		title: '员工类型',
		dataIndex: 'type',
		render: data => {
			return PTYPE_MAP[data];
		},
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '岗位',
		dataIndex: 'station',
		render: data => {
			return STATION_MAP[data];
		},
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '上岗状态',
		dataIndex: 'status',
		xtype: ColumnXtype.CUSTOM,
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '上岗时间',
		dataIndex: 'alreadyWorkedTime',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	},
	{
		title: '是否托管',
		dataIndex: 'enableAutomaticHosting',
		render: data => {
			return RADIO_MAP[data];
		},
		width: ColumnWidthEnum.TEXT4
	}
];
