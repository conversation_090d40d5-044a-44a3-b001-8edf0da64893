<template>
	<pm-dialog v-model:visible="visible" :title="title" width="680px">
		<template v-if="visible">
			<m-form
				ref="formRef"
				:model="formState"
				:colon="false"
				:label-col="{ style: { width: '107px' } }"
				autocomplete="off"
			>
				<m-form-item label="欢迎语" name="content">
					<m-row type="flex" justify="space-between">
						<m-col>客户添加成为联系人后，将立即收到该欢迎语：</m-col>
						<m-col>
							<m-button type="primary" shape="round" @click="addNickname">
								<template #icon><PlusOutlined /></template>
								插入客户昵称
							</m-button>
						</m-col>
					</m-row>
					<m-textarea
						show-count
						style="margin-top: 10px"
						v-model:value="formState.content"
						placeholder="客户添加成为联系人后，将立即收到该欢迎语"
					/>
				</m-form-item>
				<m-row type="flex">
					<m-col flex="107px">
						<m-form-item label="附件"></m-form-item>
					</m-col>
					<m-col flex="auto">
						<div v-for="(item, index) in formState.attachments" :key="item.id">
							<m-form-item :name="['attachments', index, 'msgtype']" required>
								<m-row type="flex" align="middle">
									<m-col flex="auto">
										<m-radio-group
											v-model:value="item.msgtype"
											@change="onTypeChange(index)"
											style="width: 100%"
										>
											<m-radio-button
												:value="item.value"
												v-for="item in options.messageType"
												:key="item.value"
											>
												{{ item.label }}
											</m-radio-button>
										</m-radio-group>
									</m-col>
									<m-col>
										<m-button danger type="link" @click="removeItem(index)">删除</m-button>
									</m-col>
								</m-row>
							</m-form-item>
							<m-form-item
								:name="['attachments', index, 'fileKeyObj']"
								label="图片"
								:labelCol="{ span: 0 }"
								required
								v-if="item.msgtype === 'image'"
								extra="图片大小不超过10MB，JPG、PNG、JPEG格式"
							>
								<weixinFileUploadComp fileType="image" :maxSize="10" v-model:value="item.fileKeyObj" />
							</m-form-item>
							<m-form-item v-else-if="item.msgtype === 'link'">
								<div style="display: flex">
									<div style="width: 140px">
										<m-form-item
											:name="['attachments', index, 'fileKeyObj']"
											label="封面图"
											:labelCol="{ span: 0 }"
											required
											extra="封面图(建议上传正方形，5MB以内)"
										>
											<fileUploadComp
												fileType="image"
												:maxSize="5"
												v-model:value="item.fileKeyObj"
											/>
										</m-form-item>
									</div>
									<div style="flex: 1">
										<m-form-item label="链接" :name="['attachments', index, 'url']" required>
											<m-input v-model:value="item.url" placeholder="以http或https开头" />
										</m-form-item>
										<m-form-item label="标题" :name="['attachments', index, 'title']" required>
											<m-input v-model:value="item.title" placeholder="最多20字" />
										</m-form-item>
										<m-form-item label="描述" :name="['attachments', index, 'desc']" required>
											<m-input v-model:value="item.desc" placeholder="最多20字" />
										</m-form-item>
									</div>
								</div>
							</m-form-item>
							<m-form-item v-else-if="item.msgtype === 'miniprogram'">
								<div style="display: flex">
									<div style="width: 140px">
										<m-form-item
											:name="['attachments', index, 'fileKeyObj']"
											label="封面图"
											:labelCol="{ span: 0 }"
											required
											extra="封面图(建议尺寸520*416)"
										>
											<weixinFileUploadComp
												fileType="image"
												:maxSize="10"
												v-model:value="item.fileKeyObj"
											/>
										</m-form-item>
									</div>
									<div style="flex: 1">
										<m-form-item label="标题" :name="['attachments', index, 'title']" required>
											<m-input
												v-model:value="item.title"
												placeholder="输入小程序标题，最多64个字"
											/>
										</m-form-item>
										<m-form-item
											label="appid"
											:name="['attachments', index, 'appid']"
											required
											:rules="noBlank"
										>
											<m-input v-model:value="item.appid" placeholder="小程序appid" />
										</m-form-item>
										<m-form-item
											label="page"
											:name="['attachments', index, 'page']"
											required
											:rules="noBlank"
										>
											<m-input v-model:value="item.page" placeholder="小程序page路径" />
										</m-form-item>
									</div>
								</div>
							</m-form-item>
							<m-form-item
								:name="['attachments', index, 'fileKeyObj']"
								v-else-if="item.msgtype === 'video'"
								label="视频"
								:labelCol="{ span: 0 }"
								required
								extra="大小不超过10MB，MP4格式"
							>
								<weixinFileUploadComp fileType="video" :maxSize="10" v-model:value="item.fileKeyObj" />
							</m-form-item>
							<m-form-item
								:name="['attachments', index, 'fileKeyObj']"
								v-else-if="item.msgtype === 'file'"
								label="文件"
								:labelCol="{ span: 0 }"
								required
								extra="大小不超过10MB，DOC/DOCX/TXT/PDF格式"
							>
								<weixinFileUploadComp fileType="file" :maxSize="10" v-model:value="item.fileKeyObj" />
							</m-form-item>
						</div>
						<m-button
							style="margin-top: 0; margin-bottom: 8px; width: 100%"
							type="dashed"
							@click="addItem"
							v-if="formState.attachments.length < 9"
						>
							<template #icon><PlusOutlined /></template>
							添加图片/视频/文件/网页/小程序
						</m-button>
					</m-col>
				</m-row>
			</m-form>
		</template>

		<template #footer>
			<m-button plain @click="visible = false">取消</m-button>
			<m-button :loading="loading" type="primary" @click="onConfirm">确定</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { MESSAGE_TYPE, MUtils } from 'admin-library';
import { PlusOutlined } from '@ant-design/icons-vue';

import { cloneFromPick, cloneByMerge } from '@/shard/utils';
import { noBlank } from '@/shard/validate';
import fileUploadComp from '@/comp/file-upload/index.vue';
import weixinFileUploadComp from '@/comp/weixin-file-upload/index.vue';
import { GetWelcomeStore, SetWelcomeStore } from '../../store';

const DefaultFormState = {
	employeeId: null,
	content: '',
	attachments: []
};

export default defineComponent({
	components: {
		PlusOutlined,
		fileUploadComp,
		weixinFileUploadComp
	},
	setup() {
		const components = {
			formRef: ref(null)
		};

		const constants = {
			noBlank
		};

		const state = reactive({
			visible: false,
			title: '配置欢迎语',
			loading: false,
			formState: MUtils.deepClone(DefaultFormState),
			options: {
				messageType: [
					{
						label: '图片',
						value: 'image'
					},
					{
						label: '视频',
						value: 'video'
					},
					{
						label: '文件',
						value: 'file'
					},
					{
						label: '网页',
						value: 'link'
					},
					{
						label: '小程序',
						value: 'miniprogram'
					}
				]
			}
		});

		const methods = {
			onTypeChange(index) {
				state.formState.attachments[index].fileKeyObj = null;
			},
			addNickname() {
				state.formState.content += '#客户昵称#';
			},
			removeItem(index) {
				state.formState.attachments.splice(index, 1);
			},
			async addItem() {
				await components.formRef.value.validate();

				state.formState.attachments.push({
					id: +new Date(),
					msgtype: 'image'
				});
			},
			async open(row) {
				let t = await GetWelcomeStore.request({ employeeId: row.employeeId }).getData();
				if (t) {
					let { attachments } = t;
					attachments = t.attachments;
					attachments = JSON.parse(attachments);
					attachments = attachments.map(item => {
						let newItem = { msgtype: item.msgtype };

						if (item.msgtype === 'image') {
							let image = item.image;
							if (image.fileKey) {
								image.fileKeyObj = {
									showUrl: image.fileUrl,
									encodedData: image.fileKey,
									mediaId: image.media_id
								};
							}
							newItem = { ...newItem, ...image };
						} else if (item.msgtype === 'link') {
							let link = item.link;
							if (link.fileKey) {
								link.fileKeyObj = { showUrl: link.picurl, encodedData: link.fileKey };
							}
							newItem = { ...newItem, ...link };
						} else if (item.msgtype === 'miniprogram') {
							let miniprogram = item.miniprogram;
							if (miniprogram.fileKey) {
								miniprogram.fileKeyObj = {
									showUrl: miniprogram.fileUrl,
									encodedData: miniprogram.fileKey,
									mediaId: miniprogram.pic_media_id
								};
							}
							newItem = { ...newItem, ...miniprogram };
						} else if (item.msgtype === 'video') {
							let video = item.video;
							if (video.fileKey) {
								video.fileKeyObj = {
									showUrl: video.fileUrl,
									encodedData: video.fileKey,
									mediaId: video.media_id
								};
							}
							newItem = { ...newItem, ...video };
						} else if (item.msgtype === 'file') {
							let file = item.file;
							if (file.fileKey) {
								file.fileKeyObj = {
									showUrl: file.fileUrl,
									encodedData: file.fileKey,
									mediaId: file.media_id
								};
							}
							newItem = { ...newItem, ...file };
						}
						return newItem;
					});
					t.attachments = attachments;
					state.formState = cloneFromPick({ ...row, ...t }, DefaultFormState);
				} else {
					state.formState = cloneFromPick(row, DefaultFormState);
				}

				state.visible = true;
			},
			async onConfirm() {
				await components.formRef.value.validate();

				try {
					state.loading = true;
					const params = cloneByMerge(state.formState, DefaultFormState);
					const fetchStore = SetWelcomeStore;

					params.attachments = params.attachments.map(item => {
						let { title, fileKeyObj, desc, url, appid, page } = item;
						let newItem = { msgtype: item.msgtype } as {
							msgtype;
							image?: any;
							link?: any;
							miniprogram?: any;
							video?: any;
							file?: any;
						};
						let { mediaId, encodedData } = fileKeyObj || {};
						if (item.msgtype === 'image') {
							newItem.image = { media_id: mediaId, fileKey: encodedData };
						} else if (item.msgtype === 'link') {
							newItem.link = { title, fileKey: encodedData, desc, url };
						} else if (item.msgtype === 'miniprogram') {
							newItem.miniprogram = { title, pic_media_id: mediaId, appid, page, fileKey: encodedData };
						} else if (item.msgtype === 'video') {
							newItem.video = { media_id: mediaId, fileKey: encodedData };
						} else if (item.msgtype === 'file') {
							newItem.file = { media_id: mediaId, fileKey: encodedData };
						}
						return newItem;
					});
					params.attachments = JSON.stringify(params.attachments);

					await fetchStore.request(params).getData();

					state.visible = false;
					MUtils.toast('保存成功', MESSAGE_TYPE.success);
				} finally {
					state.loading = false;
				}
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods
		};
	}
});
</script>
