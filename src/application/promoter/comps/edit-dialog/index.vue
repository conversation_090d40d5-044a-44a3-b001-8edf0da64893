<template>
	<pm-dialog v-model:visible="visible" :title="title" width="680px">
		<template v-if="visible">
			<m-form
				ref="formRef"
				:model="formState"
				:colon="false"
				:label-col="{ style: { width: '95px' } }"
				autocomplete="off"
			>
				<m-form-item v-if="!formState.id" label="手机号" name="employeePhoneNumber">
					<m-input v-model:value="formState.employeePhoneNumber" placeholder="手机号" />
				</m-form-item>
				<m-form-item label="对外昵称" name="nickName" required>
					<m-input v-model:value="formState.nickName" placeholder="名称" />
				</m-form-item>
				<m-form-item label="员工类型" name="type">
					<m-select v-model:value="formState.type" :options="options.typeList" placeholder="员工类型" />
				</m-form-item>
				<m-form-item label="sso账号" name="ssoId" v-if="formState.type === 1" required>
					<m-select
						v-model:value="formState.ssoId"
						@change="change()"
						:options="options.ssoUserList"
						:fieldNames="{ label: 'nickname', value: 'userId' }"
						:allowClear="true"
						placeholder="选择员工"
						notFoundContent="输入中文或拼音查询员工"
						show-search
						:filter-option="false"
						@search="searchUser"
					/>
				</m-form-item>
				<m-form-item label="姓名" name="employeeName" required>
					<m-input
						v-model:value="formState.employeeName"
						placeholder="姓名"
						:disabled="formState.type === 1"
					/>
				</m-form-item>
				<m-form-item label="是否开通企微" v-if="formState.type === 1" name="enableCreateWechat">
					<m-checkbox v-model:checked="formState.enableCreateWechat">开通企微</m-checkbox>
				</m-form-item>
				<m-form-item label="岗位" name="station">
					<m-select
						:disabled="formState.id"
						v-model:value="formState.station"
						:options="options.stationList"
						:allowClear="true"
						placeholder="员工类型"
					/>
				</m-form-item>
				<m-form-item label="上岗状态" name="status" v-if="!formState.id">
					<m-select
						v-model:value="formState.status"
						:options="options.statusList"
						:allowClear="true"
						placeholder="上岗状态"
					/>
				</m-form-item>
				<m-form-item label="是否托管" name="enableAutomaticHosting">
					<m-select
						v-model:value="formState.enableAutomaticHosting"
						:options="options.radioOptions"
						placeholder="是否托管"
					/>
				</m-form-item>
			</m-form>
		</template>

		<template #footer>
			<m-button plain @click="visible = false">取消</m-button>
			<m-button :loading="loading" type="primary" @click="onConfirm">确定</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { MESSAGE_TYPE, MUtils } from 'admin-library';
import { debounce } from 'lodash';

import { cloneFromPick, cloneByMerge } from '@/shard/utils';
import { RADIO_OPTIONS } from '@/shard/constant';
import { PTYPE_OPTIONS, STATION_OPTIONS, STATUS_OPTIONS } from '../../constant';
import { CreateStore, UpdateStore, SsoUserListStore } from '../../store';

const DefaultFormState = {
	id: null,
	employeePhoneNumber: '',
	employeeName: '',
	enableCreateWechat: false,
	nickName: '',
	type: 1,
	station: '',
	status: '',
	enableAutomaticHosting: false
};

export default defineComponent({
	emits: ['refresh'],
	setup(props, { emit }) {
		const components = {
			formRef: ref(null)
		};

		const constants = {};

		const state = reactive({
			visible: false,
			title: '',
			loading: false,
			formState: MUtils.deepClone(DefaultFormState),
			options: {
				typeList: PTYPE_OPTIONS,
				stationList: STATION_OPTIONS.map(item => {
					return { ...item, disabled: item.value === 6 };
				}),
				radioOptions: RADIO_OPTIONS,
				statusList: STATUS_OPTIONS,
				ssoUserList: []
			}
		});

		const methods = {
			open(row?: any) {
				state.formState = MUtils.deepClone(DefaultFormState);
				state.title = row?.id ? '编辑' : '新增';

				if (row) {
					state.formState = cloneFromPick(row, DefaultFormState);
				}

				state.visible = true;
			},
			change() {
				let user = state.options.ssoUserList.find(item => item.userId === state.formState.ssoId);
				if (user) {
					state.formState.employeeName = user.nickname;
				}
			},
			async onConfirm() {
				await components.formRef.value.validate();

				try {
					state.loading = true;
					const params = cloneByMerge(state.formState, DefaultFormState);
					const fetchStore = state.formState.id ? UpdateStore : CreateStore;

					await fetchStore.request(params).getData();

					emit('refresh');
					state.visible = false;
					MUtils.toast('保存成功', MESSAGE_TYPE.success);
				} finally {
					state.loading = false;
				}
			},
			searchUser: debounce(async value => {
				const res = await SsoUserListStore.request({ keyword: value }).getData();
				state.options.ssoUserList = res;
			}, 220)
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods
		};
	}
});
</script>
