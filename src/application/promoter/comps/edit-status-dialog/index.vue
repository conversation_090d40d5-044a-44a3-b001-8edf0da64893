<template>
	<pm-dialog v-model:visible="visible" :title="title" width="680px">
		<template v-if="visible">
			<m-form
				ref="formRef"
				:model="formState"
				:colon="false"
				:label-col="{ style: { width: '95px' } }"
				autocomplete="off"
			>
				<m-form-item label="上岗状态" name="status" required>
					<m-select v-model:value="formState.status" :options="options.statusList" placeholder="上岗状态" />
					<m-checkbox
						v-if="roleType === 'lecturer' && formState.status === 30"
						style="margin-top: 8px"
						v-model:checked="formState.delWechat"
					>
						<span style="color: red">同时删除企微</span>
					</m-checkbox>
				</m-form-item>
				<m-form-item label="状态备注" name="remark">
					<m-textarea v-model:value="formState.remark" :rows="2" />
				</m-form-item>
			</m-form>
		</template>

		<template #footer>
			<m-button plain @click="visible = false">取消</m-button>
			<m-button :loading="loading" type="primary" @click="onConfirm">确定</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { MESSAGE_TYPE, MUtils } from 'admin-library';

import { cloneFromPick, cloneByMerge } from '@/shard/utils';
import { STATUS_OPTIONS } from '../../constant';
import { UpdateEmployeeStatusStore, UpdateLecturerStatusStore } from '../../store';

const DefaultFormState = {
	lecturerId: null,
	employeeId: null,
	status: '',
	remark: '',
	delWechat: true
};

export default defineComponent({
	emits: ['refresh'],
	setup(props, { emit }) {
		const components = {
			formRef: ref(null)
		};

		const constants = {};

		const state = reactive({
			visible: false,
			title: '上岗状态',
			loading: false,
			formState: MUtils.deepClone(DefaultFormState),
			options: {
				statusList: STATUS_OPTIONS
			},
			roleType: ''
		});

		const methods = {
			open(row?: any, type?) {
				state.formState = MUtils.deepClone(DefaultFormState);
				state.roleType = type;

				if (row) {
					state.formState = cloneFromPick(row, DefaultFormState);
				}

				state.visible = true;
			},
			async onConfirm() {
				await components.formRef.value.validate();

				try {
					state.loading = true;
					const params = cloneByMerge(state.formState, DefaultFormState);
					const fetchStore =
						state.roleType === 'lecturer' ? UpdateLecturerStatusStore : UpdateEmployeeStatusStore;

					params.delWechat = state.roleType === 'lecturer' && params.status === 30 && params.delWechat;

					await fetchStore.request(params).getData();

					emit('refresh');
					state.visible = false;
					MUtils.toast('保存成功', MESSAGE_TYPE.success);
				} finally {
					state.loading = false;
				}
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods
		};
	}
});
</script>
