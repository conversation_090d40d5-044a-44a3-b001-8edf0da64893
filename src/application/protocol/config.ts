import { TableColumn, TableDateFormat } from 'admin-library';
import { renderCopyComp, renderTextWithColor } from '@/shard/utils';
import { ColumnWidthEnum, RADIO_MAP } from '@/shard/constant';
import {
	STATUS_OPTIONS,
	PLAN_TYPE_OPTIONS,
	QUOTE_CHANNEL_OPTIONS,
	ORDER_STATUS_OPTIONS,
	ORDER_TYPE_OPTIONS
} from './constant';

export const COLUMNS: TableColumn[] = [
	{
		title: '协议编码',
		dataIndex: 'protocolNo',
		fixed: 'left'
	},
	{
		title: '客户姓名',
		dataIndex: 'customerNickName',
		fixed: 'left'
	},
	{
		title: '学员姓名',
		dataIndex: 'studentName'
	},
	{
		title: '客户编码',
		dataIndex: 'customerNo'
	},
	{
		title: '客户木仓Id',
		dataIndex: 'customerMucangId',
		render: data => {
			return renderCopyComp(data);
		},
		width: ColumnWidthEnum.TEXT5
	},
	{
		title: '员工姓名',
		dataIndex: 'promoterName',
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '员工编码',
		dataIndex: 'promoterNo'
	},
	{
		title: '商品名',
		dataIndex: 'productName'
	},
	{
		title: '报价金额(元)',
		dataIndex: 'quoteAmount',
		width: ColumnWidthEnum.TEXT6
	},
	{
		title: '实付金额(元)',
		dataIndex: 'orderAmount',
		width: ColumnWidthEnum.TEXT6
	},
	{
		title: '已退金额(元)',
		dataIndex: 'refundedAmount',
		width: ColumnWidthEnum.TEXT6
	},
	{
		title: '客户来源渠道',
		dataIndex: 'promoteChannel',
		width: ColumnWidthEnum.TEXT6
	},
	{
		title: '报价渠道',
		dataIndex: 'quoteChannel',
		render: data => {
			return renderTextWithColor(data, QUOTE_CHANNEL_OPTIONS, { isTag: true });
		},
		width: ColumnWidthEnum.TEXT6
	},
	{
		title: '报价单状态',
		dataIndex: 'status',
		render: data => {
			return renderTextWithColor(data, STATUS_OPTIONS);
		},
		width: ColumnWidthEnum.TEXT5
	},
	{
		title: '订单状态',
		dataIndex: 'orderStatus',
		render: data => {
			return renderTextWithColor(data, ORDER_STATUS_OPTIONS);
		},
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '订单支付类型',
		dataIndex: 'payPlanType',
		render: data => {
			return renderTextWithColor(data, PLAN_TYPE_OPTIONS, { isTag: true });
		},
		width: ColumnWidthEnum.TEXT6
	},
	{
		title: '是否VIP下单',
		dataIndex: 'vipWhenPaid',
		render: data => {
			return RADIO_MAP[data];
		},
		width: ColumnWidthEnum.TEXT6
	},
	{
		title: '起草时间',
		dataIndex: 'draftTime',
		type: 'date',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	},
	{
		title: '生效时间',
		dataIndex: 'effectiveTime',
		type: 'date',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	},
	{
		title: '支付时间',
		dataIndex: 'paidTime',
		type: 'date',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	},
	{
		title: '废弃时间',
		dataIndex: 'revokeTime',
		type: 'date',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	},
	{
		title: '订单编码',
		dataIndex: 'orderNo'
	},
	{
		title: '订单类型',
		dataIndex: 'orderType',
		render: data => {
			return renderTextWithColor(data, ORDER_TYPE_OPTIONS);
		},
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '关联订单',
		dataIndex: 'orderRef',
		width: ColumnWidthEnum.TEXT4
	}
];
