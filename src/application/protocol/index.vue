<template>
	<pm-effi :controller="controller">
		<pm-search>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '员工编码'
				}"
				data-index="promoterNo"
				xtype="INPUT"
			/>
			<pm-search-single
				:model-value="customerNo"
				:span="3"
				:antdProps="{
					placeholder: '客户编码'
				}"
				data-index="customerNo"
				xtype="INPUT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '客户木仓Id'
				}"
				data-index="customerMucangId"
				xtype="INPUT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '学员姓名'
				}"
				data-index="studentName"
				xtype="INPUT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '订单状态'
				}"
				data-index="status"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="5"
				:antd-props="{
					placeholder: ['生效时间', '结束时间']
				}"
				:model-value="defaultTime"
				xtype="RANGEPICKER"
				data-index="startEffectiveTime|endEffectiveTime"
			/>
			<pm-search-single
				:span="5"
				:antd-props="{
					placeholder: ['订单支付时间', '结束时间']
				}"
				xtype="RANGEPICKER"
				data-index="startOrderPaidTime|endOrderPaidTime"
			/>
		</pm-search>

		<pm-table
			:columns="COLUMNS"
			:use-custom-column="true"
			:operations="false"
			:sort-num="false"
			:scroll="{
				x: 'max-content'
			}"
		></pm-table>
	</pm-effi>
</template>

<script lang="ts">
import Dayjs from 'dayjs';
import { defineComponent, reactive, toRefs } from 'vue';
import { ModelController } from 'admin-library';
import { useRoute } from 'vue-router';
import { recentWeek } from '@paas/paas-library';

import { COLUMNS } from './config';
import { STATUS_STORE } from './constant';
import { ListStore, WechatListStore } from './store';

export default defineComponent({
	setup() {
		const route = useRoute();
		const query = route.query;
		const components = {};

		const constants = {
			COLUMNS
		};

		const state = reactive({
			customerNo: query.customerNo,
			defaultTime: recentWeek().map(date => Dayjs(date))
		});
		
		console.log(route);

		const controller = new ModelController({
			table: {
				store: route.name === 'wechat-protocol' ? WechatListStore : ListStore
			},
			search: {
				status: {
					store: STATUS_STORE
				}
			}
		});
		controller.tableRequest();

		const methods = {};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			controller
		};
	}
});
</script>
