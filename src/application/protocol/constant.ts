import { getMapfromArray, getStorefromArray } from '@/shard/utils';
import { ColorEnum } from '@/shard/constant';

export const STATUS_OPTIONS = [
	{
		label: '待确认',
		value: 1,
		styleclass: ColorEnum.warning
	},
	{
		label: '已支付',
		value: 2,
		styleclass: ColorEnum.primary
	},
	{
		label: '部分支付',
		value: 3,
		styleclass: ColorEnum.orange
	},
	{
		label: '已生效',
		value: 10,
		styleclass: ColorEnum.success
	},
	{
		label: '已废弃',
		value: 99,
		styleclass: ColorEnum.info
	}
];
export const STATUS_MAP = getMapfromArray(STATUS_OPTIONS);
export const STATUS_STORE = getStorefromArray(STATUS_OPTIONS);

export const ORDER_STATUS_OPTIONS = [
	{
		label: '待支付',
		value: 1,
		styleclass: ColorEnum.warning
	},
	{
		label: '已支付',
		value: 2,
		styleclass: ColorEnum.primary
	},
	{
		label: '部分支付',
		value: 3,
		styleclass: ColorEnum.orange
	},
	{
		label: '已履约',
		value: 10,
		styleclass: ColorEnum.success
	},
	{
		label: '部分履约',
		value: 11,
		styleclass: ColorEnum.geekblue
	},
	{
		label: '待退款',
		value: 20,
		styleclass: ColorEnum.warning
	},
	{
		label: '退款成功',
		value: 21,
		styleclass: ColorEnum.success
	},
	{
		label: '退款失败',
		value: 22,
		styleclass: ColorEnum.danger
	}
];
export const ORDER_STATUS_MAP = getMapfromArray(ORDER_STATUS_OPTIONS);
export const ORDER_STATUS_STORE = getStorefromArray(ORDER_STATUS_OPTIONS);

export const PLAN_TYPE_OPTIONS = [
	{
		label: '单笔支付',
		value: 1,
		styleclass: ColorEnum.geekblue
	},
	{
		label: '多笔支付',
		value: 2,
		styleclass: ColorEnum.pink
	},
	{
		label: '定金支付',
		value: 3,
		styleclass: ColorEnum.purple
	}
];
export const PLAN_TYPE_MAP = getMapfromArray(PLAN_TYPE_OPTIONS);
export const PLAN_TYPE_STORE = getStorefromArray(PLAN_TYPE_OPTIONS);

export const QUOTE_CHANNEL_OPTIONS = [
	{
		label: '企业微信',
		value: 'weCom',
		styleclass: ColorEnum.pink
	},
	{
		label: '驾考IM',
		value: 'jkIm',
		styleclass: ColorEnum.cyan
	},
	{
		label: '直播间',
		value: 'live',
		styleclass: ColorEnum.geekblue
	}
];
export const QUOTE_CHANNEL_MAP = getMapfromArray(QUOTE_CHANNEL_OPTIONS);

export const ORDER_TYPE_OPTIONS = [
	{
		label: '普通订单',
		value: 1,
		styleclass: ColorEnum.geekblue
	},
	{
		label: '升级订单',
		value: 2,
		styleclass: ColorEnum.pink
	}
];
export const ORDER_TYPE_MAP = getMapfromArray(ORDER_TYPE_OPTIONS);
export const ORDER_TYPE_STORE = getStorefromArray(ORDER_TYPE_OPTIONS);
