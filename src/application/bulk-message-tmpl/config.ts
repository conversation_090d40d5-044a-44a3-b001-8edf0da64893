import { TableColumn, TableDateFormat } from 'admin-library';
import { ColumnWidthEnum } from '@/shard/constant';
import { RECEIVER_TYPE_MAP } from '@/application/bulk-message/constant';

export const COLUMNS: TableColumn[] = [
	{
		title: '#',
		dataIndex: 'id'
	},
	{
		title: '素材标题',
		dataIndex: 'title'
	},
	{
		title: '素材类型',
		dataIndex: 'type',
		render: data => {
			return RECEIVER_TYPE_MAP[data];
		},
		width: 160
	},
	{
		title: '使用次数',
		dataIndex: 'useTimes',
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '创建时间',
		dataIndex: 'createTime',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	},
	{
		title: '更新时间',
		dataIndex: 'updateTime',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	},
	{
		title: '操作人',
		dataIndex: 'updateUserName',
		width: ColumnWidthEnum.TEXT4
	}
];
