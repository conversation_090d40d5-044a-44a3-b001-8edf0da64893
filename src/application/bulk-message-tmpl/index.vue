<template>
	<pm-effi :controller="controller">
		<pm-search>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '素材类型'
				}"
				data-index="type"
				xtype="SELECT"
			/>
		</pm-search>
		<pm-table
			:columns="COLUMNS"
			:use-custom-column="true"
			:operations-fixed="false"
			:operations-width="160"
			:sort-num="false"
			:scroll="{
				x: 'max-content'
			}"
		>
			<template #beforeCustom>
				<m-button type="primary" @click="onCreate()">添加</m-button>
			</template>
			<template #operations="{ record }">
				<m-button type="link" @click="onEdit(record)">编辑</m-button>
				<m-button type="link" @click="onCopy(record)">复制</m-button>
				<m-button type="link" @click="goUse(record)">去使用</m-button>
			</template>
		</pm-table>
	</pm-effi>

	<edit-comp ref="editRef" @refresh="onRefresh"></edit-comp>
	<batch-edit-comp ref="batchEditRef" @refresh="onRefresh"></batch-edit-comp>
</template>
<script lang="ts">
import { defineComponent, ref, reactive, toRefs } from 'vue';
import { ModelController } from 'admin-library';

import BatchEditComp from '@/application/bulk-message/comps/edit-dialog/index.vue';
import EditComp from './comps/edit-dialog/index.vue';
import { COLUMNS } from './config';
import { RECEIVER_TYPE_STORE } from '@/application/bulk-message/constant';
import { ListStore } from './store';
import { ItemResponse } from './types';

export default defineComponent({
	components: {
		EditComp,
		BatchEditComp
	},
	setup() {
		const components = {
			editRef: ref<InstanceType<typeof EditComp>>(),
			batchEditRef: ref<InstanceType<typeof BatchEditComp>>()
		};

		const constants = {
			COLUMNS
		};

		const state = reactive({});

		const controller = new ModelController({
			table: {
				store: ListStore
			},
			search: {
				type: {
					store: RECEIVER_TYPE_STORE
				}
			}
		});
		controller.tableRequest();

		const methods = {
			onCreate() {
				components.editRef.value.open();
			},
			onEdit(row: ItemResponse) {
				components.editRef.value.open(row);
			},
			async onCopy(row: ItemResponse) {
				components.editRef.value.open({ ...row, id: null });
			},
			goUse(row: ItemResponse) {
				components.batchEditRef.value.open({
					receiverType: row.type,
					contentData: row.contentData,
					contentResource: true,
					materialId: row.id
				});
			},
			onRefresh() {
				controller.tableRequest();
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			controller
		};
	}
});
</script>
