<template>
	<pm-dialog v-model:visible="visible" :title="title" width="680px">
		<template v-if="visible">
			<m-form
				ref="formRef"
				:model="formState"
				:colon="false"
				:label-col="{ style: { width: '95px' } }"
				autocomplete="off"
			>
				<m-form-item label="素材标题" name="title" required>
					<m-input v-model:value="formState.title" :maxlength="20" placeholder="输入模版名称，20字以内" />
				</m-form-item>
				<m-form-item label="素材类型" name="type" required>
					<m-select
						v-model:value="formState.type"
						:options="options.typeList"
						:allowClear="true"
						placeholder="素材类型"
						:disabled="formState.id"
					/>
				</m-form-item>
				<m-form-item label="素材内容">
					<div v-for="(item, index) in formState.contentData" :key="item.uid">
						<m-row justify="space-between" align="middle">
							<m-col></m-col>
							<m-col>
								<div style="font-size: 14px; line-height: 1.6; text-align: center">
									消息{{ index + 1 }}
								</div>
							</m-col>
							<m-col>
								<m-button
									v-if="formState.contentData.length > 1"
									danger
									type="link"
									@click="removeItem(index)"
								>
									删除
								</m-button>
							</m-col>
						</m-row>
						<m-divider style="margin: 10px 0; height: 1px; background-color: #f2f7ff" />
						<messagegGoupComp
							:key="formState.type"
							ref="messageRef"
							v-model:value="item.messageData"
							:receiverType="formState.type"
						/>
					</div>
					<m-button
						style="margin-top: 0; margin-bottom: 8px; width: 100%"
						type="dashed"
						@click="addItem"
						:disabled="formState.contentData.length >= 10"
					>
						<template #icon><PlusOutlined /></template>
						继续添加
					</m-button>
				</m-form-item>
			</m-form>
		</template>

		<template #footer>
			<m-button plain @click="visible = false">取消</m-button>
			<m-button :loading="loading" type="primary" @click="onConfirm">确定</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { MESSAGE_TYPE, MUtils } from 'admin-library';
import getUid from 'ant-design-vue/es/vc-upload/uid';
import { isArray } from 'lodash';

import { cloneFromPick, cloneByMerge } from '@/shard/utils';
import messagegGoupComp from '@/application/bulk-message/comps/edit-dialog/message-group.vue';
import { RECEIVER_TYPE_OPTIONS } from '@/application/bulk-message/constant';
import { CreateStore, UpdateStore } from '../../store';

const DefaultFormState = {
	id: null,
	title: '',
	type: 1,
	contentData: [
		{
			messageData: {
				messageType: 1,
				messageContent: {}
			}
		}
	]
};

export default defineComponent({
	components: {
		messagegGoupComp
	},
	emits: ['refresh'],
	setup(props, { emit }) {
		const components = {
			formRef: ref(null),
			messageRef: ref(null)
		};

		const constants = {};

		const state = reactive({
			visible: false,
			title: '',
			loading: false,
			formState: MUtils.deepClone(DefaultFormState),
			options: {
				typeList: RECEIVER_TYPE_OPTIONS
			}
		});

		const getValidate = () => {
			let list = [];

			if (isArray(components.messageRef.value)) {
				list = components.messageRef.value.map(item => item.validate());
			} else {
				list = [components.messageRef.value.validate()];
			}
			return list;
		};

		const methods = {
			removeItem(index) {
				state.formState.contentData.splice(index, 1);
			},
			async addItem() {
				await Promise.all(getValidate());

				state.formState.contentData.push({
					messageData: {
						messageType: 1,
						messageContent: {}
					},
					uid: getUid()
				});
			},
			open(row?: any) {
				state.formState = MUtils.deepClone(DefaultFormState);
				state.title = row?.id ? '编辑' : '新增';

				if (row) {
					let { contentData } = row;
					contentData = JSON.parse(contentData || '[]');
					contentData = contentData.map(item => {
						let { messageType, messageContent, ...reset } = item;
						let messageData = { messageType, messageContent };

						return { ...reset, messageData, uid: getUid() };
					});

					state.formState = cloneFromPick({ ...row, contentData }, DefaultFormState);
				}

				state.visible = true;
			},

			async onConfirm() {
				await Promise.all([components.formRef.value.validate(), ...getValidate()]);

				try {
					state.loading = true;
					const params = cloneByMerge(state.formState, DefaultFormState);
					const fetchStore = state.formState.id ? UpdateStore : CreateStore;

					let { contentData } = params;
					if (contentData) {
						contentData = contentData.map(item => {
							let { messageData } = item;
							delete item.messageData;
							return {
								...item,
								...messageData
							};
						});
						params.contentData = JSON.stringify(contentData);
					}

					await fetchStore.request(params).getData();

					emit('refresh');
					state.visible = false;
					MUtils.toast('保存成功', MESSAGE_TYPE.success);
				} finally {
					state.loading = false;
				}
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods
		};
	}
});
</script>
