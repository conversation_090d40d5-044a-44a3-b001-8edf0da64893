<template>
	<pm-effi :controller="controller">
		<m-form
			style="padding: 20px 0 0 20px; max-width: 1280px"
			ref="formRef"
			:model="searchParams"
			:label-col="{ style: { width: '140px' } }"
			autocomplete="off"
		>
			<m-row>
				<m-col>
					<m-form-item label="学员" name="studentId">
						<student-select
							:disabled="!!query.studentId"
							style="width: 180px"
							@onSelectChange="onStudentChange"
							v-model:value="searchParams.studentId"
							data-index="studentId"
						/>
					</m-form-item>
				</m-col>
			</m-row>
			<m-row>
				<m-col>
					<m-form-item label="学员信息">
						<div v-if="studentData.studentId" style="display: flex; flex-wrap: wrap">
							<div>学习状态：{{ LEARN_STATUS_MAP[studentData.learnStatus] }}；</div>
							<div>班型：{{ studentData.classTypeName }}；</div>
							<div>私教课时数：{{ studentData.lessonPeriod }}；</div>
							<div>实际课时数：{{ studentData.actualLessonPeriod }}；</div>
							<div>督导老师：{{ studentData.supervisorName }}；</div>
							<div>一对一讲师：{{ studentData.lecturerName || '无' }}；</div>
							<div>销售人员：{{ studentData.salerName }}；</div>
							<div>建议约考：{{ RADIO_MAP[studentData.suggestExam] }}；</div>
							<div>预约考试时间：{{ formatDate2(studentData.reserveExamTime) }}；</div>
						</div>
					</m-form-item>
				</m-col>
			</m-row>
			<m-row>
				<m-col>
					<m-form-item label="销售跟进记录">
						<div v-for="item in commentList" :key="item.id">
							{{ item.content }} --- {{ item.commentUser }}
						</div>
					</m-form-item>
				</m-col>
			</m-row>
			<m-row>
				<m-col :span="16">
					<m-form-item label="学员上课时间段偏好" name="rangeTime" :rules="rangeTime">
						<m-slider
							v-model:value="searchParams.rangeTime"
							:tooltip-open="true"
							:tip-formatter="formatter"
							:min="0"
							:max="90"
							range
						/>
						<div v-if="searchParams.rangeTime">
							{{ searchParams.rangeTime.map(item => formatter(item)).join('至') }}
						</div>
						<div v-else>--</div>
					</m-form-item>
				</m-col>
				<m-col :span="8">
					<m-form-item label="讲师类型" name="lecturerTypeList">
						<m-checkbox-group
							v-model:value="searchParams.lecturerTypeList"
							:options="options.typeList"
						></m-checkbox-group>
					</m-form-item>
				</m-col>
			</m-row>
			<m-row>
				<m-col :span="14">
					<m-form-item label="讲师等级" name="lecturerLevelList">
						<m-checkbox-group v-model:value="searchParams.lecturerLevelList">
							<m-checkbox
								v-for="item in options.levelList"
								:key="item.levelValue"
								:value="item.levelValue"
							>
								{{ item.levelName }}
							</m-checkbox>
						</m-checkbox-group>
					</m-form-item>
				</m-col>
				<m-col :span="10">
					<m-form-item label="讲师层级" name="lecturerLayerList">
						<m-checkbox-group
							v-model:value="searchParams.lecturerLayerList"
							:options="options.layerList"
						></m-checkbox-group>
					</m-form-item>
				</m-col>
			</m-row>
			<m-row>
				<m-col>
					<m-form-item label="预计开始上课日期" name="expectAttendClassDate" required>
						<m-radio-group
							v-model:value="searchParams.expectAttendClassDate"
							:options="options.dateList"
						></m-radio-group>
					</m-form-item>
				</m-col>
			</m-row>
			<m-row>
				<m-col>
					<m-form-item label="其他要求" name="enableNewLecturer">
						<m-checkbox v-model:checked="searchParams.enableNewLecturer">
							从未排过课（启用新讲师）
						</m-checkbox>
					</m-form-item>
				</m-col>
			</m-row>
			<m-row>
				<m-col>
					<m-form-item label=" ">
						<m-button type="primary" @click="onSearch">匹配讲师</m-button>
					</m-form-item>
				</m-col>
			</m-row>
		</m-form>

		<pm-table
			:columns="COLUMNS"
			:use-custom-column="true"
			:pagination="false"
			:operations-fixed="true"
			:operations-width="70"
			:sort-num="false"
			:scroll="{
				x: 'max-content'
			}"
			class="table-no-hover"
		>
			<template #headerCell="{ column }">
				<template v-if="column.key === 'timeline'">
					<m-row type="flex">
						<m-col flex="40px"></m-col>
						<m-col flex="1">
							{{ column.title }}
							<span v-html="timelineDesc"></span>
						</m-col>
					</m-row>
					<m-row type="flex">
						<m-col flex="40px"></m-col>
						<m-col flex="1">
							<header-tick-comp />
						</m-col>
					</m-row>
				</template>
			</template>
			<template #studentCount="{ record }">
				<m-button type="link" @click="toStudent(record)">
					{{ record.learningStudentNum }}/{{ record.suspendStudentNum }}
				</m-button>
			</template>
			<template #timeline="{ record }">
				<div>
					<div v-for="item in record.scheduleGroupList" :key="item.date">
						<m-row type="flex">
							<m-col flex="40px">
								<span>{{ formatDate1(item.date) }}</span>
							</m-col>
							<m-col flex="1">
								<Timeline
									:courses="item.courseScheduleList"
									:freeDurations="item.scheduleDurationList"
									:noSchedule="item.noSchedule"
									:select-day="item.date"
								/>
							</m-col>
						</m-row>
					</div>
				</div>
			</template>
			<template #operations="{ record }">
				<m-button type="link" @click="onCreate(record)">排课</m-button>
			</template>
		</pm-table>
	</pm-effi>

	<batch-create-comp ref="batchCreateRef" @refresh="onRefresh"></batch-create-comp>
</template>

<script lang="ts">
import Dayjs from 'dayjs';
import { defineComponent, reactive, toRefs, ref, watchEffect } from 'vue';
import { ModelController, MUtils, PaasPostMessage } from 'admin-library';
import { useRoute } from 'vue-router';

import StudentSelect from '@/comp/student-select/index.vue';
import HeaderTickComp from '@/application/courses-schedule/comps/timeline/header-tick.vue';
import Timeline, { timelineDesc } from '@/application/courses-schedule/comps/timeline/index.vue';
import BatchCreateComp from '@/application/courses/comps/batch-create-dialog/index.vue';
import { formatTimeStr } from '@/shard/utils';
import { COLUMNS } from './config';
import { RADIO_MAP } from '@/shard/constant';
import { LAYER_OPTIONS, TYPE_OPTIONS } from '@/application/part-time-teacher/constant';
import { LEARN_STATUS_MAP } from '@/application/student/constant';
import { ListStore as LevelListStore } from '@/application/teacher-level-manage/store';
import { RemarkCommentListStore } from '@/popup-pages/student-remark-records/store';
import { RecommendLecturerListStore } from './store';
import { ItemResponse } from './types';

const today = Dayjs();
const dateList = [
	{
		label: `今天(${today.format('M月D日')})`,
		value: today.format('YYYY-MM-DD')
	},
	{
		label: `明天(${today.add(1, 'day').format('M月D日')})`,
		value: today.add(1, 'day').format('YYYY-MM-DD')
	},
	{
		label: `后天(${today.add(2, 'day').format('M月D日')})`,
		value: today.add(2, 'day').format('YYYY-MM-DD')
	}
];

export default defineComponent({
	components: { StudentSelect, HeaderTickComp, Timeline, BatchCreateComp },
	setup() {
		const route = useRoute();
		const query = route.query;
		const components = {
			formRef: ref(null),
			batchCreateRef: ref<InstanceType<typeof BatchCreateComp>>()
		};

		const constants = {
			rangeTime: {
				validator(rule, value) {
					if (value[1] === 0) {
						return Promise.reject('请输入时间段');
					}
					return Promise.resolve();
				}
			},
			COLUMNS,
			RADIO_MAP,
			timelineDesc,
			LEARN_STATUS_MAP,
			query
		};

		let studentId;
		if (query.studentId) {
			studentId = +query.studentId;
		}

		const state = reactive({
			searchParams: {
				studentId,
				rangeTime: [0, 0],
				lecturerTypeList: [],
				lecturerLevelList: [],
				lecturerLayerList: [],
				expectAttendClassDate: '',
				enableNewLecturer: false
			},
			options: {
				typeList: TYPE_OPTIONS,
				levelList: [],
				layerList: LAYER_OPTIONS,
				dateList
			},
			studentData: {} as any,
			commentList: []
		});

		const controller = new ModelController({
			table: {
				store: RecommendLecturerListStore
			}
		});

		controller.table.onRequest.use(params => {
			let searchParams = MUtils.deepClone(state.searchParams);
			let [beginTime, endTime] = searchParams.rangeTime;
			let preferenceBeginTime = methods.formatter(beginTime);
			let preferenceEndTime = methods.formatter(endTime);
			delete searchParams.rangeTime;
			params = {
				...params,
				...searchParams,
				preferenceBeginTime,
				preferenceEndTime,
				lecturerTypeList: searchParams.lecturerTypeList.join(','),
				lecturerLevelList: searchParams.lecturerLevelList.join(','),
				lecturerLayerList: searchParams.lecturerLayerList.join(',')
			};

			return params;
		});

		watchEffect(async () => {
			if (state.searchParams.studentId) {
				let res = await RemarkCommentListStore.request({
					limit: 9999,
					type: 3,
					studentId: state.searchParams.studentId
				}).getData();
				state.commentList = res;
			}
		});

		const methods = {
			formatDate1(date) {
				return Dayjs(date).format('M.D');
			},
			formatDate2(date) {
				if (date) {
					return Dayjs(date).format('YYYY-MM-DD');
				}
			},
			formatter(value) {
				let v = (value + 9 * 6) * 600;
				return formatTimeStr(Math.min(v, 86400 - 1) * 1000, 'HH:mm');
			},
			async onStudentChange(_val, detail) {
				state.studentData = detail;
			},

			async initLevelList() {
				const res = await LevelListStore.request({ limit: 9999 }).getData();
				if (res) {
					state.options.levelList = res;
				}
			},
			onCreate(row: ItemResponse) {
				components.batchCreateRef.value.open({
					lecturerId: row.lecturerId,
					studentId: state.searchParams.studentId
				});
			},
			async onSearch() {
				await components.formRef.value.validate();
				methods.onRefresh();
			},
			toStudent(row) {
				PaasPostMessage.post('navigation.to', '/#/membership-student', {
					title: 'Ta的学员',
					query: {
						lecturerId: row?.lecturerId
					},
					target: '_tab'
				});
			},

			onRefresh() {
				controller.tableRequest();
			}
		};

		methods.initLevelList();

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			controller
		};
	}
});
</script>
<style lang="less">
.ant-slider {
	margin-top: 0;
	height: 20px;
}
.ant-slider-rail,
.ant-slider-step,
.ant-slider-track {
	height: 16px;
}
.ant-slider-handle {
	margin-top: -2px;
	width: 20px;
	height: 20px;
}
</style>
