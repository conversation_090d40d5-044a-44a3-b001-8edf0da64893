import { ColumnXtype, TableColumn } from 'admin-library';
import { ColumnWidthEnum } from '@/shard/constant';
import { LAYER_MAP, TYPE_MAP } from '@/application/part-time-teacher/constant';

export const COLUMNS: TableColumn[] = [
	{
		title: '讲师',
		dataIndex: 'lecturerName',
		render: (value, lineData) => {
			return `${value}（${lineData.lecturerNickName}）`;
		},
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '类型/等级',
		dataIndex: 'position',
		render: (value, lineData) => {
			return `${TYPE_MAP[lineData.lecturerType]}/${lineData.lecturerLevel}`;
		},
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '学员数(学习中/停课)',
		dataIndex: 'studentCount',
		xtype: ColumnXtype.CUSTOM,
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '层级',
		dataIndex: 'lecturerLayer',
		width: ColumnWidthEnum.TEXT2,
		render: data => {
			return LAYER_MAP[data];
		}
	},
	{
		title: '近期日程',
		dataIndex: 'timeline',
		width: 'max',
		xtype: ColumnXtype.CUSTOM
	},
	{
		title: '主管老师',
		dataIndex: 'supervisorTeacherName',
		render: (value, lineData) => {
			return `${value}（${lineData.supervisorTeacherNickName}）`;
		},
		width: ColumnWidthEnum.TEXT4
	}
];
