import { TableColumn, TableDateFormat } from 'admin-library';
import { ColumnWidthEnum } from '@/shard/constant';
import { TYPE_MAP } from './constant';

export const COLUMNS: TableColumn[] = [
	{
		title: '#',
		dataIndex: 'id'
	},
	{
		title: '模版名称',
		dataIndex: 'name'
	},
	{
		title: '模版类型',
		dataIndex: 'type',
		render: data => {
			return TYPE_MAP[data];
		},
		width: 160
	},
	// {
	// 	title: '模版消息周期',
	// 	dataIndex: 'contentData',
	// 	render: data => {
	// 		let str;
	// 		try {
	// 			data = JSON.parse(data);
	// 			str = data.length + '天';
	// 		} catch (error) {
	// 			console.log(error);
	// 		}
	// 		return str;
	// 	},
	// 	width: 160
	// },
	{
		title: '创建时间',
		dataIndex: 'createTime',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	},
	{
		title: '更新时间',
		dataIndex: 'updateTime',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	},
	{
		title: '操作人',
		dataIndex: 'updateUserName',
		width: ColumnWidthEnum.TEXT4
	}
];
