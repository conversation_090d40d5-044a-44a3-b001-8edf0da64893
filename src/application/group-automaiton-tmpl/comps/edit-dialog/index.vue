<template>
	<pm-dialog v-model:visible="visible" :title="title" width="680px">
		<template v-if="visible">
			<m-form
				ref="formRef"
				:model="formState"
				:colon="false"
				:label-col="{ style: { width: '95px' } }"
				autocomplete="off"
			>
				<m-form-item label="模版名称" name="name" required>
					<m-input v-model:value="formState.name" :maxlength="20" placeholder="输入模版名称，20字以内" />
				</m-form-item>
				<m-form-item label="模版类型" name="type" required>
					<m-select
						v-model:value="formState.type"
						:options="options.typeList"
						:allowClear="true"
						placeholder="模版类型"
						:disabled="formState.id"
					/>
				</m-form-item>
				<m-form-item label="内容配置">
					<div
						v-for="(dayItem, dayIndex) in formState.contentData"
						:key="dayItem.uid"
						style="margin-bottom: 10px; background-color: #f2f2f2"
					>
						<m-row justify="space-between" align="middle" style="padding: 8px 8px 10px">
							<m-col></m-col>
							<m-col>
								<div style="font-size: 14px; line-height: 1.6">第{{ dayIndex + 1 }}天</div>
							</m-col>
							<m-col>
								<m-button
									v-if="formState.contentData.length > 1"
									danger
									type="link"
									@click="removeDayItem(dayIndex)"
								>
									删除
								</m-button>
							</m-col>
						</m-row>
						<m-divider style="margin: 0 0 10px; height: 2px; background-color: #fff" />
						<div style="padding: 0 20px 10px">
							<div v-for="(item, index) in dayItem" :key="item.uid">
								<m-row justify="space-between" align="middle" style="margin: 0 0 10px">
									<m-col>
										<div style="font-size: 14px; line-height: 1.6">
											推送{{ formState.type === 2 ? '节点' : '任务' }}{{ index + 1 }}
										</div>
									</m-col>
									<m-col>
										<m-button
											v-if="dayItem.length > 1"
											danger
											type="link"
											@click="removeItem(dayIndex, index)"
										>
											删除
										</m-button>
									</m-col>
								</m-row>
								<div style="padding: 10px 20px 1px; margin-bottom: 10px; background-color: #fff">
									<div style="margin-bottom: 10px">
										<div>推送时间点</div>
										<div>
											<m-form-item
												:name="['contentData', dayIndex, index, 'sendTime']"
												label="推送时间点"
												:labelCol="{ span: 0 }"
												required
											>
												<m-time-picker
													v-model:value="item.sendTime"
													format="HH:mm"
													valueFormat="HH:mm"
												/>
											</m-form-item>
										</div>
									</div>
									<div>
										<div>推送内容</div>
										<div>
											<messagegGoupComp
												:key="formState.type"
												ref="messageRef"
												v-model:value="item.messageData"
												:receiverType="formState.type"
											/>
										</div>
									</div>
								</div>
							</div>
							<m-button
								style="margin-top: 0; margin-bottom: 8px; width: 100%"
								type="dashed"
								@click="addItem(dayIndex)"
							>
								<template #icon><PlusOutlined /></template>
								继续添加
							</m-button>
						</div>
					</div>
					<m-button style="margin-top: 0; margin-bottom: 8px; width: 100%" type="dashed" @click="addDayItem">
						<template #icon><PlusOutlined /></template>
						继续添加
					</m-button>
				</m-form-item>
			</m-form>
		</template>

		<template #footer>
			<m-button plain @click="visible = false">取消</m-button>
			<m-button :loading="loading" type="primary" @click="onConfirm">确定</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { MESSAGE_TYPE, MUtils } from 'admin-library';
import getUid from 'ant-design-vue/es/vc-upload/uid';
import { isArray } from 'lodash';

import { cloneFromPick, cloneByMerge } from '@/shard/utils';
import messagegGoupComp from '@/application/bulk-message/comps/edit-dialog/message-group.vue';
import { TYPE_OPTIONS } from '../../constant';
import { CreateStore, UpdateStore } from '../../store';

const DefaultFormState = {
	id: null,
	name: '',
	type: 2,
	contentData: [
		[
			{
				messageData: {
					messageType: 1,
					messageContent: {}
				}
			}
		]
	]
};

export default defineComponent({
	components: {
		messagegGoupComp
	},
	emits: ['refresh'],
	setup(props, { emit }) {
		const components = {
			formRef: ref(null),
			messageRef: ref(null)
		};

		const constants = {};

		const state = reactive({
			visible: false,
			title: '',
			loading: false,
			formState: MUtils.deepClone(DefaultFormState),
			options: {
				typeList: TYPE_OPTIONS
			}
		});

		const getValidate = () => {
			let list = [];

			if (isArray(components.messageRef.value)) {
				list = components.messageRef.value.map(item => item.validate());
			} else {
				list = [components.messageRef.value.validate()];
			}
			return list;
		};

		const methods = {
			removeDayItem(index) {
				state.formState.contentData.splice(index, 1);
			},
			async addDayItem() {
				await Promise.all(getValidate());

				state.formState.contentData.push([
					{
						messageData: {
							messageType: 1,
							messageContent: {}
						},
						uid: getUid()
					}
				]);
			},
			removeItem(dayIndex, index) {
				state.formState.contentData[dayIndex].splice(index, 1);
			},
			async addItem(dayIndex) {
				await Promise.all(getValidate());

				state.formState.contentData[dayIndex].push({
					messageData: {
						messageType: 1,
						messageContent: {}
					},
					uid: getUid()
				});
			},
			open(row?: any) {
				state.formState = MUtils.deepClone(DefaultFormState);
				state.title = row?.id ? '编辑' : '新增';

				if (row) {
					let { contentData } = row;
					contentData = JSON.parse(contentData || '[]');
					contentData = contentData.map(dayItem => {
						return dayItem.map(item => {
							let { messageType, messageContent, ...reset } = item;
							let messageData = { messageType, messageContent };

							return { ...reset, messageData, uid: getUid() };
						});
					});

					state.formState = cloneFromPick({ ...row, contentData }, DefaultFormState);
				}

				state.visible = true;
			},

			async onConfirm() {
				await Promise.all([components.formRef.value.validate(), ...getValidate()]);

				try {
					state.loading = true;
					const params = cloneByMerge(state.formState, DefaultFormState);
					const fetchStore = state.formState.id ? UpdateStore : CreateStore;

					let { contentData } = params;
					if (contentData) {
						contentData = contentData.map(dayItem => {
							return dayItem.map(item => {
								let { messageData } = item;
								delete item.messageData;
								return {
									...item,
									...messageData
								};
							});
						});
						params.contentData = JSON.stringify(contentData);
					}

					await fetchStore.request(params).getData();

					emit('refresh');
					state.visible = false;
					MUtils.toast('保存成功', MESSAGE_TYPE.success);
				} finally {
					state.loading = false;
				}
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods
		};
	}
});
</script>
