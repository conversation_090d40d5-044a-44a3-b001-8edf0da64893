import { TableColumn, ColumnXtype, TableDateFormat } from 'admin-library';
import { renderCopyComp } from '@/shard/utils';
import { ColumnWidthEnum } from '@/shard/constant';
import { EDUCATION_MAP, GENDER_MAP } from '@/application/customer/constant';
import { CONTRACT_STATUS_MAP, TYPE_MAP, OCCUPATION_MAP } from './constant';

export const COLUMNS: TableColumn[] = [
	{
		title: '讲师Id',
		dataIndex: 'id',
		fixed: 'left'
	},
	{
		title: '姓名',
		dataIndex: 'lecturerMixName',
		fixed: 'left'
	},
	{
		title: '木仓Id',
		dataIndex: 'mucangId',
		render: data => {
			return renderCopyComp(data);
		},
		width: ColumnWidthEnum.TEXT3
	},
	{
		title: '类型',
		dataIndex: 'type',
		render: data => {
			return TYPE_MAP[data];
		},
		width: ColumnWidthEnum.TEXT3
	},
	{
		title: '层级',
		dataIndex: 'layer',
		xtype: ColumnXtype.CUSTOM,
		width: ColumnWidthEnum.TEXT3
	},
	{
		title: '等级',
		dataIndex: 'levelName',
		xtype: ColumnXtype.CUSTOM,
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '签约状态',
		dataIndex: 'contractStatus',
		render: data => {
			return CONTRACT_STATUS_MAP[data];
		},
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '可排课时间',
		dataIndex: 'scheduleTime',
		xtype: ColumnXtype.CUSTOM,
		width: ColumnWidthEnum.TEXT5
	},
	{
		title: '技能组',
		dataIndex: 'skill',
		xtype: ColumnXtype.CUSTOM,
		width: 150
	},
	{
		title: '培训进度',
		dataIndex: 'trainProgress',
		xtype: ColumnXtype.CUSTOM,
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '财务资料',
		dataIndex: 'finance',
		xtype: ColumnXtype.CUSTOM,
		width: ColumnWidthEnum.TEXT5
	},
	{
		title: '主管老师',
		dataIndex: 'dockingPersonMixName'
	},
	{
		title: '上岗状态',
		dataIndex: 'status',
		xtype: ColumnXtype.CUSTOM,
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '是否邀约拉黑',
		dataIndex: 'blacked',
		xtype: ColumnXtype.CUSTOM,
		width: ColumnWidthEnum.TEXT6
	},
	{
		title: '上岗时间',
		dataIndex: 'alreadyWorkedTime',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	},
	{
		title: '离职时间',
		dataIndex: 'departureTime',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	},
	{
		title: '性别',
		dataIndex: 'gender',
		render: data => {
			return GENDER_MAP[data];
		},
		width: ColumnWidthEnum.TEXT2
	},
	{
		title: '地区',
		dataIndex: 'userCityCodeName',
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '学历',
		dataIndex: 'education',
		render: data => {
			return EDUCATION_MAP[data];
		},
		width: ColumnWidthEnum.TEXT3
	},
	{
		title: '职业分类',
		dataIndex: 'occupationCategory',
		render: data => {
			return OCCUPATION_MAP[data];
		},
		width: ColumnWidthEnum.TEXT6
	},
	{
		title: '职业',
		dataIndex: 'specificOccupationName',
		width: ColumnWidthEnum.TEXT5
	},
	{
		title: '方言',
		dataIndex: 'provincialism',
		width: ColumnWidthEnum.TEXT5
	},
	{
		title: '生日',
		dataIndex: 'birthday',
		width: ColumnWidthEnum.DATEDAY
	},
	{
		title: '年龄',
		dataIndex: 'age',
		width: ColumnWidthEnum.TEXT2
	}
];
