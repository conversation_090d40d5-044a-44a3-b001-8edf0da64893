<template>
	<pm-dialog v-model:visible="visible" :title="title" width="680px">
		<template v-if="visible">
			<m-form
				ref="formRef"
				:model="formState"
				:colon="false"
				:label-col="{ style: { width: '95px' } }"
				autocomplete="off"
			>
				<m-form-item label="信息列表" name="employeeInfo">
					<m-textarea
						v-model:value="formState.employeeInfo"
						:rows="5"
						placeholder="每行一个讲师信息，格式：姓名-对外昵称-手机号"
					/>
				</m-form-item>
				<m-form-item label="讲师类型" name="type">
					<m-select v-model:value="formState.type" :options="options.typeList" placeholder="讲师类型" />
				</m-form-item>
				<m-form-item label="上岗状态" name="status">
					<m-select
						v-model:value="formState.status"
						:options="options.statusList"
						:allowClear="true"
						placeholder="上岗状态"
					/>
				</m-form-item>
				<m-form-item label="等级" name="level" v-if="formState.type === 2">
					<m-select
						v-model:value="formState.level"
						:options="options.levelList"
						:fieldNames="{ label: 'levelName', value: 'levelValue' }"
						placeholder="等级"
					/>
				</m-form-item>
				<m-form-item label="层级" name="layer" v-if="formState.type === 2">
					<m-select v-model:value="formState.layer" :options="options.layerList" placeholder="层级" />
				</m-form-item>
				<m-form-item label="主管老师" name="dockingPerson">
					<m-select
						show-search
						optionFilterProp="employeeMixName"
						v-model:value="formState.dockingPerson"
						:options="options.dockingPersonList"
						:fieldNames="{ label: 'employeeMixName', value: 'employeeNo' }"
						:allowClear="true"
						placeholder="主管老师"
					/>
				</m-form-item>
				<m-form-item label="是否开通企微" name="enableCreateWechat">
					<m-checkbox v-model:checked="formState.enableCreateWechat">开通企微</m-checkbox>
				</m-form-item>
				<m-form-item label="技能组">
					<m-form ref="form2Ref" :model="formState">
						<AdvancedForm
							:formRef="form2Ref"
							:columns="columns"
							v-model:value="formState.skillGroups"
							:showHeader="false"
							:showEditBtn="false"
						>
							<template #carType="{ lineData, index, dataIndex }">
								<m-form-item
									:name="['skillGroups', index, dataIndex]"
									label="车型"
									:labelCol="{ span: 0 }"
									required
									style="margin: 10px 0; height: 32px"
								>
									<m-select
										v-model:value="lineData[dataIndex]"
										:options="options.carTypeList"
										placeholder="车型"
									/>
								</m-form-item>
							</template>
							<template #kemu="{ lineData, index, dataIndex }">
								<m-form-item
									:name="['skillGroups', index, dataIndex]"
									label="科目"
									:labelCol="{ span: 0 }"
									required
									style="margin: 10px 0; height: 32px"
								>
									<m-select
										v-model:value="lineData[dataIndex]"
										:options="options.kemuList"
										placeholder="科目"
									/>
								</m-form-item>
							</template>
							<template #status="{ lineData, index, dataIndex }">
								<m-form-item
									:name="['skillGroups', index, dataIndex]"
									label="审核状态"
									:labelCol="{ span: 0 }"
									required
									style="margin: 10px 0; height: 32px"
								>
									<m-select
										v-model:value="lineData[dataIndex]"
										:options="options.skillStatusList"
										placeholder="审核状态"
									/>
								</m-form-item>
							</template>
						</AdvancedForm>
					</m-form>
				</m-form-item>
			</m-form>
		</template>

		<template #footer>
			<m-button plain @click="visible = false">取消</m-button>
			<m-button :loading="loading" type="primary" @click="onConfirm">确定</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref, watch } from 'vue';
import { MESSAGE_TYPE, MUtils } from 'admin-library';
import { minBy } from 'lodash';

import { cloneByMerge } from '@/shard/utils';
import AdvancedForm from '@/comp/advanced-form/index.vue';
import { CAR_TYPE_OPTIONS, EXAM_KEMU_OPTIONS } from '@/shard/constant';
import { STATUS_OPTIONS } from '@/application/promoter/constant';
import { LAYER_OPTIONS, TYPE_OPTIONS, SKILL_STATUS_OPTIONS } from '../../constant';
import { ListStore as EmployeeListStore } from '@/application/promoter/store';
import { ListStore as LevelListStore } from '@/application/teacher-level-manage/store';
import { BatchCreateStore } from '../../store';

const DefaultFormState = {
	employeeInfo: '',
	type: 2,
	status: 10,
	level: '',
	layer: '',
	dockingPerson: '',
	enableCreateWechat: false,
	skillGroups: [
		{
			carType: 'car',
			kemu: 10,
			status: 2
		}
	]
};

export default defineComponent({
	components: {
		AdvancedForm
	},
	emits: ['refresh'],
	setup(props, { emit }) {
		const components = {
			formRef: ref(null),
			form2Ref: ref(null)
		};

		const constants = {
			columns: [
				{
					title: '车型',
					dataIndex: 'carType',
					xtype: 'custom'
				},
				{
					title: '科目',
					dataIndex: 'kemu',
					xtype: 'custom'
				},
				{
					title: '审核状态',
					dataIndex: 'status',
					xtype: 'custom'
				}
			]
		};

		const state = reactive({
			visible: false,
			title: '批量新增',
			loading: false,
			formState: MUtils.deepClone(DefaultFormState),
			options: {
				typeList: TYPE_OPTIONS,
				levelList: [],
				dockingPersonList: [],
				layerList: LAYER_OPTIONS,
				statusList: STATUS_OPTIONS,
				carTypeList: CAR_TYPE_OPTIONS,
				kemuList: EXAM_KEMU_OPTIONS,
				skillStatusList: SKILL_STATUS_OPTIONS
			},
			lowestLevelValue: ''
		});

		watch(
			() => state.formState.type,
			value => {
				if (value === 1) {
					state.formState.level = '';
				} else if (value === 2 && state.lowestLevelValue) {
					state.formState.level = state.lowestLevelValue;
				}
			}
		);

		const getValidate = () => {
			let promise;

			if (components.form2Ref.value) {
				promise = components.form2Ref.value.validate();
			} else {
				promise = Promise.resolve();
			}
			return promise;
		};
		const methods = {
			open() {
				state.formState = MUtils.deepClone(DefaultFormState);
				state.visible = true;

				methods.initDockingPersonList();
				methods.initLevelList();
			},

			async initDockingPersonList() {
				const res = await EmployeeListStore.request({ limit: 9999, status: 20 }).getData();
				if (res) {
					state.options.dockingPersonList = res;
				}
			},

			async initLevelList() {
				const res = await LevelListStore.request({ limit: 9999 }).getData();
				if (res) {
					state.lowestLevelValue = minBy(res, 'levelValue')['levelValue'];
					state.options.levelList = res;

					if (state.formState.type === 2 && state.lowestLevelValue) {
						state.formState.level = state.lowestLevelValue;
					}
				}
			},

			async onConfirm() {
				await Promise.all([components.formRef.value.validate(), getValidate()]);

				try {
					state.loading = true;
					const params = cloneByMerge(state.formState, DefaultFormState);
					const fetchStore = BatchCreateStore;

					if (params?.skillGroups.length) {
						params.skillGroups = JSON.stringify(params.skillGroups);
					}

					const res = (await fetchStore.request(params).getData()) as any;
					await MUtils.alert({
						title: '新增结果',
						content: `新增成功${res.successCount}人，失败${res.failCount}人`
					});
					if (res.successCount) {
						emit('refresh');
					}
					if (!res.failCount) {
						state.visible = false;
						MUtils.toast('保存成功', MESSAGE_TYPE.success);
					}
				} finally {
					state.loading = false;
				}
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods
		};
	}
});
</script>
