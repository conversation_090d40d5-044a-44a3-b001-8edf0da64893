<template>
	<pm-dialog v-model:visible="visible" :title="title" width="680px">
		<template v-if="visible">
			<m-form
				ref="formRef"
				:model="formState"
				:colon="false"
				:label-col="{ style: { width: '95px' } }"
				autocomplete="off"
			>
				<m-form-item label="性别" name="gender">
					<m-select v-model:value="formState.gender" :options="options.genderList" :allowClear="true" />
				</m-form-item>
				<m-form-item label="生日" name="birthday">
					<m-date-picker v-model:value="formState.birthday" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
				</m-form-item>
				<m-form-item label="所在地区" name="userCityCode">
					<m-cascader
						v-model:value="formState.userCityCode"
						:options="options.cityCodeList"
						:fieldNames="{ label: 'name', value: 'adcode', children: 'districts', level: 'city' }"
						:allowClear="true"
					/>
				</m-form-item>
				<m-form-item label="学历" name="education">
					<m-select v-model:value="formState.education" :options="options.educationList" :allowClear="true" />
				</m-form-item>
				<m-form-item label="职业分类" name="occupationCategory">
					<m-select
						v-model:value="formState.occupationCategory"
						:options="options.occupationList"
						:allowClear="true"
					/>
				</m-form-item>
				<m-form-item label="职业" name="specificOccupationName">
					<m-input v-model:value="formState.specificOccupationName" />
				</m-form-item>
				<m-form-item label="是否有驾照" name="hasDrivingLicense">
					<m-select
						v-model:value="formState.hasDrivingLicense"
						:options="options.radioOptions"
						:allowClear="true"
					/>
				</m-form-item>
				<m-form-item label="方言" name="provincialism">
					<m-input
						v-model:value="formState.provincialism"
						placeholder="请输入方言并以、分隔，如：粤语、四川话。"
					/>
				</m-form-item>
				<m-form-item label="主管老师" name="dockingPerson">
					<m-select
						show-search
						optionFilterProp="employeeMixName"
						v-model:value="formState.dockingPerson"
						:options="options.dockingPersonList"
						:fieldNames="{ label: 'employeeMixName', value: 'employeeNo' }"
						:allowClear="true"
						placeholder="主管老师"
					/>
				</m-form-item>
			</m-form>
		</template>

		<template #footer>
			<m-button plain @click="visible = false">取消</m-button>
			<m-button :loading="loading" type="primary" @click="onConfirm">确定</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { MESSAGE_TYPE, MUtils } from 'admin-library';

import { cloneFromPick, cloneByMerge } from '@/shard/utils';
import { getCityJsonData, CityLevelEnum, findCityByCode } from '@/shard/city';
import { ListStore as EmployeeListStore } from '@/application/promoter/store';
import { RADIO_OPTIONS } from '@/shard/constant';
import { GENDER_OPTIONS, EDUCATION_OPTIONS } from '@/application/customer/constant';
import { OCCUPATION_OPTIONS } from '../../constant';
import { UpdateStore } from '../../store';

const DefaultFormState = {
	id: null,
	gender: null,
	birthday: '',
	userCityCode: [],
	education: null,
	occupationCategory: null,
	specificOccupationName: '',
	hasDrivingLicense: null,
	provincialism: '',
	dockingPerson: ''
};

export default defineComponent({
	emits: ['refresh'],
	setup(props, { emit }) {
		const components = {
			formRef: ref(null),
			form2Ref: ref(null)
		};

		const constants = {};

		const state = reactive({
			visible: false,
			title: '编辑',
			loading: false,
			formState: MUtils.deepClone(DefaultFormState),
			options: {
				genderList: GENDER_OPTIONS,
				educationList: EDUCATION_OPTIONS,
				occupationList: OCCUPATION_OPTIONS,
				radioOptions: RADIO_OPTIONS,
				dockingPersonList: [],
				cityCodeList: []
			}
		});

		const methods = {
			async open(row: any) {
				state.formState = cloneFromPick(row, DefaultFormState);

				state.visible = true;

				methods.initDockingPersonList();
				await methods.initCityCodeList();
				if (row.userCityCode) {
					const t = findCityByCode(row.userCityCode);
					if (t && t.length > 1) {
						state.formState.userCityCode = t.map((item: any) => item.adcode);
					}
				}
			},

			async initDockingPersonList() {
				const res = await EmployeeListStore.request({ limit: 9999, status: 20 }).getData();
				if (res) {
					state.options.dockingPersonList = res;
				}
			},

			async initCityCodeList() {
				const jsonData = await getCityJsonData(CityLevelEnum.city);

				const list = jsonData?.districts[0]?.districts || [];

				state.options.cityCodeList = list;
			},

			async onConfirm() {
				await components.formRef.value.validate();

				try {
					state.loading = true;
					const params = cloneByMerge(state.formState, DefaultFormState);
					const fetchStore = UpdateStore;

					if (params.userCityCode?.length > 1) {
						params.userCityCode = params.userCityCode[params.userCityCode.length - 1];
					} else {
						params.userCityCode = '';
					}

					await fetchStore.request(params).getData();

					emit('refresh');
					state.visible = false;
					MUtils.toast('保存成功', MESSAGE_TYPE.success);
				} finally {
					state.loading = false;
				}
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods
		};
	}
});
</script>
