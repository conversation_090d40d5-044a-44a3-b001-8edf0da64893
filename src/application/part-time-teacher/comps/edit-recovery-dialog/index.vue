<template>
	<pm-dialog v-model:visible="visible" :title="title" width="480px">
		<template v-if="visible">
			<m-form
				ref="formRef"
				:model="formState"
				:colon="false"
				:label-col="{ style: { width: '95px' } }"
				autocomplete="off"
			>
				<m-form-item>
					确定要解除拉黑吗？请输入解除拉黑原因
					<span class="danger">（必填）</span>
				</m-form-item>
				<m-form-item name="reason" required>
					<m-input v-model:value="formState.reason" placeholder="请输入解除拉黑原因" />
				</m-form-item>
			</m-form>
		</template>

		<template #footer>
			<m-button plain @click="visible = false">取消</m-button>
			<m-button :loading="loading" type="primary" @click="onConfirm">确定</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { MESSAGE_TYPE, MUtils } from 'admin-library';

import { cloneFromPick, cloneByMerge } from '@/shard/utils';
import { RecoveryStore } from '@/application/invite-blacklist-lecture/store';

const DefaultFormState = {
	lecturerId: null,
	reason: ''
};

export default defineComponent({
	emits: ['refresh'],
	setup(props, { emit }) {
		const components = {
			formRef: ref(null)
		};

		const constants = {};

		const state = reactive({
			visible: false,
			title: '解除拉黑',
			loading: false,
			formState: MUtils.deepClone(DefaultFormState)
		});

		const methods = {
			open(row: any) {
				state.formState = cloneFromPick(row, DefaultFormState);

				state.visible = true;
			},
			async onConfirm() {
				await components.formRef.value.validate();

				try {
					state.loading = true;
					const params = cloneByMerge(state.formState, DefaultFormState);
					const fetchStore = RecoveryStore;

					await fetchStore.request(params).getData();

					emit('refresh');
					state.visible = false;
					MUtils.toast('解除成功', MESSAGE_TYPE.success);
				} finally {
					state.loading = false;
				}
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods
		};
	}
});
</script>
