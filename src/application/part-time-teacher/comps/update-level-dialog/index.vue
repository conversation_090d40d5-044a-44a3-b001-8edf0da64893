<template>
	<pm-dialog v-model:visible="visible" :title="title" width="680px">
		<template v-if="visible">
			<m-form
				ref="formRef"
				:model="formState"
				:colon="false"
				:label-col="{ style: { width: '115px' } }"
				autocomplete="off"
			>
				<m-form-item label="当前等级" name="oldLevel">
					<m-select
						disabled
						v-model:value="formState.oldLevel"
						:options="options.levelList"
						:fieldNames="{ label: 'levelName', value: 'levelValue' }"
					/>
				</m-form-item>
				<m-form-item label="新等级" name="newLevel" required>
					<m-select
						v-model:value="formState.newLevel"
						:options="options.levelList"
						:fieldNames="{ label: 'levelName', value: 'levelValue' }"
						placeholder="等级"
					/>
				</m-form-item>
				<m-form-item label="新等级生效时机" name="newLevelEffectiveChance" required>
					<m-radio-group
						v-model:value="formState.newLevelEffectiveChance"
						:options="options.effectList"
					></m-radio-group>
				</m-form-item>
			</m-form>
		</template>
		<template #footer>
			<m-button plain @click="visible = false">取消</m-button>
			<m-button :loading="loading" type="primary" @click="onConfirm">确定</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import { MESSAGE_TYPE, MUtils } from 'admin-library';
import { defineComponent, reactive, toRefs, ref } from 'vue';

import { cloneByMerge } from '@/shard/utils';
import { EFFECT_TIME_OPTIONS } from '../../constant';
import { ListStore as LevelListStore } from '@/application/teacher-level-manage/store';
import { ChangeLevelStore } from '../../store';

const DefaultFormState = {
	id: null,
	newLevel: '',
	newLevelEffectiveChance: ''
};

export default defineComponent({
	emits: ['refresh'],
	setup(props, { emit }) {
		const components = {
			formRef: ref(null)
		};

		const constants = {};

		const state = reactive({
			visible: false,
			title: '等级维护',
			loading: false,
			formState: MUtils.deepClone(DefaultFormState),
			options: {
				levelList: [],
				effectList: EFFECT_TIME_OPTIONS
			}
		});

		const methods = {
			open(row) {
				let { id, level } = row;
				state.formState = { id, oldLevel: level };

				state.visible = true;

				methods.initLevelList();
			},

			async initLevelList() {
				const res = await LevelListStore.request({ limit: 9999 }).getData();
				if (res) {
					state.options.levelList = res;
				}
			},
			async onConfirm() {
				await components.formRef.value.validate();

				try {
					state.loading = true;
					const params = cloneByMerge(state.formState, DefaultFormState);
					const fetchStore = ChangeLevelStore;

					delete params.oldLevel;

					await fetchStore.request(params).getData();

					emit('refresh');
					state.visible = false;
					MUtils.toast('保存成功', MESSAGE_TYPE.success);
				} finally {
					state.loading = false;
				}
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods
		};
	}
});
</script>
