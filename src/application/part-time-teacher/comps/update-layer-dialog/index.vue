<template>
	<pm-dialog v-model:visible="visible" :title="title" width="680px">
		<template v-if="visible">
			<m-form
				ref="formRef"
				:model="formState"
				:colon="false"
				:label-col="{ style: { width: '115px' } }"
				autocomplete="off"
			>
				<m-form-item label="层级" name="layer" required>
					<m-select v-model:value="formState.layer" :options="options.layerList" placeholder="层级" />
				</m-form-item>
				<m-form-item label="变更原因" name="reason" required>
					<m-textarea v-model:value="formState.reason" :rows="3" />
				</m-form-item>
			</m-form>
		</template>
		<template #footer>
			<m-button plain @click="visible = false">取消</m-button>
			<m-button :loading="loading" type="primary" @click="onConfirm">确定</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import { MESSAGE_TYPE, MUtils } from 'admin-library';
import { defineComponent, reactive, toRefs, ref } from 'vue';

import { cloneFromPick, cloneByMerge } from '@/shard/utils';
import { LAYER_OPTIONS } from '../../constant';
import { ChangeLayerStore } from '../../store';

const DefaultFormState = {
	lecturerId: null,
	layer: '',
	reason: ''
};

export default defineComponent({
	emits: ['refresh'],
	setup(props, { emit }) {
		const components = {
			formRef: ref(null)
		};

		const constants = {};

		const state = reactive({
			visible: false,
			title: '层级维护',
			loading: false,
			formState: MUtils.deepClone(DefaultFormState),
			options: {
				layerList: LAYER_OPTIONS
			}
		});

		const methods = {
			open(row) {
				state.formState = cloneFromPick(row, DefaultFormState);

				state.visible = true;
			},

			async onConfirm() {
				await components.formRef.value.validate();

				try {
					state.loading = true;
					const params = cloneByMerge(state.formState, DefaultFormState);
					const fetchStore = ChangeLayerStore;

					await fetchStore.request(params).getData();

					emit('refresh');
					state.visible = false;
					MUtils.toast('保存成功', MESSAGE_TYPE.success);
				} finally {
					state.loading = false;
				}
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods
		};
	}
});
</script>
