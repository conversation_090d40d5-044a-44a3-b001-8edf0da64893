<template>
	<pm-dialog v-model:visible="visible" :title="title" width="680px">
		<template v-if="visible">
			<m-form
				ref="formRef"
				:model="formState"
				:colon="false"
				:label-col="{ style: { width: '95px' } }"
				autocomplete="off"
			>
				<m-form-item label="签约状态" name="contractStatus" required>
					<m-select
						v-model:value="formState.contractStatus"
						:options="options.ContractStatusList"
						:allowClear="true"
						placeholder="签约状态"
					/>
				</m-form-item>
			</m-form>
		</template>
		<template #footer>
			<m-button plain @click="visible = false">取消</m-button>
			<m-button :loading="loading" type="primary" @click="onConfirm">确定</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import { MESSAGE_TYPE, MUtils } from 'admin-library';
import { defineComponent, reactive, toRefs, ref } from 'vue';

import { cloneFromPick, cloneByMerge } from '@/shard/utils';
import { CONTRACT_STATUS_OPTIONS } from '../../constant';
import { SetContractStatusStore } from '../../store';

const DefaultFormState = {
	id: null,
	contractStatus: ''
};

export default defineComponent({
	emits: ['refresh'],
	setup(props, { emit }) {
		const components = {
			formRef: ref(null)
		};

		const constants = {};

		const state = reactive({
			visible: false,
			title: '更新签约状态',
			loading: false,
			formState: MUtils.deepClone(DefaultFormState),
			options: {
				ContractStatusList: CONTRACT_STATUS_OPTIONS
			}
		});

		const methods = {
			open(row) {
				state.formState = cloneFromPick(row, DefaultFormState);

				state.visible = true;
			},
			async onConfirm() {
				await components.formRef.value.validate();

				try {
					state.loading = true;
					const params = cloneByMerge(state.formState, DefaultFormState);
					const fetchStore = SetContractStatusStore;

					await fetchStore.request(params).getData();

					emit('refresh');
					state.visible = false;
					MUtils.toast('保存成功', MESSAGE_TYPE.success);
				} finally {
					state.loading = false;
				}
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods
		};
	}
});
</script>
