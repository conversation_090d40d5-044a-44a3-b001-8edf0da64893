<template>
	<pm-dialog v-model:visible="visible" :title="title" width="480px">
		<template v-if="visible">
			<m-form
				ref="formRef"
				:model="formState"
				:colon="false"
				:label-col="{ style: { width: '95px' } }"
				autocomplete="off"
			>
				<m-form-item label="层级" name="layer" required>
					<m-select v-model:value="formState.layer" :options="options.layerList" placeholder="层级" />
				</m-form-item>
				<m-form-item label="上岗状态" name="stationStatus" required>
					<m-select
						v-model:value="formState.stationStatus"
						:options="options.statusList"
						placeholder="上岗状态"
					/>
				</m-form-item>
				<m-form-item label="添加技能组" required>
					<m-row justify="space-between" :gutter="16">
						<m-col :span="8">
							<m-form-item label="" :name="['skillGroups', 0, 'carType']" required>
								<m-select
									v-model:value="formState.skillGroups[0].carType"
									:options="options.carList"
									placeholder="车型"
								/>
							</m-form-item>
						</m-col>
						<m-col :span="8">
							<m-form-item label="" :name="['skillGroups', 0, 'kemu']" required>
								<m-select
									v-model:value="formState.skillGroups[0].kemu"
									:options="options.kemuList"
									placeholder="科目"
								/>
							</m-form-item>
						</m-col>
						<m-col :span="8">
							<m-form-item label="" :name="['skillGroups', 0, 'status']" required>
								<m-select
									v-model:value="formState.skillGroups[0].status"
									:options="options.skillStatusList"
									placeholder="审核状态"
								/>
							</m-form-item>
						</m-col>
					</m-row>
				</m-form-item>
			</m-form>
		</template>

		<template #footer>
			<m-button plain @click="visible = false">取消</m-button>
			<m-button :loading="loading" type="primary" @click="onConfirm">确定</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { MESSAGE_TYPE, MUtils } from 'admin-library';

import { cloneByMerge } from '@/shard/utils';
import { CAR_TYPE_OPTIONS, EXAM_KEMU_OPTIONS } from '@/shard/constant';
import { STATUS_OPTIONS } from '@/application/promoter/constant';
import { LAYER_OPTIONS, SKILL_STATUS_OPTIONS } from '../../constant';
import { BatchCreateSkillStore } from '../../store';

const DefaultFormState = {
	layer: null,
	stationStatus: null,
	skillGroups: [
		{
			carType: null,
			kemu: null,
			status: null
		}
	]
};

export default defineComponent({
	emits: ['refresh'],
	setup(props, { emit }) {
		const components = {
			formRef: ref(null)
		};

		const constants = {};

		const state = reactive({
			visible: false,
			title: '批量增加技能组',
			loading: false,
			formState: MUtils.deepClone(DefaultFormState),
			options: {
				layerList: LAYER_OPTIONS,
				statusList: STATUS_OPTIONS,
				carList: CAR_TYPE_OPTIONS,
				kemuList: EXAM_KEMU_OPTIONS,
				skillStatusList: SKILL_STATUS_OPTIONS
			}
		});

		const methods = {
			open() {
				state.formState = MUtils.deepClone(DefaultFormState);
				state.visible = true;
			},
			async onConfirm() {
				await components.formRef.value.validate();

				try {
					state.loading = true;
					const params = cloneByMerge(state.formState, DefaultFormState);
					const fetchStore = BatchCreateSkillStore;

					params.skillGroups = JSON.stringify(params.skillGroups);
					await fetchStore.request(params).getData();

					emit('refresh');
					state.visible = false;
					MUtils.toast('保存成功', MESSAGE_TYPE.success);
				} finally {
					state.loading = false;
				}
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods
		};
	}
});
</script>
