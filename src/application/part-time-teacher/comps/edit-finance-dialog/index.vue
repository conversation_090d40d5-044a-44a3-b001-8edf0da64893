<template>
	<pm-dialog v-model:visible="visible" :title="title" width="680px">
		<template v-if="visible">
			<m-form
				ref="formRef"
				:model="formState"
				:colon="false"
				:label-col="{ style: { width: '95px' } }"
				autocomplete="off"
			>
				<m-form-item label="身份证号" name="idCardNo" required :rules="idCard">
					<m-input v-model:value="formState.idCardNo" placeholder="身份证号" />
				</m-form-item>
				<m-form-item label="银行卡号" name="bankNo" required :rules="bankNo">
					<m-input v-model:value="formState.bankNo" placeholder="银行卡号" />
				</m-form-item>
			</m-form>
		</template>

		<template #footer>
			<m-button plain @click="visible = false">取消</m-button>
			<m-button :loading="loading" type="primary" @click="onConfirm">确定</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { MESSAGE_TYPE, MUtils } from 'admin-library';

import { cloneByMerge } from '@/shard/utils';
import { idCard, bankNo } from '@/shard/validate';
import { SetFinanceInfoStore } from '../../store';

const DefaultFormState = {
	id: null,
	bankNo: '',
	idCardNo: ''
};

export default defineComponent({
	emits: ['refresh'],
	setup(props, { emit }) {
		const components = {
			formRef: ref(null)
		};

		const constants = {
			idCard,
			bankNo
		};

		const state = reactive({
			visible: false,
			title: '财务资料',
			loading: false,
			formState: MUtils.deepClone(DefaultFormState)
		});

		const methods = {
			open(row: any) {
				let { id } = row;
				state.formState = MUtils.deepClone({ ...DefaultFormState, id });

				state.visible = true;
			},
			async onConfirm() {
				await components.formRef.value.validate();

				try {
					state.loading = true;
					const params = cloneByMerge(state.formState, DefaultFormState);
					const fetchStore = SetFinanceInfoStore;

					await fetchStore.request(params).getData();

					emit('refresh');
					state.visible = false;
					MUtils.toast('保存成功', MESSAGE_TYPE.success);
				} finally {
					state.loading = false;
				}
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods
		};
	}
});
</script>
