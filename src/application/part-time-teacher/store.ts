import {
	List,
	Create,
	BatchCreate,
	Update,
	BatchCreateSkill,
	SetFinanceInfo,
	GetFinanceInfo,
	SetContractStatus,
	SearchAndSetContractStatus,
	ChangeLevel,
	WakeUpLecturer,
	ChangeLayer,
	CreateGroup,
	GetPhone
} from '@/store/part-time-teacher';
import { ItemResponse } from './types';

export const ListStore = new List<Array<ItemResponse>>({});
export const CreateStore = new Create({});
export const BatchCreateStore = new BatchCreate({});
export const UpdateStore = new Update({});
export const BatchCreateSkillStore = new BatchCreateSkill({});

export const SetFinanceInfoStore = new SetFinanceInfo({});
export const GetFinanceInfoStore = new GetFinanceInfo({});
export const SetContractStatusStore = new SetContractStatus({});
export const SearchAndSetContractStatusStore = new SearchAndSetContractStatus({});
export const ChangeLevelStore = new ChangeLevel({});

export const WakeUpLecturerStore = new WakeUpLecturer({});

export const ChangeLayerStore = new ChangeLayer({});

export const CreateGroupStore = new CreateGroup({});

export const GetPhoneStore = new GetPhone({});
