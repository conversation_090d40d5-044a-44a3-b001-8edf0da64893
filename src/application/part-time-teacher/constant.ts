import { getMapfromArray, getStorefromArray } from '@/shard/utils';
import { ColorEnum } from '@/shard/constant';

export const STATUS_OPTIONS = [
	{
		label: '在职',
		value: 1
	},
	{
		label: '离职',
		value: 2
	}
];
export const STATUS_MAP = getMapfromArray(STATUS_OPTIONS);

export const CONTRACT_STATUS_OPTIONS = [
	{
		label: '已签约',
		value: 1
	},
	{
		label: '未签约',
		value: 2
	},
	{
		label: '已解约',
		value: 3
	},
	{
		label: '未知',
		value: 4
	}
];
export const CONTRACT_STATUS_MAP = getMapfromArray(CONTRACT_STATUS_OPTIONS);

export const LAYER_OPTIONS = [
	{
		label: 'S',
		value: 'S'
	},
	{
		label: 'A',
		value: 'A'
	},
	{
		label: 'B',
		value: 'B'
	},
	{
		label: 'C',
		value: 'C'
	},
	{
		label: 'D',
		value: 'D'
	}
];
export const LAYER_MAP = getMapfromArray(LAYER_OPTIONS);
export const LAYER_STORE = getStorefromArray(LAYER_OPTIONS);

export const TYPE_OPTIONS = [
	{
		label: '官方',
		value: 1
	},
	{
		label: '兼职',
		value: 2
	}
];
export const TYPE_MAP = getMapfromArray(TYPE_OPTIONS);
export const TYPE_STORE = getStorefromArray(TYPE_OPTIONS);

export const EFFECT_TIME_OPTIONS = [
	{
		label: '立即生效',
		value: 1
	},
	{
		label: '次日0点生效',
		value: 2
	}
];
export const EFFECT_TIME_MAP = getMapfromArray(EFFECT_TIME_OPTIONS);

export const OCCUPATION_OPTIONS = [
	{
		label: '学生',
		value: 1
	},
	{
		label: '在家宝妈',
		value: 2
	},
	{
		label: '有过讲师经历',
		value: 3
	},
	{
		label: '自由职业',
		value: 4
	},
	{
		label: '在职人员',
		value: 5
	},
	{
		label: '其他',
		value: 999
	}
];
export const OCCUPATION_MAP = getMapfromArray(OCCUPATION_OPTIONS);
export const OCCUPATION_STORE = getStorefromArray(OCCUPATION_OPTIONS);

export const SKILL_STATUS_OPTIONS = [
	{
		label: '审核中',
		value: 1,
		styleclass: ColorEnum.info
	},
	{
		label: '已通过',
		value: 2,
		styleclass: ColorEnum.success
	}
];
export const SKILL_STATUS_MAP = getMapfromArray(SKILL_STATUS_OPTIONS);
export const SKILL_STATUS_STORE = getStorefromArray(SKILL_STATUS_OPTIONS);
