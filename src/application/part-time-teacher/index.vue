<template>
	<pm-effi :controller="controller">
		<pm-search>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '讲师Id'
				}"
				v-model="lecturerId"
				data-index="id"
				xtype="INPUT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '员工姓名'
				}"
				data-index="name"
				xtype="INPUT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '对外昵称'
				}"
				data-index="nickName"
				xtype="INPUT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '性别'
				}"
				data-index="gender"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '学历'
				}"
				data-index="education"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '职业分类'
				}"
				data-index="occupationCategory"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '职业'
				}"
				data-index="specificOccupationName"
				xtype="INPUT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '上岗状态'
				}"
				data-index="status"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '类型'
				}"
				data-index="type"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '层级'
				}"
				data-index="layer"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '等级',
					fieldNames: { label: 'levelName', value: 'levelValue' }
				}"
				data-index="levelValue"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="3"
				show-search
				optionFilterProp="employeeMixName"
				:antdProps="{
					placeholder: '主管老师',
					fieldNames: { label: 'employeeMixName', value: 'employeeNo' }
				}"
				data-index="dockingPerson"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="5"
				:antd-props="{
					placeholder: ['上岗时间', '结束时间']
				}"
				xtype="RANGEPICKER"
				data-index="alreadyWorkedTimeFrom|alreadyWorkedTimeTo"
			/>
			<pm-search-single
				:span="5"
				:antd-props="{
					placeholder: ['离职时间', '结束时间']
				}"
				xtype="RANGEPICKER"
				data-index="departureTimeFrom|departureTimeTo"
			/>
		</pm-search>
		<pm-table
			:columns="COLUMNS"
			:use-custom-column="true"
			:operations-fixed="true"
			:operations-width="160"
			:sort-num="false"
			:scroll="{
				x: 'max-content'
			}"
			:antd-props="{
				customRow: record => {
					return {
						onClick: () => {
							onClick(record);
						}
					};
				}
			}"
			:rowClassName="setInstallClassName"
		>
			<template #beforeCustom>
				<m-button type="primary" @click="onCreate">添加</m-button>
				<m-button type="primary" @click="onTeacherLevleManage">讲师等级管理</m-button>
				<m-button type="primary" @click="onAddSkillRecovery">批量添加技能组</m-button>
			</template>
			<template #scheduleTime="{ record }">
				<m-button type="link" @click="onViewScheduleTime(record)">查看</m-button>
				<m-button type="link" @click="toClearRecord(record)">清空日志</m-button>
			</template>
			<template #layer="{ record }">
				<m-button type="link" @click="onUpdateLayer(record)">
					{{ LAYER_MAP[record.layer] || '层级维护' }}
				</m-button>
				<m-button type="link" @click="toLayerLog(record)">层级日志</m-button>
			</template>
			<template #levelName="{ record }">
				<m-button type="link" @click="onUpdateLevel(record)">
					{{ record.levelName || '等级维护' }}
				</m-button>
				<m-button type="link" @click="toLevelLog(record)">等级日志</m-button>
			</template>

			<template #finance="{ record }">
				<m-button type="link" @click="onEditFinance(record)">管理</m-button>
				<m-button type="link" @click="onViewFinance(record)">查看</m-button>
			</template>
			<template #skill="{ record }">
				<div v-if="record.skillGroups?.filter(item => item.status === 1).length">
					审核中：{{ renderSkillList(record.skillGroups.filter(item => item.status === 1)) }}
				</div>
				<div v-if="record.skillGroups?.filter(item => item.status === 2).length">
					已通过：{{ renderSkillList(record.skillGroups.filter(item => item.status === 2)) }}
				</div>
				<m-button type="link" @click="toSkillList(record)">管理</m-button>
			</template>
			<template #trainProgress="{ record }">
				<m-button type="link" @click="toTrainProgress(record)">视频课程</m-button>
				<m-button type="link" @click="showDataBank(record)">资料库</m-button>
			</template>
			<template #status="{ record }">
				<span
					class="cursor-pointer"
					@click="onEditStatus(record)"
					v-html="
						renderTextWithColor(record.status, STATUS_OPTIONS, {
							isTag: true,
							inverse: false,
							isReturnStr: true
						})
					"
				></span>
			</template>
			<template #blacked="{ record }">
				<m-button type="link" v-if="record.blacked" @click="onRecovery(record)">已拉黑，点击解除</m-button>
			</template>

			<template #operations="{ record }">
				<m-button type="link" @click="toStudent(record)">Ta的学员</m-button>
				<m-button type="link" @click="toInspectList(record)">历史评价</m-button>
				<m-button type="link" @click="toInviteRecord(record)">邀约记录</m-button>
				<m-dropdown trigger="click">
					<span class="primary cursor-pointer" @click.prevent>
						操作
						<DownOutlined />
					</span>
					<template #overlay>
						<m-menu>
							<m-menu-item @click="onSearchUpdateSignStatus(record)">查询更新签约状态</m-menu-item>
							<m-menu-item @click="onUpdateSignStatus(record)">手动更新签约状态</m-menu-item>
							<m-menu-item @click="onEdit(record)">编辑</m-menu-item>
							<m-menu-item @click="viewPhone(record)">查看手机</m-menu-item>
							<m-menu-item @click="onSendWakeupMessage(record)">提醒回App</m-menu-item>
							<m-menu-item @click="onCreateGroup(record)">拉群</m-menu-item>
						</m-menu>
					</template>
				</m-dropdown>
			</template>
		</pm-table>
	</pm-effi>

	<edit-comp ref="editRef" @refresh="onRefresh"></edit-comp>
	<batch-create-comp ref="batchCreateRef" @refresh="onRefresh"></batch-create-comp>
	<edit-finance-comp ref="editFinanceRef" @refresh="onRefresh"></edit-finance-comp>
	<update-sign-comp ref="UpdateSignRef" @refresh="onRefresh"></update-sign-comp>
	<update-level-comp ref="UpdateLevelRef" @refresh="onRefresh"></update-level-comp>
	<update-layer-comp ref="UpdateLayerRef" @refresh="onRefresh"></update-layer-comp>
	<DataBankComp ref="DataBankCompRef" @refresh="onRefresh"></DataBankComp>
	<edit-status-comp ref="editStatusRef" @refresh="onRefresh"></edit-status-comp>
	<edit-recovery-comp ref="editRecoveryRef" @refresh="onRefresh"></edit-recovery-comp>
	<send-wakeup-message-comp ref="sendWakeupMessageRef" @refresh="onRefresh"></send-wakeup-message-comp>
	<add-skill-comp ref="addSkillRef" @refresh="onRefresh"></add-skill-comp>
</template>
<script lang="ts">
import { defineComponent, ref, reactive, toRefs } from 'vue';
import { MESSAGE_TYPE, ModelController, PaasPostMessage } from 'admin-library';
import { useRoute } from 'vue-router';
import { DownOutlined } from '@ant-design/icons-vue';
import { MUtils } from '@paas/paas-library';

import EditStatusComp from '@/application/promoter/comps/edit-status-dialog/index.vue';
import SendWakeupMessageComp from '@/application/student/comps/send-wakeup-message-dialog/index.vue';
import BatchCreateComp from './comps/batch-create-dialog/index.vue';
import EditComp from './comps/edit-dialog/index.vue';
import EditFinanceComp from './comps/edit-finance-dialog/index.vue';
import UpdateSignComp from './comps/update-sign-dialog/index.vue';
import UpdateLevelComp from './comps/update-level-dialog/index.vue';
import UpdateLayerComp from './comps/update-layer-dialog/index.vue';
import EditRecoveryComp from './comps/edit-recovery-dialog/index.vue';
import AddSkillComp from './comps/add-skill-dialog/index.vue';
import { renderTextWithColor, copyText } from '@/shard/utils';
import { COLUMNS } from './config';
import { CAR_TYPE_MAP, EXAM_KEMU_MAP } from '@/shard/constant';
import { TYPE_STORE, LAYER_STORE, LAYER_MAP, OCCUPATION_STORE } from './constant';
import { STATUS_STORE, STATUS_OPTIONS } from '@/application/promoter/constant';
import { EDUCATION_STORE, GENDER_STORE } from '@/application/customer/constant';
import { ListStore as EmployeeListStore } from '@/application/promoter/store';
import { ListStore as levelListStore } from '@/application/teacher-level-manage/store';
import {
	GetFinanceInfoStore,
	ListStore,
	SearchAndSetContractStatusStore,
	CreateGroupStore,
	GetPhoneStore
} from './store';
import { ItemResponse } from './types';

export default defineComponent({
	components: {
		DownOutlined,
		BatchCreateComp,
		EditComp,
		EditFinanceComp,
		UpdateSignComp,
		UpdateLevelComp,
		UpdateLayerComp,
		EditStatusComp,
		EditRecoveryComp,
		SendWakeupMessageComp,
		AddSkillComp
	},
	setup() {
		const route = useRoute();
		const query = route.query;

		const components = {
			batchCreateRef: ref<InstanceType<typeof BatchCreateComp>>(),
			editRef: ref<InstanceType<typeof EditComp>>(),
			editFinanceRef: ref<InstanceType<typeof EditFinanceComp>>(),
			UpdateSignRef: ref<InstanceType<typeof UpdateSignComp>>(),
			UpdateLevelRef: ref<InstanceType<typeof UpdateLevelComp>>(),
			UpdateLayerRef: ref<InstanceType<typeof UpdateLayerComp>>(),
			editStatusRef: ref<InstanceType<typeof EditStatusComp>>(),
			editRecoveryRef: ref<InstanceType<typeof EditRecoveryComp>>(),
			sendWakeupMessageRef: ref<InstanceType<typeof SendWakeupMessageComp>>(),
			addSkillRef: ref<InstanceType<typeof AddSkillComp>>()
		};

		const constants = {
			COLUMNS,
			STATUS_OPTIONS,
			LAYER_MAP
		};

		let lecturerId;
		if (query.lecturerId) {
			lecturerId = +query.lecturerId;
		}

		const state = reactive({
			selectedId: 0,
			lecturerId
		});

		const controller = new ModelController({
			table: {
				store: ListStore
			},
			search: {
				gender: {
					store: GENDER_STORE
				},
				education: {
					store: EDUCATION_STORE
				},
				occupationCategory: {
					store: OCCUPATION_STORE
				},
				dockingPerson: {
					store: EmployeeListStore
				},
				status: {
					store: STATUS_STORE
				},
				type: {
					store: TYPE_STORE
				},
				layer: {
					store: LAYER_STORE
				},
				levelValue: {
					store: levelListStore
				}
			}
		});
		controller.tableRequest();

		controller.search.dockingPerson.onRequest.use(params => {
			params = {
				...params,
				limit: 9999
			};

			return params;
		});

		controller.search.levelValue.onRequest.use(params => {
			params = {
				...params,
				limit: 9999
			};

			return params;
		});

		const methods = {
			renderTextWithColor,
			onClick(recored) {
				state.selectedId = recored.id;
			},
			setInstallClassName(record) {
				if (record.id === state.selectedId) {
					return 'selected-row';
				}
			},
			renderSkillList(list) {
				return list
					.map(item => {
						let text = '';
						if (item.carType) {
							text += CAR_TYPE_MAP[item.carType];
						}
						if (item.kemu) {
							text += EXAM_KEMU_MAP[item.kemu];
						}
						return text;
					})
					.join(',');
			},
			/**
			 * 查询并更新签约状态
			 * @param row 信息
			 */
			onSearchUpdateSignStatus(row) {
				SearchAndSetContractStatusStore.request({
					id: row.id
				})
					.getData()
					.then(() => {
						MUtils.toast('保存成功', MESSAGE_TYPE.success);
					});
			},
			/**
			 * 手动更新签约状态
			 * @param row 信息
			 */
			onUpdateSignStatus(row) {
				components.UpdateSignRef.value.open(row);
			},
			onUpdateLayer(row) {
				components.UpdateLayerRef.value.open({ ...row, lecturerId: row.id });
			},
			onUpdateLevel(row) {
				components.UpdateLevelRef.value.open(row);
			},
			async viewPhone(row: ItemResponse) {
				const viewData = (await GetPhoneStore.request({ id: row.id }).getData()) as any;

				const isConfirm = await MUtils.confirm({
					title: '讲师手机号',
					content: viewData.value,
					type: MESSAGE_TYPE.success,
					confirmText: '复制',
					cancelText: '关闭'
				});
				if (isConfirm) {
					methods.copy(viewData.value);
				}
			},
			onCreate() {
				components.batchCreateRef.value.open();
			},
			onEdit(row: ItemResponse) {
				components.editRef.value.open(row);
			},
			onEditStatus(row: ItemResponse) {
				components.editStatusRef.value.open(
					{
						...row,
						lecturerId: row.id,
						remark: row.latestStatusChangeRemark
					},
					'lecturer'
				);
			},
			toStudent(row) {
				PaasPostMessage.post('navigation.to', '/#/membership-student', {
					title: 'Ta的学员',
					query: {
						lecturerId: row?.id
					},
					target: '_tab'
				});
			},
			toInspectList(row) {
				PaasPostMessage.post('navigation.to', '/#/inspect-list', {
					title: '历史评价',
					query: {
						lecturerId: row?.id
					},
					extendData: {
						style: 'width: 1020px; height: 90%; margin: 2% auto 0;'
					},
					target: '_popup'
				});
			},
			toInviteRecord(row) {
				PaasPostMessage.post('navigation.to', '/#/lecturer-invite-record', {
					title: '邀约记录',
					query: {
						lecturerId: row?.id
					},
					extendData: {
						style: 'width: 1020px; height: 90%; margin: 2% auto 0;'
					},
					target: '_popup'
				});
			},
			/**
			 * 查看可排课时间
			 */
			onViewScheduleTime(row) {
				PaasPostMessage.post('navigation.to', '/#/lecturer-schedule', {
					query: {
						lecturerId: row?.id
					},
					target: '_tab'
				});
			},
			toSkillList(row) {
				PaasPostMessage.post('navigation.to', '/#/skill-list', {
					title: '技能组',
					query: {
						lecturerId: row?.id
					},
					extendData: {
						style: 'width: 780px; height: 90%; margin: 2% auto 0;'
					},
					target: '_popup'
				});
			},
			toTrainProgress(row) {
				PaasPostMessage.post('navigation.to', '/#/train-progress', {
					title: '培训进度-视频课程',
					query: {
						lecturerId: row?.id
					},
					extendData: {
						style: 'width: 980px; height: 90%; margin: 2% auto 0;'
					},
					target: '_popup'
				});
			},
			toClearRecord(row) {
				PaasPostMessage.post('navigation.to', '/#/schedule-clear-record', {
					title: '清空日志',
					query: {
						lecturerId: row?.id
					},
					extendData: {
						style: 'width: 980px; height: 90%; margin: 2% auto 0;'
					},
					target: '_popup'
				});
			},
			/**
			 * 编辑财务资料
			 * @param row 信息
			 */
			async onEditFinance(row) {
				components.editFinanceRef.value.open(row);
			},
			/**
			 * 查看财务资料
			 * @param row 信息
			 */
			async onViewFinance(row) {
				const financeInfo: any = await GetFinanceInfoStore.request({
					id: row.id
				}).getData();

				MUtils.alert({
					title: '财务资料',
					content: `身份证：${financeInfo.idCardNoMask || '--'} <br/> 银行卡：${
						financeInfo.bankNoMask || '--'
					}`
				});
			},
			async onRecovery(row: ItemResponse) {
				components.editRecoveryRef.value.open({ lecturerId: row.id });
			},
			async onAddSkillRecovery() {
				components.addSkillRef.value.open();
			},
			/**
			 * 老师等级管理
			 */
			onTeacherLevleManage() {
				PaasPostMessage.post('navigation.to', '/#/teacher-level-manage', {
					title: '讲师等级管理',
					extendData: {
						style: 'width: 60%; height: 600px; margin: 140px auto 0;'
					},
					target: '_popup'
				});
			},
			toLayerLog(row) {
				PaasPostMessage.post('navigation.to', '/#/layer-change-log', {
					title: '层级日志',
					query: {
						lecturerId: row?.id
					},
					extendData: {
						style: 'width: 780px; height: 90%; margin: 2% auto 0;'
					},
					target: '_popup'
				});
			},
			toLevelLog(row) {
				PaasPostMessage.post('navigation.to', '/#/level-change-log', {
					title: '等级日志',
					query: {
						lecturerId: row?.id
					},
					extendData: {
						style: 'width: 780px; height: 90%; margin: 2% auto 0;'
					},
					target: '_popup'
				});
			},
			onSendWakeupMessage(row: ItemResponse) {
				components.sendWakeupMessageRef.value.open({ id: row.id }, 2);
			},
			async onCreateGroup(row: ItemResponse) {
				await CreateGroupStore.request({ lecturerId: row.id }).getData();
				MUtils.toast('拉群成功', MESSAGE_TYPE.success);
			},

			copy(text) {
				if (!text) {
					return;
				}
				const ret = copyText(text);
				if (ret) {
					MUtils.toast('复制成功');
				}
			},
			onRefresh() {
				controller.tableRequest();
			},
			//资料库弹窗
			showDataBank(row) {
				PaasPostMessage.post('navigation.to', '/#/data-bank-progress', {
					title: '培训进度-资料库',
					query: {
						lecturerId: row?.id
					},
					extendData: {
						style: 'width: 980px; height: 90%; margin: 2% auto 0;'
					},
					target: '_popup'
				});
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			controller
		};
	}
});
</script>
<style lang="less" scoped></style>
