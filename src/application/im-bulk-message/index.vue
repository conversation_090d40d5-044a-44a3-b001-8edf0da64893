<template>
	<pm-effi :controller="controller">
		<pm-table
			:columns="COLUMNS"
			:use-custom-column="true"
			:operations-fixed="false"
			:operations-width="120"
			:sort-num="false"
			:scroll="{
				x: 'max-content'
			}"
		>
			<template #beforeCustom>
				<m-button type="primary" @click="onCreate()">添加</m-button>
			</template>
			<template #receivers="{ record }">
				<m-button type="link" v-if="record.receivers.length > 5" @click="onViewDetail(record.receivers)">
					{{ record.receivers.length }}个联系人
				</m-button>
				<div v-else>
					{{ record.receivers.map(item => item.receiverNickName).join('，') }}
				</div>
			</template>
			<template #msgContent="{ record }">
				<div v-if="record.msgType === 11">
					{{ parseContent(record.msgContent).text }}
				</div>
				<div v-else-if="record.msgType === 13">
					<m-image :src="parseContent(record.msgContent).fileUrl" style="width: 50px" />
				</div>
				<div
					v-else-if="record.msgType === 15 || record.msgType === 16"
					class="elli primary cursor-pointer w200"
				>
					<a :href="parseContent(record.msgContent).fileUrl" target="_blank">
						{{ parseContent(record.msgContent).fileUrl }}
					</a>
				</div>
				<div v-else-if="record.msgType === 10011">
					封面：
					<m-image :src="parseContent(record.msgContent).fileUrl" style="width: 50px" />
					<br />
					<div class="elli cursor-pointer w200">
						<span>链接：</span>
						<a :href="parseContent(record.msgContent).url" target="_blank">
							{{ parseContent(record.msgContent).url }}
						</a>
					</div>
					标题：
					{{ parseContent(record.msgContent).title }}
					<br />
					描述：
					{{ parseContent(record.msgContent).desc }}
				</div>
				<div v-else-if="record.msgType === 10002" class="w200">
					<m-row>
						<img :src="parseContent(record.msgContent).icon" width="24" />
						<span>{{ parseContent(record.msgContent).subTitle }}</span>
					</m-row>
					<div style="font-size: 15px">{{ parseContent(record.msgContent).title }}</div>
					<div>
						<img :src="parseContent(record.msgContent).cover" width="120" />
					</div>
					<div class="elli cursor-pointer w200">
						<span>链接：</span>
						<a :href="parseContent(record.msgContent).url" target="_blank">
							{{ parseContent(record.msgContent).url }}
						</a>
					</div>
				</div>
				<div v-else></div>
			</template>
			<template #operations="{ record }">
				<m-button type="link" @click="onEdit(record)" v-if="record.status === 1">编辑</m-button>
				<m-button type="link" @click="onCopy(record)">复制</m-button>
				<m-button type="link" v-if="record.status === 1" @click="onCancel(record)">取消任务</m-button>
				<m-button type="link" v-if="record.status === 2" @click="onWithdraw(record)">撤回</m-button>
			</template>
		</pm-table>
	</pm-effi>

	<edit-comp ref="editRef" @refresh="onRefresh"></edit-comp>
</template>
<script lang="ts">
import { defineComponent, ref, reactive, toRefs } from 'vue';
import { MESSAGE_TYPE, MUtils, ModelController } from 'admin-library';

import EditComp from './comps/edit-dialog/index.vue';
import { confirmMessageBox, openLoading, openTextInfo } from '@/shard/utils';
import { COLUMNS } from './config';
import { ListStore, CancelStore, WithdrawStore } from './store';
import { ItemResponse } from './types';

export default defineComponent({
	components: {
		EditComp
	},
	setup() {
		const components = {
			editRef: ref<InstanceType<typeof EditComp>>()
		};

		const constants = {
			COLUMNS
		};

		const state = reactive({});

		const controller = new ModelController({
			table: {
				store: ListStore
			}
		});
		controller.tableRequest();

		const methods = {
			onCreate() {
				components.editRef.value.open();
			},
			async onCancel(row: ItemResponse) {
				await confirmMessageBox('确认取消任务吗？');

				await CancelStore.request({ id: row.id }).getData();
				methods.onRefresh();
				MUtils.toast('取消任务成功', MESSAGE_TYPE.success);
			},
			async onWithdraw(row: ItemResponse) {
				await confirmMessageBox('确认撤回消息吗？');

				const loading = openLoading();
				await WithdrawStore.request({ id: row.id }).getData();
				loading.close();
				methods.onRefresh();
				MUtils.toast('撤回消息成功', MESSAGE_TYPE.success);
			},
			onEdit(row: ItemResponse) {
				components.editRef.value.open(row);
			},
			async onCopy(row: ItemResponse) {
				components.editRef.value.open({ ...row, id: null });
			},
			parseContent(data) {
				return JSON.parse(data);
			},
			onViewDetail(receivers) {
				openTextInfo({
					title: '群发对象',
					content: receivers.map(item => item.receiverNickName).join('，')
				});
			},
			onRefresh() {
				controller.tableRequest();
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			controller
		};
	}
});
</script>
