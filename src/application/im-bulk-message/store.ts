import { List, Update, Create, Cancel, Withdraw, AccountList } from '@/store/im-bulk-message';
import { ItemResponse } from './types';

export const ListStore = new List<Array<ItemResponse>>({});
export const UpdateStore = new Update({});
export const CreateStore = new Create({
	ajaxOptions: {
		contentType: false,
		headers: { 'content-type': 'application/json;charset=UTF-8' }
	}
});
export const CancelStore = new Cancel({});
export const WithdrawStore = new Withdraw({});

export const AccountListStore = new AccountList<Array<ItemResponse>>({});
