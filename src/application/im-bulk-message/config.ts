import { TableColumn, ColumnXtype, TableDateFormat } from 'admin-library';
import { ColumnWidthEnum } from '@/shard/constant';
import { MESSAGE_TYPE_MAP, STATUS_MAP } from './constant';

export const COLUMNS: TableColumn[] = [
	{
		title: '#',
		dataIndex: 'id'
	},
	{
		title: '客服账号',
		dataIndex: 'accountName',
		width: ColumnWidthEnum.TEXT6
	},
	{
		title: '承接人',
		dataIndex: 'saleEmployeeName',
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '群发对象',
		dataIndex: 'receivers',
		xtype: ColumnXtype.CUSTOM
	},
	{
		title: '消息类型',
		dataIndex: 'msgType',
		render: data => {
			return MESSAGE_TYPE_MAP[data];
		},
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '群发内容',
		dataIndex: 'msgContent',
		xtype: ColumnXtype.CUSTOM,
		width: 220
	},
	{
		title: '群发时间',
		dataIndex: 'sendTime',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	},
	{
		title: '群发状态',
		dataIndex: 'status',
		render: data => {
			return STATUS_MAP[data];
		},
		width: ColumnWidthEnum.TEXT6
	},
	{
		title: '任务创建时间',
		dataIndex: 'createTime',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	},
	{
		title: '创建人',
		dataIndex: 'createUserName',
		width: ColumnWidthEnum.TEXT4
	}
];
