import { getMapfromArray, getStorefromArray } from '@/shard/utils';

export const STATUS_OPTIONS = [
	{
		label: '待发送',
		value: 1
	},
	{
		label: '已发送',
		value: 2
	},
	{
		label: '已取消',
		value: 3
	},
	{
		label: '已撤回',
		value: 4
	}
];
export const STATUS_MAP = getMapfromArray(STATUS_OPTIONS);
export const STATUS_STORE = getStorefromArray(STATUS_OPTIONS);

export const RECEIVER_TYPE_OPTIONS = [
	{
		label: '个人',
		value: 1
	},
	{
		label: '群',
		value: 2
	}
];
export const RECEIVER_TYPE_MAP = getMapfromArray(RECEIVER_TYPE_OPTIONS);
export const RECEIVER_TYPE_STORE = getStorefromArray(RECEIVER_TYPE_OPTIONS);

export const MESSAGE_TYPE_OPTIONS = [
	{
		label: '文本',
		value: 11
	},
	{
		label: '图片',
		value: 13
	},
	{
		label: '视频',
		value: 15
	},
	{
		label: '文件',
		value: 16
	},
	{
		label: '链接',
		value: 10011
	},
	{
		label: '小程序',
		value: 10002
	},
	{
		label: '优惠券',
		value: 10009
	},
	{
		label: '群公告',
		value: 202
	}
];
export const MESSAGE_TYPE_MAP = getMapfromArray(MESSAGE_TYPE_OPTIONS);
export const MESSAGE_TYPE_STORE = getStorefromArray(MESSAGE_TYPE_OPTIONS);
