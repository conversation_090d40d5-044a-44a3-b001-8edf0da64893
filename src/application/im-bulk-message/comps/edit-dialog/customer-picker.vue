<template>
	<m-select
		mode="multiple"
		@click.stop="open"
		:value="select"
		:fieldNames="{ label: 'mucangNickName', value: 'mucangId' }"
		:options="allContactOptions"
		:open="false"
		max-tag-count="responsive"
		removeIcon=" "
	/>
	<m-form-item-rest>
		<pm-dialog v-model:visible="visible" title="分别发送给" width="420px">
			<div v-if="visible" class="int">
				<div class="pty">
					<div class="sl-item" @click="selectMode = 'all'">
						<span>全部客户</span>
						<check-outlined v-if="selectMode === 'all'" />
					</div>
					<div class="sl-item" @click="selectMode = 'part'">
						<span>按条件筛选的客户</span>
						<check-outlined v-if="selectMode === 'part'" />
					</div>
					<template v-if="!loading">
						<div v-if="selectMode === 'part'">
							<div>
								<UserSelectComp
									:options="allContactOptions"
									:saleEmployeeId="$props.saleEmployeeId"
									v-model:select="posSelect"
								>
									<div>
										<user-add-outlined />
										发送给
									</div>
								</UserSelectComp>
							</div>
							<div>
								<UserSelectComp
									:options="allContactOptions"
									:saleEmployeeId="$props.saleEmployeeId"
									v-model:select="oppSelect"
								>
									<div>
										<user-delete-outlined />
										不发送给
									</div>
								</UserSelectComp>
							</div>
						</div>
					</template>
				</div>
			</div>

			<template #footer>
				<m-button :loading="loading" :disabled="disabled" type="primary" @click="onConfirm">确定</m-button>
			</template>
		</pm-dialog>
	</m-form-item-rest>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, computed, watch } from 'vue';
import { MUtils, MESSAGE_TYPE } from 'admin-library';
import { CheckOutlined } from '@ant-design/icons-vue';
import { UserAddOutlined } from '@ant-design/icons-vue';
import { UserDeleteOutlined } from '@ant-design/icons-vue';

import { recentSixMouth, formatDate } from '@/shard/utils';
import UserSelectComp from './user-select.vue';
import { ListStore as ContactListStore } from '@/application/customer/store';

export default defineComponent({
	components: {
		CheckOutlined,
		UserAddOutlined,
		UserDeleteOutlined,
		UserSelectComp
	},
	props: {
		value: {
			type: String,
			isRequired: true
		},
		saleEmployeeId: {
			type: String,
			isRequired: true
		}
	},
	emits: ['update:value'],
	setup(props, { emit }) {
		const components = {};

		const constants = {};

		const state = reactive({
			selectMode: '',
			select: [],

			posSelect: [],
			oppSelect: [],

			allContactOptions: [],

			loading: false,
			visible: false
		});

		const disabled = computed(() => {
			if (state.selectMode === 'all') {
				return false;
			} else {
				return !(state.posSelect.length || state.oppSelect.length);
			}
		});

		const methods = {
			async open() {
				if (!props.saleEmployeeId) {
					MUtils.toast('请先选择一个承接人', MESSAGE_TYPE.warning);
					return;
				}
				if (state.allContactOptions.length === 0) {
					methods.initContactListOptions();
				}
				state.visible = true;
			},
			async initContactListOptions() {
				state.loading = true;

				const [from, to] = recentSixMouth().map(date => formatDate(date, 'YYYY-MM-DD HH:mm:ss'));
				let res = (await ContactListStore.request({
					saleEmployeeId: props.saleEmployeeId,
					promoteChannel: 2,
					limit: 0,
					createTimeFrom: from,
					createTimeTo: to
				}).getData()) as any[];

				res = res.map(item => {
					let { mucangNickName, mucangId, createTime, category } = item;
					return {
						mucangNickName,
						mucangId,
						createTime,
						category
					};
				});
				state.loading = false;
				state.allContactOptions = res;
			},

			onConfirm() {
				let select = [];
				if (state.selectMode === 'all') {
					select = MUtils.deepClone(state.allContactOptions.map(item => item.mucangId));
				} else {
					if (state.posSelect.length && state.oppSelect.length) {
						select = MUtils.deepClone(state.posSelect.filter(item => state.oppSelect.indexOf(item) === -1));
					} else if (state.posSelect.length) {
						select = MUtils.deepClone(state.posSelect);
					} else if (state.oppSelect.length) {
						select = MUtils.deepClone(
							state.allContactOptions
								.map(item => item.mucangId)
								.filter(item => state.oppSelect.indexOf(item) === -1)
						);
					}
				}

				if (select.length === 0) {
					MUtils.toast(`无满足条件客户，请重新选择`, MESSAGE_TYPE.error);
					return;
				}

				state.select = select;
				emit('update:value', state.select);

				state.visible = false;
			}
		};
		watch(
			() => props.saleEmployeeId,
			(val, oldVal) => {
				if (val === null && oldVal === undefined) {
					return;
				}
				Object.assign(state, {
					selectMode: '',
					select: MUtils.deepClone(props.value),

					posSelect: [],
					oppSelect: [],

					allContactOptions: []
				});
				methods.initContactListOptions();
			},
			{ immediate: true }
		);

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			...props,
			disabled
		};
	}
});
</script>

<style lang="less" scoped>
.pty {
	display: flex;
	flex-direction: column;
	padding: 10px;
	background: #efefef;
	.sl-item {
		display: flex;
		justify-content: space-between;
		cursor: pointer;
	}
}
</style>
