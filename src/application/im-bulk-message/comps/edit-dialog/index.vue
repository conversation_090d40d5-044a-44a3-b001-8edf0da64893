<template>
	<pm-dialog v-model:visible="visible" :title="title" width="680px">
		<template v-if="visible">
			<m-form
				ref="formRef"
				:model="formState"
				:colon="false"
				:label-col="{ style: { width: '107px' } }"
				autocomplete="off"
			>
				<m-form-item label="客服账号" name="accountId" required>
					<m-select
						show-search
						optionFilterProp="name"
						v-model:value="formState.accountId"
						:options="options.accountOptions"
						:fieldNames="{ label: 'name', value: 'id' }"
					/>
				</m-form-item>
				<m-form-item label="承接人" name="saleEmployeeId" required>
					<m-select
						show-search
						optionFilterProp="employeeMixName"
						@change="onEventEmployeeNoChange"
						v-model:value="formState.saleEmployeeId"
						:options="options.employeeOptions"
						:fieldNames="{ label: 'employeeMixName', value: 'id' }"
					/>
				</m-form-item>
				<m-form-item label="群发对象" name="receivers" required>
					<CustomerPickComp :saleEmployeeId="formState.saleEmployeeId" v-model:value="formState.receivers" />
				</m-form-item>
				<m-form-item label="推送时间" name="sendTime" v-if="formState.id" required>
					<m-date-picker
						v-model:value="formState.sendTime"
						:show-time="{ format: 'HH:mm' }"
						format="YYYY-MM-DD HH:mm"
						valueFormat="YYYY-MM-DD HH:mm"
					/>
				</m-form-item>
				<m-form-item label="推送内容">
					<messagegGoupComp
						v-if="formState.id"
						:key="1"
						ref="messageRef"
						v-model:value="formState.messageData"
						:receiverType="1"
					/>
					<template v-else>
						<div
							v-for="(item, index) in formState.bulkMsgItems"
							:key="item.uid"
							style="margin-bottom: 10px; background-color: #f2f2f2"
						>
							<m-row justify="space-between" align="middle" style="padding: 8px 8px 10px">
								<m-col></m-col>
								<m-col>
									<div style="font-size: 14px; line-height: 1.6">消息{{ index + 1 }}</div>
								</m-col>
								<m-col>
									<m-button
										v-if="formState.bulkMsgItems.length > 1"
										danger
										type="link"
										@click="removeItem(index)"
									>
										删除
									</m-button>
								</m-col>
							</m-row>
							<m-divider style="margin: 0 0 10px; height: 2px; background-color: #fff" />
							<div style="padding: 10px 20px 1px; margin-bottom: 10px">
								<div style="margin-bottom: 10px">
									<div>推送时间</div>
									<div>
										<m-form-item
											:name="['bulkMsgItems', index, 'sendTime']"
											label="推送时间"
											:labelCol="{ span: 0 }"
											required
										>
											<m-date-picker
												v-model:value="item.sendTime"
												:show-time="{ format: 'HH:mm' }"
												format="YYYY-MM-DD HH:mm"
												valueFormat="YYYY-MM-DD HH:mm"
											/>
										</m-form-item>
									</div>
								</div>
								<div>
									<div>推送内容</div>
									<div>
										<messagegGoupComp
											:key="1"
											ref="messageRef"
											v-model:value="item.messageData"
											:receiverType="1"
										/>
									</div>
								</div>
							</div>
						</div>
						<m-button
							style="margin-top: 0; margin-bottom: 8px; width: 100%"
							type="dashed"
							@click="addItem"
							:disabled="formState.materialId"
						>
							<template #icon><PlusOutlined /></template>
							继续添加
						</m-button>
					</template>
				</m-form-item>
			</m-form>
		</template>

		<template #footer>
			<m-button plain @click="visible = false">取消</m-button>
			<m-button :loading="loading" type="primary" @click="onConfirm">确定</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import Dayjs from 'dayjs';
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { MESSAGE_TYPE, MUtils } from 'admin-library';
import { PlusOutlined } from '@ant-design/icons-vue';
import getUid from 'ant-design-vue/es/vc-upload/uid';
import { isArray } from 'lodash';

import { cloneFromPick, cloneByMerge } from '@/shard/utils';
import CustomerPickComp from './customer-picker.vue';
import messagegGoupComp from './message-group.vue';
import { RECEIVER_TYPE_OPTIONS } from '../../constant';
import { ListStore as EmployeeListStore } from '@/application/promoter/store';
import { CreateStore, UpdateStore, AccountListStore } from '../../store';

const DefaultFormState = {
	id: null,
	accountId: null,
	saleEmployeeId: null,
	receivers: [],
	sendTime: null,
	msgType: 11,
	msgContent: {},
	messageData: {
		msgType: 11,
		msgContent: {}
	},
	bulkMsgItems: [
		{
			sendTime: null,
			messageData: {
				msgType: 11,
				msgContent: {}
			}
		}
	]
};

export default defineComponent({
	components: {
		PlusOutlined,
		CustomerPickComp,
		messagegGoupComp
	},
	emits: ['refresh'],
	setup(props, { emit }) {
		const components = {
			formRef: ref(null),
			messageRef: ref(null)
		};

		const constants = {};

		const state = reactive({
			visible: false,
			title: '',
			loading: false,
			formState: MUtils.deepClone(DefaultFormState),
			options: {
				accountOptions: [],
				employeeOptions: [],
				receiverType: RECEIVER_TYPE_OPTIONS
			}
		});

		const getValidate = () => {
			let list = [];

			if (isArray(components.messageRef.value)) {
				list = components.messageRef.value.map(item => item.validate());
			} else {
				list = [components.messageRef.value.validate()];
			}
			return list;
		};

		const methods = {
			removeItem(index) {
				state.formState.bulkMsgItems.splice(index, 1);
			},
			async addItem() {
				await Promise.all(getValidate());

				state.formState.bulkMsgItems.push({
					sendTime: '',
					messageData: {
						msgType: 11,
						msgContent: {}
					},
					uid: getUid()
				});
			},
			async open(row?: any) {
				state.formState = MUtils.deepClone(DefaultFormState);
				state.title = row?.id ? '编辑' : '新增';

				if (row) {
					let { msgType, msgContent, sendTime } = row;
					if (sendTime) {
						sendTime = Dayjs(sendTime);
					}
					let messageData = { msgType, msgContent } as any;
					if (messageData.msgContent) {
						messageData.msgContent = JSON.parse(messageData.msgContent);
					}
					if (row.id) {
						state.formState = cloneFromPick(
							{
								...row,
								sendTime,
								messageData
							},
							DefaultFormState
						);
					} else {
						state.formState = cloneFromPick(
							{
								...row,
								bulkMsgItems: [{ messageData, sendTime, uid: getUid() }]
							},
							DefaultFormState
						);
					}
				}

				if (state.formState.receivers) {
					state.formState.receivers = state.formState.receivers.map(item => item.receiverId);
				}

				state.visible = true;

				methods.initAccountOptions();
				methods.initEmployeeOptions();
			},

			onEventEmployeeNoChange() {
				state.formState.receivers = [];
			},

			async initAccountOptions() {
				const res = await AccountListStore.request({ limit: 9999 }).getData();
				if (res) {
					state.options.accountOptions = res.map(item => {
						const { name, id } = item;
						return {
							...item,
							name: `${name}(${id})`
						};
					});
				}
			},
			async initEmployeeOptions() {
				const res = await EmployeeListStore.request({ limit: 9999, station: 1, status: 20 }).getData();
				if (res) {
					state.options.employeeOptions = res;
				}
			},

			async onConfirm() {
				await Promise.all([components.formRef.value.validate(), ...getValidate()]);

				try {
					state.loading = true;
					const params = cloneByMerge(state.formState, DefaultFormState);
					const fetchStore = state.formState.id ? UpdateStore : CreateStore;

					if (state.formState.id) {
						if (params.sendTime) {
							params.sendTime = +new Date(params.sendTime);
						}
						let { msgType, msgContent } = params.messageData;
						delete params.bulkMsgItems;

						params.msgType = msgType;
						params.msgContent = JSON.stringify(msgContent);
					} else {
						params.bulkMsgItems = params.bulkMsgItems.map(item => {
							let { messageData, sendTime } = item;
							if (sendTime) {
								sendTime = +new Date(sendTime);
							}
							delete item.messageData;
							return {
								...item,
								sendTime,
								...messageData
							};
						});

						delete params.sendTime;
						delete params.msgType;
						delete params.msgContent;
					}
					delete params.messageData;

					await fetchStore.request(params).getData();

					emit('refresh');
					state.visible = false;
					MUtils.toast('保存成功', MESSAGE_TYPE.success);
				} finally {
					state.loading = false;
				}
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods
		};
	}
});
</script>
