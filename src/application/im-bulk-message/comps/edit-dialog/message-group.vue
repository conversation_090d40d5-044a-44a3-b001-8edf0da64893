<template>
	<m-form ref="formRef" :model="messageDate" :colon="false" autocomplete="off">
		<m-form-item name="msgType" required>
			<m-radio-group v-model:value="messageDate.msgType" @change="onTypeChange" style="width: 100%">
				<template v-for="item in options.msgType">
					<m-radio-button
						:value="item.value"
						:key="item.value"
						v-if="
							!(item.value === 202 && receiverType !== 2) &&
							!(item.value === 10009 && receiverType !== 1) &&
							!(item.value === 10002 && receiverType === 3)
						"
					>
						{{ item.label }}
					</m-radio-button>
				</template>
			</m-radio-group>
		</m-form-item>
		<m-form-item name="text" v-if="messageDate.msgType === 11" label="文本" :labelCol="{ span: 0 }" required>
			<m-textarea v-model:value="messageDate.text" :rows="4" placeholder="录入要配置的纯文本消息" />
		</m-form-item>
		<m-form-item
			name="fileKeyObj"
			v-else-if="messageDate.msgType === 13"
			extra="图片大小不超过10MB，JPG、PNG、JPEG格式"
			label="图片"
			:labelCol="{ span: 0 }"
			required
		>
			<fileUploadComp fileType="image" :maxSize="10" v-model:value="messageDate.fileKeyObj" />
		</m-form-item>
		<m-form-item
			name="fileKeyObj"
			v-else-if="messageDate.msgType === 15"
			extra="大小不超过10MB，MP4格式"
			label="视频"
			:labelCol="{ span: 0 }"
			required
		>
			<fileUploadComp
				fileType="video"
				:produceCover="true"
				:maxSize="10"
				v-model:value="messageDate.fileKeyObj"
			/>
		</m-form-item>
		<m-form-item
			name="fileKeyObj"
			v-else-if="messageDate.msgType === 16"
			extra="大小不超过10MB，DOC/DOCX/TXT/PDF格式"
			label="文件"
			:labelCol="{ span: 0 }"
			required
		>
			<fileUploadComp fileType="file" :maxSize="10" v-model:value="messageDate.fileKeyObj" />
		</m-form-item>
		<m-form-item v-else-if="messageDate.msgType === 10011">
			<div style="display: flex">
				<div style="width: 140px">
					<m-form-item
						name="fileKeyObj"
						extra="封面图(建议上传正方形，5MB以内)"
						label="封面图"
						:labelCol="{ span: 0 }"
						required
					>
						<fileUploadComp fileType="image" :maxSize="5" v-model:value="messageDate.fileKeyObj" />
					</m-form-item>
				</div>
				<div style="flex: 1">
					<m-form-item label="链接" name="url" required>
						<m-input v-model:value="messageDate.url" placeholder="以http或https开头" />
					</m-form-item>
					<m-form-item label="标题" name="title" required>
						<m-input v-model:value="messageDate.title" :maxlength="20" placeholder="最多20字" />
					</m-form-item>
					<m-form-item label="描述" name="desc" required>
						<m-input v-model:value="messageDate.desc" :maxlength="20" placeholder="最多20字" />
					</m-form-item>
				</div>
			</div>
		</m-form-item>
		<m-form-item v-else-if="messageDate.msgType === 10002">
			<div>
				<m-form-item name="url" label="小程序链接" :labelCol="{ span: 0 }" required>
					<m-input v-model:value="messageDate.url" placeholder="小程序链接" />
				</m-form-item>
			</div>
			<div style="display: flex">
				<div style="flex: 1; padding-right: 10px">
					<m-form-item name="title" label="小程序标题" :labelCol="{ span: 0 }" required>
						<m-input
							v-model:value="messageDate.title"
							placeholder="输入小程序标题，最多64个字"
							:maxlength="64"
						/>
					</m-form-item>
					<m-form-item
						name="fileKeyObj"
						extra="封面图(建议尺寸520*416)"
						label="封面图"
						:labelCol="{ span: 0 }"
						required
					>
						<fileUploadComp fileType="image" :maxSize="10" v-model:value="messageDate.fileKeyObj" />
					</m-form-item>
				</div>
				<div style="flex: 1; padding-left: 10px">
					<m-form-item name="subTitle" label="应用名称" :labelCol="{ span: 0 }" required>
						<m-input v-model:value="messageDate.subTitle" placeholder="输入应用名称" />
					</m-form-item>
					<m-form-item
						name="fileKeyObj2"
						extra="应用图标(建议尺寸128*128)"
						label="应用图标"
						:labelCol="{ span: 0 }"
						required
					>
						<fileUploadComp fileType="image" :maxSize="10" v-model:value="messageDate.fileKeyObj2" />
					</m-form-item>
				</div>
			</div>
		</m-form-item>
		<m-form-item name="content" v-if="messageDate.msgType === 202" label="群公告" :labelCol="{ span: 0 }" required>
			<m-textarea v-model:value="messageDate.content" :rows="4" placeholder="录入要配置的群公告" />
		</m-form-item>
		<m-form-item v-if="messageDate.msgType === 11 && receiverType === 2">
			<m-checkbox v-model:checked="messageDate.atAltFlag">@所有人</m-checkbox>
		</m-form-item>
	</m-form>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref, watch } from 'vue';
import { MUtils } from 'admin-library';

import fileUploadComp from '@/comp/file-upload/index.vue';
import { MESSAGE_TYPE_OPTIONS } from '../../constant';

export default defineComponent({
	components: {
		fileUploadComp
	},
	props: {
		value: {
			type: Object,
			isRequired: true
		},
		receiverType: {
			type: Number,
			isRequired: true
		}
	},
	emits: ['update:value'],
	setup(props, { emit }) {
		const components = {
			formRef: ref(null)
		};

		const constants = {
			options: {
				msgType: MESSAGE_TYPE_OPTIONS
			}
		};

		let value = MUtils.deepClone(props.value);
		let msgType = value.msgType,
			msgContent,
			fileKeyObj,
			fileKeyObj2;
		if (value && value.msgContent) {
			msgContent = value.msgContent;
			fileKeyObj = null;
			if (msgType === 13 || msgType === 16) {
				let { fileUrl, encodedData, ...rest } = msgContent;
				msgContent = rest;
				if (encodedData) {
					fileKeyObj = {
						showUrl: fileUrl,
						encodedData: encodedData
					};
				}
			} else if (msgType === 15) {
				let { fileUrl, encodedData, snapshotEncodeData, duration, ...rest } = msgContent;
				msgContent = rest;
				if (encodedData) {
					fileKeyObj = {
						showUrl: fileUrl,
						encodedData,
						snapshotEncodeData,
						duration
					};
				}
			} else if (msgType === 10011) {
				let { fileUrl, encodedData, ...rest } = msgContent;
				msgContent = rest;
				if (encodedData) {
					fileKeyObj = {
						showUrl: fileUrl,
						encodedData
					};
				}
			} else if (msgType === 10002) {
				let { cover, coverEncodedData, icon, iconEncodedData, ...rest } = msgContent;
				msgContent = rest;
				if (coverEncodedData) {
					fileKeyObj = {
						showUrl: cover,
						encodedData: coverEncodedData
					};
				}
				if (iconEncodedData) {
					fileKeyObj2 = {
						showUrl: icon,
						encodedData: iconEncodedData
					};
				}
			}
		}
		const state = reactive({
			messageDate: MUtils.deepClone({ msgType, fileKeyObj, fileKeyObj2, ...msgContent })
		});

		const methods = {
			onTypeChange() {
				state.messageDate.fileKeyObj = null;
			},
			async validate() {
				return components.formRef.value.validate();
			}
		};

		watch(
			() => state.messageDate,
			() => {
				let { msgType, text, content, fileKeyObj, fileKeyObj2, atAltFlag } = state.messageDate;
				if (msgType === 11) {
					msgContent = { text };
					if (props.receiverType === 2) {
						msgContent.atAltFlag = atAltFlag;
					}
				} else if (msgType === 13 || msgType === 16) {
					msgContent = { encodedData: fileKeyObj?.encodedData };
				} else if (msgType === 15) {
					msgContent = {
						encodedData: fileKeyObj?.encodedData,
						snapshotEncodeData: fileKeyObj?.snapshotEncodeData,
						duration: fileKeyObj?.duration
					};
				} else if (msgType === 10011) {
					let { url, title, desc } = state.messageDate;
					msgContent = {
						url,
						title,
						desc,
						encodedData: fileKeyObj?.encodedData
					};
				} else if (msgType === 10002) {
					let { url, title, subTitle } = state.messageDate;
					msgContent = {
						url,
						title,
						subTitle,
						coverEncodedData: fileKeyObj?.encodedData,
						iconEncodedData: fileKeyObj2?.encodedData
					};
				} else if (msgType === 202) {
					msgContent = { content };
				} else {
					msgContent = {};
				}
				emit('update:value', MUtils.deepClone({ msgType, msgContent }));
			},
			{
				deep: true
			}
		);

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			...props
		};
	}
});
</script>

<style lang="less" scoped>
.file-uploader {
	position: relative;
	display: flex;
	justify-content: center;
	align-items: center;
	overflow: hidden;
	width: 102px;
	height: 102px;
	.btns {
		position: absolute;
		bottom: 0;
		left: 0;
		display: none;
		padding-top: 4px;
		width: 100%;
		height: 26px;
		background-color: rgba(0, 0, 0, 0.5);
	}
	&:hover {
		.btns {
			display: block;
		}
	}
	.anticon {
		margin: 0 4px;
		width: 16px;
		font-size: 16px;
		color: rgba(255, 255, 255, 0.85);
		cursor: pointer;
	}
}
</style>
