<template>
	<div @click.stop="open" class="sk-item">
		<slot></slot>
		<div class="sk-right">
			<span class="sk-right" v-if="select.length">
				<span class="user-name elli">{{ getUserName(select[0]) }}</span>
				<span v-if="select.length > 1">等{{ select.length }}人</span>
			</span>
			<right-outlined />
		</div>
	</div>
	<m-form-item-rest>
		<pm-dialog
			v-model:visible="visible"
			:mask="false"
			title="选择客户"
			width="740px"
			style="height: calc(100vh - 64px)"
			class="sjsj"
			:autoFocus="false"
			@close="onCancel"
		>
			<div v-if="visible" class="int">
				<div>
					<m-form ref="formRef" layout="inline" :model="formState" :colon="false" autocomplete="off">
						<m-form-item name="nickName">
							<m-input style="width: 140px" v-model:value="formState.nickName" placeholder="昵称" />
						</m-form-item>
						<m-form-item name="categories">
							<m-select
								mode="multiple"
								v-model:value="formState.categories"
								placeholder="用户分层"
								style="width: 120px"
								:options="formOptions.categories"
								:allowClear="true"
							/>
						</m-form-item>
						<m-form-item name="categories">
							<m-range-picker v-model:value="formState.rangeTime" style="width: 220px" />
						</m-form-item>
						<m-form-item>
							<m-button @click="onSearch" type="primary">
								<template #icon><search-outlined /></template>
								搜索
							</m-button>
							<m-button style="margin-left: 8px" @click="onReset">
								<template #icon><undo-outlined /></template>
							</m-button>
						</m-form-item>
					</m-form>
					<m-row justify="space-between" align="middle" style="margin-top: 10px">
						<m-col>
							<m-checkbox v-model:checked="checkAll" @change="onCheckAllChange">全部客户</m-checkbox>
						</m-col>
						<m-col></m-col>
					</m-row>
					<m-divider style="margin: 10px 0; height: 1px; background-color: #f2f7ff" />
				</div>
				<div class="sh">
					<DynamicScroller class="recycle" :items="contactOptions" :min-item-size="34" key-field="mucangId">
						<template #="{ item, index, active }">
							<DynamicScrollerItem :item="item" :active="active" :data-index="index">
								<label class="ant-checkbox-wrapper" :for="item.mucangId">
									<span
										class="ant-checkbox"
										:class="{ 'ant-checkbox-checked': checkedUsers.includes(item.mucangId) }"
									>
										<input
											type="checkbox"
											class="ant-checkbox-input"
											:id="item.mucangId"
											:value="item.mucangId"
											@change="onCheckChange"
											:checked="checkedUsers.includes(item.mucangId)"
										/>
										<span class="ant-checkbox-inner"></span>
									</span>
									<span class="ant-cont">
										<span>
											<span>{{ item.mucangNickName }}</span>
											<span class="tag">
												{{ item.category }}
											</span>
										</span>
										<span>{{ formatDate(item.createTime, 'MM-DD') }}</span>
									</span>
								</label>
							</DynamicScrollerItem>
						</template>
					</DynamicScroller>
				</div>
			</div>

			<template #footer>
				<span>(已选择{{ checkedUsers.length }}位客户)</span>
				<m-button :loading="loading" type="primary" style="margin-left: 8px" @click="onConfirm">确定</m-button>
			</template>
		</pm-dialog>
	</m-form-item-rest>
</template>

<script lang="ts">
import Dayjs from 'dayjs';
import { defineComponent, reactive, toRefs, computed } from 'vue';
import { MUtils } from 'admin-library';
import { RightOutlined, UndoOutlined, SearchOutlined } from '@ant-design/icons-vue';
import { DynamicScroller, DynamicScrollerItem } from 'vue-virtual-scroller';
import 'vue-virtual-scroller/dist/vue-virtual-scroller.css';
import { CATEGORY_OPTIONS } from '@/application/leads/constant';
import { ListStore as ContactListStore } from '@/application/customer/store';

import { recentSixMouth } from '@/shard/utils';

const START_TIME_FORMAT = 'YYYY-MM-DD 00:00:00';
const END_TIME_FORMAT = 'YYYY-MM-DD 23:59:59';

const DefaultFormState = {
	nickName: '',
	categories: [],
	rangeTime: recentSixMouth().map(date => Dayjs(date))
};

export default defineComponent({
	components: {
		DynamicScroller,
		DynamicScrollerItem,
		RightOutlined,
		UndoOutlined,
		SearchOutlined
	},
	props: {
		options: {
			type: Array,
			isRequired: true
		} as any,
		select: {
			type: Array,
			isRequired: true
		},
		saleEmployeeId: {
			type: String,
			isRequired: true
		}
	},
	emits: ['update:select'],
	setup(props, { emit }) {
		const components = {};

		const constants = {};

		const state = reactive({
			formState: MUtils.deepClone(DefaultFormState),
			formOptions: {
				categories: CATEGORY_OPTIONS
			},
			contactOptions: MUtils.deepClone(props.options),
			checkedUsers: MUtils.deepClone(props.select),
			loading: false,
			visible: false
		});

		const checkAll = computed(() => {
			let val = state.checkedUsers;
			return val.length > 0 && val.length === state.contactOptions.length;
		});

		const methods = {
			formatDate(date, template) {
				return Dayjs(date).format(template);
			},
			open() {
				state.visible = true;
			},
			onCheckAllChange(e: any) {
				Object.assign(state, {
					checkedUsers: e.target.checked ? state.contactOptions.map(item => item.mucangId) : []
				});
			},
			getUserName(mucangId) {
				let user = state.contactOptions.find(item => item.mucangId === mucangId);
				return user?.mucangNickName;
			},
			async onSearch() {
				const params = {} as any;
				if (state.formState.nickName) {
					params.nickName = state.formState.nickName;
				}
				if (state.formState.categories) {
					params.categories = state.formState.categories;
				}
				if (state.formState.rangeTime && state.formState.rangeTime[0]) {
					params.createTimeFrom = Dayjs(state.formState.rangeTime[0]).format(START_TIME_FORMAT);
					params.createTimeTo = Dayjs(state.formState.rangeTime[1]).format(END_TIME_FORMAT);
				}

				state.loading = true;
				let res = (await ContactListStore.request({
					...params,
					saleEmployeeId: props.saleEmployeeId,
					promoteChannel: 2,
					limit: 9999
				}).getData()) as any[];

				res = res.map(item => {
					let { mucangNickName, mucangId, createTime, category } = item;
					return {
						mucangNickName,
						mucangId,
						createTime,
						category
					};
				});
				state.loading = false;
				state.contactOptions = res;
			},
			onReset() {
				state.contactOptions = MUtils.deepClone(props.options);
			},
			onConfirm() {
				const select = MUtils.deepClone(state.checkedUsers);
				state.visible = false;
				emit('update:select', select);
			},
			onCancel() {
				state.checkedUsers = MUtils.deepClone(props.select);
			},
			onCheckChange(e) {
				let { checked, id } = e.target;
				if (checked) {
					state.checkedUsers.push(id);
				} else {
					let index = state.checkedUsers.indexOf(id);
					state.checkedUsers.splice(index, 1);
				}
			}
		};

		return {
			...toRefs(state),
			checkAll,
			...components,
			...constants,
			...methods,
			...toRefs(props)
		};
	}
});
</script>

<style lang="less">
.recycle {
	overflow-x: hidden;
	overflow-y: auto;
	max-height: 100%;
}
.sjsj {
	.ant-modal-content {
		overflow: hidden;
		height: 100%;
	}
	.ant-modal-body {
		overflow: hidden;
		height: calc(100% - 118px);
	}
	.m-dialog-body {
		overflow: hidden;
		height: 100%;
	}
	.int {
		display: flex;
		flex-direction: column;
		height: 100%;
	}
}
</style>
<style lang="less" scoped>
.sk-item {
	display: flex;
	justify-content: space-between;
	padding: 6px;
	margin: 4px 0;
	background-color: #dfdfdf;
	cursor: pointer;
	.sk-right {
		display: flex;
		justify-content: center;
		align-items: center;
	}
	.user-name {
		max-width: 212px;
		vertical-align: middle;
	}
}
.sh {
	flex: 1;
	height: 0;
}
.ant-checkbox-wrapper {
	display: flex;
	padding: 2px 0;
	.ant-checkbox + span {
		display: flex;
		justify-content: space-between;
		padding: 4px 8px;
		margin-left: 4px;
		background-color: #f7f7f7;
		flex: 1;
	}
	.tag {
		display: inline-block;
		padding: 0 4px;
		margin-left: 8px;
		text-align: center;
		color: #0081ff;
		background-color: #c5e8ff;
	}
}
</style>
