import { TableColumn, TableDateFormat } from 'admin-library';
import { renderCopyComp, renderTextWithColor } from '@/shard/utils';
import { FULL_KEMU_MAP, ColumnWidthEnum } from '@/shard/constant';
import { GENDER_MAP, EDUCATION_MAP, BLACKED_OPTIONS, DROP_OUT_OPTIONS } from './constant';

export const COLUMNS: TableColumn[] = [
	{
		title: '客户编码',
		dataIndex: 'customerNo',
		fixed: 'left'
	},
	{
		title: '木仓昵称',
		dataIndex: 'mucangNickName'
	},
	{
		title: '木仓Id',
		dataIndex: 'mucangId',
		render: data => {
			return renderCopyComp(data);
		},
		width: ColumnWidthEnum.TEXT3
	},
	{
		title: '客户姓名',
		dataIndex: 'name',
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '性别',
		dataIndex: 'gender',
		render: data => {
			return GENDER_MAP[data];
		},
		width: ColumnWidthEnum.TEXT2
	},
	{
		title: '学历',
		dataIndex: 'education',
		render: data => {
			return EDUCATION_MAP[data];
		},
		width: ColumnWidthEnum.TEXT3
	},
	{
		title: '年龄',
		dataIndex: 'age',
		width: ColumnWidthEnum.TEXT2
	},
	{
		title: '考试城市',
		dataIndex: 'cityName',
		width: ColumnWidthEnum.TEXT5
	},
	{
		title: '是否拉黑',
		dataIndex: 'blacklist',
		render: data => {
			return renderTextWithColor(data, BLACKED_OPTIONS);
		},
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '退学',
		dataIndex: 'isDropOutUser',
		render: data => {
			return renderTextWithColor(data, DROP_OUT_OPTIONS, {
				isTag: true,
				inverse: true,
				isReturnStr: true
			});
		},
		width: ColumnWidthEnum.TEXT3
	},
	{
		title: '学习阶段',
		dataIndex: 'learningStage',
		render: data => {
			return FULL_KEMU_MAP[data];
		},
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '创建时间',
		dataIndex: 'createTime',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	}
];
