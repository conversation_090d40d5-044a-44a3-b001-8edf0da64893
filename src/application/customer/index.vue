<template>
	<pm-effi :controller="controller">
		<pm-search>
			<pm-search-single
				:span="5"
				:antd-props="{
					placeholder: ['创建时间', '结束时间']
				}"
				:model-value="defaultTime"
				xtype="RANGEPICKER"
				data-index="createTimeFrom|createTimeTo"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '客户编码'
				}"
				data-index="customerNo"
				xtype="INPUT"
			/>
			<!-- <pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '木仓昵称'
				}"
				data-index="nickName"
				xtype="INPUT"
			/> -->
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '木仓Id'
				}"
				data-index="mucangId"
				xtype="INPUT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '客户姓名'
				}"
				data-index="name"
				xtype="INPUT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '性别'
				}"
				data-index="gender"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '学习阶段'
				}"
				data-index="learningStage"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '是否拉黑'
				}"
				data-index="blacklist"
				xtype="SELECT"
			/>
			<!-- <pm-search-single
				:span="6"
				xtype="CASCADER"
				data-index="cityCode"
				:antd-props="{
					'max-tag-count': 'responsive',
					showSearch: true,
					fieldNames: { label: 'name', value: 'adcode', children: 'districts' },
					placeholder: '所属省市区'
				}"
			/> -->
		</pm-search>

		<pm-table
			:columns="COLUMNS"
			:use-custom-column="true"
			:operations-fixed="true"
			:operations-width="160"
			:sort-num="false"
			:scroll="{
				x: 'max-content'
			}"
			:antd-props="{
				customRow: record => {
					return {
						onClick: () => {
							onClick(record);
						}
					};
				}
			}"
			:rowClassName="setInstallClassName"
		>
			<template #operations="{ record }">
				<m-dropdown trigger="click">
					<span class="primary cursor-pointer" @click.prevent>
						学习记录
						<DownOutlined />
					</span>
					<template #overlay>
						<m-menu>
							<m-menu-item @click="goMockPage(record)">模考记录</m-menu-item>
							<m-menu-item @click="goQuestionPage(record)">错题专项分布</m-menu-item>
							<m-menu-item @click="onRealExam(record)">真实考试记录</m-menu-item>
						</m-menu>
					</template>
				</m-dropdown>
				<m-dropdown trigger="click">
					<span class="primary cursor-pointer" @click.prevent>
						操作
						<DownOutlined />
					</span>
					<template #overlay>
						<m-menu>
							<m-menu-item @click="onEdit(record)">编辑</m-menu-item>
							<m-menu-item @click="goLeads(record)">查看线索</m-menu-item>
							<m-menu-item v-if="!record.blacklist" @click="toggleBlacked(record)">拉黑</m-menu-item>
							<m-menu-item v-else danger @click="toggleBlacked(record)">解除拉黑</m-menu-item>
							<m-menu-item @click="onEditMucangid(record)">木仓账号改绑</m-menu-item>
							<m-menu-item v-if="!isOnline" danger @click="onDel(record)">删除</m-menu-item>
						</m-menu>
					</template>
				</m-dropdown>
			</template>
		</pm-table>
	</pm-effi>

	<edit-comp ref="editRef" @refresh="onRefresh"></edit-comp>
	<edit-mucangid-comp ref="editMucangidRef" @refresh="onRefresh"></edit-mucangid-comp>
</template>

<script lang="ts">
import Dayjs from 'dayjs';
import { defineComponent, reactive, toRefs, ref, shallowRef } from 'vue';
import { ModelController, MUtils, MESSAGE_TYPE, PaasPostMessage, TableDateFormat } from 'admin-library';
import { DownOutlined } from '@ant-design/icons-vue';
import { Store } from '@simplex/simple-store';
import { recentWeek } from '@paas/paas-library';

import EditComp from './comps/edit-dialog/index.vue';
import EditMucangidComp from './comps/edit-mucangid-dialog/index.vue';
import { confirmMessageBox } from '@/shard/utils';
import { getCityJsonData, CityLevelEnum } from '@/shard/city';
import { COLUMNS } from './config';
import { FULL_KEMU_STORE } from '@/shard/constant';
import { GENDER_STORE, BLACKED_STORE } from './constant';
import { ListStore, DelStore, AddBlackStore, RemoveBlackStore } from './store';
import { ItemResponse } from './types';

export default defineComponent({
	components: {
		DownOutlined,
		EditComp,
		EditMucangidComp
	},
	setup() {
		const components = {
			editRef: ref<InstanceType<typeof EditComp>>(),
			editMucangidRef: ref<InstanceType<typeof EditMucangidComp>>()
		};

		const constants = {
			COLUMNS,
			isOnline: APP.isOnline,
			TableDateFormat
		};

		const state = reactive({
			selectedId: '',
			defaultTime: recentWeek().map(date => Dayjs(date))
		});

		const controller = new ModelController({
			table: {
				store: ListStore
			},
			search: {
				gender: {
					store: GENDER_STORE
				},
				cityCode: {
					store: new Store([])
				},
				learningStage: {
					store: FULL_KEMU_STORE
				},
				blacklist: {
					store: BLACKED_STORE
				}
			}
		});
		const cityData = shallowRef([]);

		controller.tableRequest();

		controller.table.onRequest.use(params => {
			const { cityCode } = params;

			return {
				...params,
				cityCode: cityCode ? cityCode[cityCode.length - 1] : null
			};
		});

		const methods = {
			formatDate(date, template) {
				return Dayjs(date).format(template);
			},
			onClick(recored) {
				state.selectedId = recored.customerNo;
			},
			setInstallClassName(record) {
				if (record.customerNo === state.selectedId) {
					return 'selected-row';
				}
			},
			goMockPage(row: ItemResponse) {
				PaasPostMessage.post('navigation.to', '/#/mock-records', {
					title: '模考记录',
					query: {
						mucangId: row?.mucangId,
						from: '客户',
						createTime: row?.createTime
					},
					extendData: {
						style: 'width: 960px; height: 90%; margin: 2% auto 0;'
					},
					target: '_popup'
				});
			},
			goQuestionPage(row: ItemResponse) {
				PaasPostMessage.post('navigation.to', '/#/wrong-questions', {
					title: '错题专项分布',
					query: {
						mucangId: row?.mucangId
					},
					extendData: {
						style: 'width: 960px; height: 90%; margin: 2% auto 0;'
					},
					target: '_popup'
				});
			},
			onRealExam(row: ItemResponse) {
				PaasPostMessage.post('navigation.to', '/#/real-exam-records', {
					title: '学员真实考试记录',
					query: {
						customerNo: row?.customerNo,
						carType: row?.tiku || 'car',
						kemu: row?.tutorKemu || 10
					},
					extendData: {
						style: 'width: 900px; height: 90%; margin: 2% auto 0;'
					},
					target: '_popup'
				});
			},
			goLeads(row: ItemResponse) {
				PaasPostMessage.post('navigation.to', '/#/leads', {
					title: '客户线索',
					query: {
						mucangId: row?.mucangId
					},
					extendData: {
						style: 'width: 960px; height: 90%; margin: 2% auto 0;'
					},
					target: '_popup'
				});
			},

			onEdit(row: ItemResponse) {
				components.editRef.value.open({ ...row, userNo: row.customerNo });
			},
			onEditMucangid(row: ItemResponse) {
				components.editMucangidRef.value.open(row);
			},
			async toggleBlacked(row: ItemResponse) {
				const { customerNo, blacklist } = row;
				const message = blacklist ? '是否将该客户「解除」黑名单？' : '是否将该客户「加入」黑名单？';
				const featchStore = blacklist ? RemoveBlackStore : AddBlackStore;
				await confirmMessageBox(message);

				await featchStore.request({ customerNo }).getData();
				methods.onRefresh();
				MUtils.toast('保存成功', MESSAGE_TYPE.success);
			},
			async onDel(row: ItemResponse) {
				await confirmMessageBox('确认删除吗？');

				await DelStore.request({ customerNo: row.customerNo }).getData();
				methods.onRefresh();
				MUtils.toast('删除成功', MESSAGE_TYPE.success);
			},

			onRefresh() {
				controller.tableRequest();
			},
			async getCityJson() {
				const jsonData = await getCityJsonData(CityLevelEnum.district);

				const list = jsonData?.districts[0]?.districts || [];

				cityData.value = list;

				controller.search.cityCode.updateStore(new Store(list));
			}
		};
		// methods.getCityJson();

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			controller
		};
	}
});
</script>
