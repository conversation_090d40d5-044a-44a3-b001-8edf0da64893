export interface ItemResponse {
	id: number;
	customerNo: string;
	mucangNickName: string;
	mucangId: string;
	nickName: string;
	gender: number;
	age: number;
	education: string;
	createUserId: number;
	createUserName: string;
	createTime: number;
	updateUserId: number;
	updateUserName: string;
	updateTime: number;
	deleted: boolean;
	tiku: string;
	tutorKemu: number;
	blacklist: boolean;
}

export interface RelateedInfoResponse {
	leadsNoList: string[] | number[],
	orderNoList: string[] | number[],
	changeBindOrderNoList: string[] | number[],
	enrollmentProfileIdList: string[] | number[],
	changeBindEnrollmentProfileIdList: string[] | number[]
}