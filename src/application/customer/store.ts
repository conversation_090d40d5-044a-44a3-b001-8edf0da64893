import {
	List,
	Del,
	Update,
	ChangeBind,
	CreateReceptionConfig,
	ViewReceptionConfig,
	AddBlack,
	RemoveBlack,
	CustomerRelationInfo
} from '@/store/customer';

import { ItemResponse, RelateedInfoResponse } from './types';

export const ListStore = new List<Array<ItemResponse>>({});
export const DelStore = new Del({});
export const UpdateStore = new Update({});

export const ChangeBindStore = new ChangeBind({});
export const CustomerRelationInfoStore = new CustomerRelationInfo<RelateedInfoResponse>({});

export const CreateReceptionConfigStore = new CreateReceptionConfig({
	ajaxOptions: {
		contentType: false,
		headers: { 'content-type': 'application/json;charset=UTF-8' }
	}
});
export const ViewReceptionConfigStore = new ViewReceptionConfig({});

export const AddBlackStore = new AddBlack({});
export const RemoveBlackStore = new RemoveBlack({});
