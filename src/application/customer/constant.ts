import { getMapfromArray, getStorefromArray } from '@/shard/utils';
import { ColorEnum } from '@/shard/constant';

export const GENDER_OPTIONS = [
	{
		label: '男',
		value: 1
	},
	{
		label: '女',
		value: 2
	}
];
export const GENDER_MAP = getMapfromArray(GENDER_OPTIONS);
export const GENDER_STORE = getStorefromArray(GENDER_OPTIONS);

export const EDUCATION_OPTIONS = [
	{
		label: '未知',
		value: -1
	},
	{
		label: '小学',
		value: 1
	},
	{
		label: '初中',
		value: 2
	},
	{
		label: '高中',
		value: 3
	},
	{
		label: '中专',
		value: 8
	},
	{
		label: '大专',
		value: 4
	},
	{
		label: '本科',
		value: 5
	},
	{
		label: '研究生',
		value: 6
	},
	{
		label: '博士',
		value: 7
	}
];
export const EDUCATION_MAP = getMapfromArray(EDUCATION_OPTIONS);
export const EDUCATION_STORE = getStorefromArray(EDUCATION_OPTIONS);

export const BLACKED_OPTIONS = [
	{
		label: '已拉黑',
		value: true,
		styleclass: ColorEnum.warning
	},
	{
		label: '未拉黑',
		value: false,
		styleclass: ColorEnum.info
	}
];
export const BLACKED_MAP = getMapfromArray(BLACKED_OPTIONS);
export const BLACKED_STORE = getStorefromArray(BLACKED_OPTIONS);

export const DROP_OUT_OPTIONS = [
	{
		label: '退学',
		value: true,
		styleclass: ColorEnum.warning
	}
];
export const DROP_OUT_MAP = getMapfromArray(DROP_OUT_OPTIONS);
export const DROP_OUT_STORE = getStorefromArray(DROP_OUT_OPTIONS);
