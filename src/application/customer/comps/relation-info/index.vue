<template>
	<pm-dialog v-model:visible="visible" :title="title" width="580px">
		<template v-if="visible">
			<div class="info-list">
				<label>线索编号：</label>
				<p>{{ infoData.leadsNoList?.join(', ') || '' }}</p>
			</div>
			<div class="info-list">
				<label>订单编号：</label>
				<p>{{ infoData.orderNoList?.join(', ') || '' }}</p>
			</div>
			<div class="info-list">
				<label>改绑订单编号：</label>
				<p>{{ infoData.changeBindOrderNoList?.join(', ') || '' }}</p>
			</div>
			<div class="info-list">
				<label>档案ID：</label>
				<p>{{ infoData.enrollmentProfileIdList?.join(', ') || '' }}</p>
			</div>
			<div class="info-list">
				<label>改绑档案ID：</label>
				<p>{{ infoData.changeBindEnrollmentProfileIdList?.join(', ') || '' }}</p>
			</div>

		</template>

		<template #footer>
			<m-button plain @click="visible = false">取消</m-button>
			<m-button :loading="loading" type="primary" @click="onConfirm">确定</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue';
import { CustomerRelationInfoStore } from '../../store';

export default defineComponent({
	emits: ['refresh'],
	setup() {
		const state = reactive({
			visible: false,
			title: '客户关联信息',
			loading: false,
			resolve: null,
			infoData: {
				leadsNoList: [],
				orderNoList: [],
				changeBindOrderNoList: [],
				enrollmentProfileIdList: [],
				changeBindEnrollmentProfileIdList: []
			}
		});

		const constants = {
		};

		const methods = {
			async open(customerNo) {
				return new Promise(resolve => {
					state.visible = true;
					console.log(customerNo);
					(async () => {
						const res = await CustomerRelationInfoStore.request({ customerNo }).getData();
						state.infoData = res;
						console.log('state.infoData', res);
					})();
					state.resolve = resolve;
				});
			},

			async onConfirm() {
				console.log('1');
				state.resolve();
				state.visible = false;
			}
		};

		return {
			...constants,
			...toRefs(state),
			...methods
		};
	}
});
</script>

<style lang="less">
.info-list {
	display: flex;
	padding: 10px 0;
	margin: 0 auto;
	border-bottom: 1px dashed #aaa;
	width: 400px;
	label {
		width: 120px;
	}
	p {
		flex: 1;
		word-break: break-all;
	}
}
</style>
