<template>
	<pm-dialog v-model:visible="visible" :title="title" width="540px">
		<template v-if="visible">
			<m-form
				ref="formRef"
				:model="formState"
				:colon="false"
				:label-col="{ style: { width: '95px' } }"
				autocomplete="off"
			>
				<m-form-item>
					排班表
					<div style="padding: 15px; background-color: #f2f2f2">
						<div
							v-for="(item, index) in formState.scheduleList"
							:key="index"
							style="margin-bottom: 10px; background-color: #fff"
						>
							<m-row justify="space-between" align="middle" style="padding: 8px 8px 10px">
								<m-col></m-col>
								<m-col>
									<div style="font-size: 14px; line-height: 1.6">排班表{{ index + 1 }}</div>
								</m-col>
								<m-col>
									<m-button
										v-if="formState.scheduleList.length > 1"
										danger
										type="link"
										@click="removeItem(index)"
									>
										删除
									</m-button>
								</m-col>
							</m-row>
							<m-divider style="margin: 0 0 10px; height: 2px; background-color: #fff" />
							<div style="padding: 0 20px 10px">
								<div style="margin-bottom: 10px">
									<div>
										<m-form-item
											:name="['scheduleList', index, 'employeeIdList']"
											required
											label="接待成员"
										>
											<m-select
												mode="multiple"
												@change="onEmployeeChange"
												show-search
												optionFilterProp="employeeMixName"
												v-model:value="item.employeeIdList"
												:options="options.dockingPersonList"
												:fieldNames="{ label: 'employeeMixName', value: 'id' }"
												placeholder="接待成员"
											/>
										</m-form-item>
										<m-form-item
											:name="['scheduleList', index, 'dateSpan']"
											required
											label="接待日期"
										>
											<m-range-picker
												v-model:value="item.dateSpan"
												format="YYYY-MM-DD"
												valueFormat="YYYY-MM-DD"
											/>
										</m-form-item>
										<m-form-item
											:name="['scheduleList', index, 'dateSpan']"
											required
											label="接待日期"
										>
											<m-time-range-picker
												v-model:value="item.timeSpan"
												:show-time="{ format: 'HH:mm' }"
												format="HH:mm"
												valueFormat="HH:mm"
											/>
										</m-form-item>
									</div>
								</div>
							</div>
						</div>
						<m-button style="margin-top: 0; margin-bottom: 8px; width: 100%" type="dashed" @click="addItem">
							<template #icon><PlusOutlined /></template>
							继续添加
						</m-button>
					</div>
				</m-form-item>
				<m-form-item extra="当排班中的所有督导均无法承接学员时，将分配给该督导">
					备用督导
					<m-select
						show-search
						optionFilterProp="employeeMixName"
						v-model:value="formState.defaultEmployeeId"
						:options="options.dockingPersonList"
						:fieldNames="{ label: 'employeeMixName', value: 'id' }"
						placeholder="备用督导"
					/>
				</m-form-item>
				<m-form-item>
					承接量配置
					<div style="padding: 15px 15px 0; background-color: #f2f2f2">
						<div v-for="(item, index) in formState.allocateConditionList" :key="index">
							<m-row align="middle">
								<m-col :span="8" style="padding-right: 10px">
									<m-form-item>
										<m-select
											disabled
											:value="item.employeeId"
											:options="options.dockingPersonList"
											:fieldNames="{ label: 'employeeMixName', value: 'id' }"
										/>
									</m-form-item>
								</m-col>
								<m-col :span="8" style="padding-right: 10px">
									<m-form-item
										:name="['allocateConditionList', index, 'turnAllocateCount']"
										label="每轮分配数"
										:labelCol="{ span: 0 }"
										required
										:rules="positiveInteger"
									>
										<m-input-number
											style="width: 100%"
											:min="1"
											v-model:value="item.turnAllocateCount"
											placeholder="每轮分配数，限录入正整数"
										/>
									</m-form-item>
								</m-col>
								<m-col :span="8">
									<m-form-item
										:name="['allocateConditionList', index, 'dailyMaxCount']"
										label="最大承接量"
										:labelCol="{ span: 0 }"
										required
										:rules="positiveInteger"
									>
										<m-input-number
											style="width: 100%"
											:min="1"
											v-model:value="item.dailyMaxCount"
											placeholder="每日最大承接量，限录入正整数"
										/>
									</m-form-item>
								</m-col>
							</m-row>
						</div>
					</div>
				</m-form-item>
			</m-form>
		</template>

		<template #footer>
			<m-button plain @click="visible = false">取消</m-button>
			<m-button :loading="loading" type="primary" @click="onConfirm">确定</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { MESSAGE_TYPE, MUtils } from 'admin-library';
import { union, difference } from 'lodash';

import { cloneFromPick, cloneByMerge } from '@/shard/utils';
import { positiveInteger } from '@/shard/validate';
import { ListStore as EmployeeListStore } from '@/application/promoter/store';
import { CreateReceptionConfigStore, ViewReceptionConfigStore } from '@/application/customer/store';

const DefaultFormState = {
	allocateConditionList: [],
	defaultEmployeeId: null,
	scheduleList: [
		{
			dateSpan: [],
			employeeIdList: [],
			timeSpan: []
		}
	]
};

export default defineComponent({
	components: {},
	emits: ['refresh'],
	setup(props, { emit }) {
		const components = {
			formRef: ref(null)
		};

		const constants = {
			positiveInteger
		};

		const state = reactive({
			visible: false,
			title: '督导自动分配策略',
			loading: false,
			formState: MUtils.deepClone(DefaultFormState),
			options: {
				dockingPersonList: []
			}
		});

		const methods = {
			removeItem(index) {
				state.formState.scheduleList.splice(index, 1);
				methods.onEmployeeChange();
			},
			async addItem() {
				await components.formRef.value.validate();

				state.formState.scheduleList.push({
					dateSpan: [],
					employeeIdList: [],
					timeSpan: []
				});
			},
			async open() {
				let config: any = await ViewReceptionConfigStore.request().getData();

				if (config.scheduleList) {
					config.scheduleList = config.scheduleList.map(item => {
						return {
							...item,
							dateSpan: [item.dateSpan.from, item.dateSpan.to],
							timeSpan: [item.timeSpan.from, item.timeSpan.to]
						};
					});
				}

				state.formState = cloneFromPick(config || {}, DefaultFormState);

				state.visible = true;

				methods.initDockingPersonList();
			},
			async initDockingPersonList() {
				const res = await EmployeeListStore.request({ limit: 9999, station: 5, status: 20 }).getData();
				if (res) {
					state.options.dockingPersonList = res;
				}
			},
			onEmployeeChange() {
				const scheduleIds = union(...state.formState.scheduleList.map(item => item.employeeIdList));
				const conditionIds = state.formState.allocateConditionList.map(item => item.employeeId);
				const add = difference(scheduleIds, conditionIds);
				const remove = difference(conditionIds, scheduleIds);
				if (add.length) {
					state.formState.allocateConditionList.push(
						...add.map(item => {
							return {
								dailyMaxCount: null,
								employeeId: item
							};
						})
					);
				}
				if (remove.length) {
					remove.map(item => {
						const index = state.formState.allocateConditionList.findIndex(con => con.employeeId === item);
						state.formState.allocateConditionList.splice(index, 1);
						return item;
					});
				}
			},

			async onConfirm() {
				await components.formRef.value.validate();

				try {
					state.loading = true;
					const params = cloneByMerge(state.formState, DefaultFormState);
					const fetchStore = CreateReceptionConfigStore;

					params.scheduleList = params.scheduleList.map(item => {
						return {
							...item,
							dateSpan: {
								from: item.dateSpan[0],
								to: item.dateSpan[1]
							},
							timeSpan: {
								from: item.timeSpan[0],
								to: item.timeSpan[1]
							}
						};
					});

					await fetchStore.request(params).getData();

					emit('refresh');
					state.visible = false;
					MUtils.toast('保存成功', MESSAGE_TYPE.success);
				} finally {
					state.loading = false;
				}
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods
		};
	}
});
</script>
