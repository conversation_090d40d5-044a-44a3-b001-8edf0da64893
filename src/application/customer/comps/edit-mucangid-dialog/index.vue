<template>
	<pm-dialog v-model:visible="visible" :title="title" width="680px">
		<template v-if="visible">
			<m-form
				ref="formRef"
				:model="formState"
				:colon="false"
				:label-col="{ style: { width: '105px' } }"
				autocomplete="off"
			>
				<m-form-item label="原账号木仓Id" name="oldMucangid">
					<m-input v-model:value="formState.oldMucangid" placeholder="原账号木仓Id" disabled />
				</m-form-item>
				<!-- <m-form-item label="新账号木仓Id" name="mucangIdNew" required>
					<m-input v-model:value="formState.mucangIdNew" placeholder="新账号木仓Id" />
				</m-form-item> -->
				<m-form-item label="手机号码" name="phone" required :rules="phoneNumber">
					<m-input v-model:value="formState.phone" maxlength="11" placeholder="手机号码" />
				</m-form-item>
			</m-form>
		</template>

		<template #footer>
			<m-button plain @click="visible = false">取消</m-button>
			<m-button :loading="loading" type="primary" @click="onConfirm">确定</m-button>
		</template>
	</pm-dialog>
	<relation-info-comp ref="relationInfoRef"></relation-info-comp>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { MESSAGE_TYPE, MUtils } from 'admin-library';

import { cloneByMerge } from '@/shard/utils';
import { ChangeBindStore } from '../../store';
import relationInfoComp from '../relation-info/index.vue';
import { phoneNumber } from '@/shard/validate';

const DefaultFormState = {
	customerNo: '',
	// mucangIdNew: '',
	phone: ''
};

export default defineComponent({
	emits: ['refresh'],
	components: {
		relationInfoComp
	},
	setup(props, { emit }) {
		const components = {
			formRef: ref(null),
			relationInfoRef: ref(null)
		};

		const constants = {
			phoneNumber
		};

		const state = reactive({
			visible: false,
			title: '木仓账号改绑',
			loading: false,
			formState: MUtils.deepClone(DefaultFormState)
		});

		const methods = {
			open(row: any) {
				let { customerNo, mucangId } = row;
				state.formState = { customerNo, oldMucangid: mucangId };

				state.visible = true;
			},
			async onConfirm() {
				await components.formRef.value.validate();

				await components.relationInfoRef.value.open(state.formState.customerNo);

				// await confirmMessageBox(
				// 	'账号换绑只有一次机会，请确认改绑账号信息无误后提交，换绑成功之后无法进行更改。'
				// );

				try {
					state.loading = true;
					const params = cloneByMerge(state.formState, DefaultFormState);
					const fetchStore = ChangeBindStore;

					delete params.oldMucangid;

					await fetchStore.request(params).getData();

					emit('refresh');
					state.visible = false;
					MUtils.toast('保存成功', MESSAGE_TYPE.success);
				} finally {
					state.loading = false;
				}
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods
		};
	}
});
</script>
