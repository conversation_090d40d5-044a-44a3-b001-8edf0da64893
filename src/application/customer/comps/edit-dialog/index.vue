<template>
	<pm-dialog v-model:visible="visible" :title="title" width="680px">
		<template v-if="visible">
			<m-form
				ref="formRef"
				:model="formState"
				:colon="false"
				:label-col="{ style: { width: '95px' } }"
				autocomplete="off"
			>
				<m-form-item label="姓名" name="name">
					<m-input v-model:value="formState.name" placeholder="姓名" />
				</m-form-item>
				<m-form-item label="性别" name="gender">
					<m-select
						:options="options.genderTypeList"
						v-model:value="formState.gender"
						:allowClear="true"
						placeholder="性别"
					/>
				</m-form-item>
				<m-form-item label="学历" name="education">
					<m-select
						:options="options.educationList"
						v-model:value="formState.education"
						:allowClear="true"
						placeholder="学历"
					/>
				</m-form-item>
				<m-form-item label="年龄" name="age">
					<m-input-number v-model:value="formState.age" :min="1" placeholder="年龄" />
				</m-form-item>
				<m-form-item label="考试城市" name="cityCode">
					<m-cascader
						v-model:value="formState.cityCode"
						:options="options.cityCodeList"
						:fieldNames="{ label: 'name', value: 'adcode', children: 'districts', level: 'city' }"
						:allowClear="true"
					/>
				</m-form-item>
			</m-form>
		</template>

		<template #footer>
			<m-button plain @click="visible = false">取消</m-button>
			<m-button :loading="loading" type="primary" @click="onConfirm">确定</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { MESSAGE_TYPE, MUtils } from 'admin-library';

import { cloneFromPick, cloneByMerge } from '@/shard/utils';
import { getCityJsonData, CityLevelEnum, findCityByCode } from '@/shard/city';
import { GENDER_OPTIONS, EDUCATION_OPTIONS } from '../../constant';
import { UpdateStore } from '../../store';

const DefaultFormState = {
	userNo: '',
	name: '',
	gender: 0,
	education: '',
	age: 0,
	cityCode: []
};

export default defineComponent({
	emits: ['refresh'],
	setup(props, { emit }) {
		const components = {
			formRef: ref(null)
		};

		const constants = {};

		const state = reactive({
			visible: false,
			title: '编辑',
			loading: false,
			formState: MUtils.deepClone(DefaultFormState),
			options: {
				genderTypeList: GENDER_OPTIONS,
				educationList: EDUCATION_OPTIONS,
				cityCodeList: []
			}
		});

		const methods = {
			async open(row: any) {
				state.formState = cloneFromPick(row, DefaultFormState);

				state.visible = true;
				await methods.initCityCodeList();
				if (row.cityCode) {
					const t = findCityByCode(row.cityCode);
					if (t && t.length > 1) {
						state.formState.cityCode = t.map((item: any) => item.adcode);
					}
				}
			},
			async initCityCodeList() {
				const jsonData = await getCityJsonData(CityLevelEnum.city);

				const list = jsonData?.districts[0]?.districts || [];

				state.options.cityCodeList = list;
			},

			async onConfirm() {
				await components.formRef.value.validate();

				try {
					state.loading = true;
					const params = cloneByMerge(state.formState, DefaultFormState);
					const fetchStore = UpdateStore;

					if (params.cityCode?.length > 1) {
						params.cityCode = params.cityCode[params.cityCode.length - 1];
					} else {
						params.cityCode = '';
					}

					await fetchStore.request(params).getData();

					emit('refresh');
					state.visible = false;
					MUtils.toast('保存成功', MESSAGE_TYPE.success);
				} finally {
					state.loading = false;
				}
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods
		};
	}
});
</script>
