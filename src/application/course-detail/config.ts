import { ColumnXtype, TableColumn, TableDateFormat } from 'admin-library';
import { ColumnWidthEnum } from '@/shard/constant';
import { COURSE_DETAIL_STATUS_MAP, LESSON_NUMBER_MAP } from './constant';

export const COLUMNS: TableColumn[] = [
	{
		title: '#',
		dataIndex: 'id'
	},
	{
		title: '讲次',
		dataIndex: 'index',
		render: data => {
			return LESSON_NUMBER_MAP[data] || `第${data}节`;
		},
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '课程名称',
		dataIndex: 'className',
		width: 150
	},
	{
		title: '课程主题',
		dataIndex: 'subject',
		width: 150
	},
	{
		title: '课程内容',
		dataIndex: 'mainContent',
		width: 200,
		render: (text) => {
			if (!text) {return '-';}
			return text.length > 50 ? text.substring(0, 50) + '...' : text;
		}
	},
	{
		title: '上课时间',
		dataIndex: 'beginTime',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	},
	{
		title: '授课老师',
		dataIndex: 'lectureName',
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '状态',
		dataIndex: 'status',
		width: ColumnWidthEnum.TEXT4,
		render: (data) => {
			return COURSE_DETAIL_STATUS_MAP[data];
		}
	},
	{
		title: '创建人',
		dataIndex: 'createUserName',
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '创建时间',
		dataIndex: 'createTime',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	},
	{
		title: '操作',
		dataIndex: 'action',
		xtype: ColumnXtype.CUSTOM,
		fixed: 'right',
		width: 280
	}
];
