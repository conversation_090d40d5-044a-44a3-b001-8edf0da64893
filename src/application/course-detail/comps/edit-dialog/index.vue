<template>
	<pm-dialog v-model:visible="visible" :title="title" width="680px">
		<template v-if="visible">
			<m-form
				ref="formRef"
				:model="formState"
				:colon="false"
				:label-col="{ style: { width: '95px' } }"
				autocomplete="off"
			>
				<m-form-item
					label="课程主题"
					name="subject"
					:rules="[
						{ required: true, message: '请输入课程主题' },
						{ max: 20, message: '课程主题不能超过20个字符' }
					]"
				>
					<m-input
						v-model:value="formState.subject"
						placeholder="请输入课程主题，限20字以内"
						:maxlength="20"
						show-count
					/>
				</m-form-item>

				<m-form-item
					label="课程内容"
					name="mainContent"
					:rules="[
						{ required: true, message: '请输入课程内容' },
						{ max: 200, message: '课程内容不能超过200个字符' }
					]"
				>
					<m-textarea
						v-model:value="formState.mainContent"
						placeholder="请输入课程内容，限200字以内"
						:maxlength="200"
						:rows="4"
						show-count
					/>
				</m-form-item>

				<m-form-item label="上课时间" name="beginTime" :rules="[{ required: true, message: '请选择上课时间' }]">
					<m-date-picker
						v-model:value="formState.beginTime"
						placeholder="请选择上课时间"
						format="YYYY-MM-DD HH:mm:ss"
						value-format="YYYY-MM-DD HH:mm:ss"
						show-time
						:disabled-date="disabledDate"
						style="width: 100%"
					/>
				</m-form-item>

				<m-form-item
					label="授课老师"
					name="lecturerId"
					:rules="[{ required: true, message: '请选择授课老师' }]"
				>
					<m-select
						v-model:value="formState.lecturerId"
						:options="options.lecturerList"
						placeholder="请选择授课老师"
						:disabled="isEdit"
						show-search
						option-filter-prop="label"
					/>
				</m-form-item>

				<m-form-item label="课程讲次" name="index" :rules="[{ required: true, message: '请选择课程讲次' }]">
					<m-select
						v-model:value="formState.index"
						:options="options.lessonNumberList"
						placeholder="请选择课程讲次"
						:disabled="isEdit"
					/>
				</m-form-item>

				<!-- <m-form-item 
					label="小班课程" 
					name="smallClassId"
					:rules="[{ required: true, message: '请选择小班课程' }]"
				>
					<m-select
						v-model:value="formState.smallClassId"
						:options="options.smallClassList"
						placeholder="请选择小班课程"
						:disabled="isEdit"
						show-search
						option-filter-prop="label"
					/>
				</m-form-item> -->
			</m-form>
		</template>

		<template #footer>
			<m-button plain @click="visible = false">取消</m-button>
			<m-button :loading="loading" type="primary" @click="onConfirm">确定</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref, computed } from 'vue';
import { DEFAULT_TIME_FORMAT, MESSAGE_TYPE, MUtils } from 'admin-library';

import { cloneFromPick, cloneByMerge } from '@/shard/utils';
import { LESSON_NUMBER_OPTIONS } from '../../constant';
import { CreateStore, UpdateStore, LecturerListStore } from '../../store';
import Dayjs from 'dayjs';


const DefaultFormState = {
	id: null,
	subject: '',
	mainContent: '',
	beginTime: '',
	lecturerId: null,
	index: null,
	classNo: null
};

export default defineComponent({
	emits: ['refresh'],
	setup(props, { emit }) {
		const components = {
			formRef: ref(null)
		};

		const state = reactive({
			visible: false,
			title: '新增课程',
			loading: false,
			formState: MUtils.deepClone(DefaultFormState),
			operationType: '',
			options: {
				lecturerList: [],
				lessonNumberList: LESSON_NUMBER_OPTIONS,
				smallClassList: []
			}
		});

		const isEdit = computed(() => !!state.formState.id);

		const methods = {
			// 禁用过去的日期
			disabledDate(current: any) {
				return current && current < new Date().setHours(0, 0, 0, 0);
			},

			async initLecturerList() {
				try {
					const data = await LecturerListStore.request({
						type: 1
					}).getData();
					state.options.lecturerList = data.map(item => ({
						label: item.lecturerMixName,
						value: item.id
					}));
				} catch (error) {
					console.error('Failed to load lecturer list:', error);
				}
			},

			async open(row?: any, type?: string) {
				if (row) {
					state.title = '编辑课程';
					state.formState = cloneFromPick(row, DefaultFormState);
					state.formState.beginTime = Dayjs(row.beginTime).format(DEFAULT_TIME_FORMAT);
				} else {
					state.title = '新增课程';
					state.formState = MUtils.deepClone(DefaultFormState);
				}

				if (type === 'copy') {
					state.title = '复制课程';
					state.operationType = 'copy';
					state.formState = cloneFromPick(row, DefaultFormState);
					state.formState.beginTime = Dayjs(row.beginTime).format(DEFAULT_TIME_FORMAT);
				}

				state.visible = true;

				// 初始化选项数据
				await Promise.all([methods.initLecturerList()]);
			},

			async onConfirm() {
				await components.formRef.value.validate();

				try {
					state.loading = true;
					const params = cloneByMerge(state.formState, DefaultFormState);

					let fetchStore = params.id ? UpdateStore : CreateStore;
					if (state.operationType === 'copy') {
						fetchStore = CreateStore;
					}
					await fetchStore.request(params).getData();

					emit('refresh');
					state.visible = false;
					MUtils.toast('保存成功', MESSAGE_TYPE.success);
				} catch (error) {
					console.error('Save course detail failed:', error);
					MUtils.toast('保存失败，请重试', MESSAGE_TYPE.error);
				} finally {
					state.loading = false;
				}
			}
		};

		return {
			...toRefs(state),
			...components,
			...methods,
			isEdit
		};
	}
});
</script>
