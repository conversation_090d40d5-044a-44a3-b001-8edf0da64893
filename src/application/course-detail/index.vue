<template>
	<pm-effi :controller="controller">
		<pm-search>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '请选择状态',
					allowClear: true
				}"
				data-index="status"
				xtype="SELECT"
			/>
		</pm-search>

		<pm-table
			:columns="COLUMNS"
			:use-custom-column="true"
			:operations-fixed="true"
			:operations-width="160"
			:sort-num="false"
			:scroll="{
				x: 'max-content'
			}"
		>
			<template #beforeCustom>
				<m-button type="primary" @click="onCreate">添加课程</m-button>
			</template>

			<template #operations="{ record }">
				<m-button type="link" @click="onEdit(record)">编辑</m-button>
				<m-button type="link" @click="onCopy(record)">复制</m-button>
				<m-button danger v-if="record.status === 1" type="link" @click="onAbandon(record)">废弃</m-button>
			</template>
		</pm-table>
	</pm-effi>

	<edit-dialog ref="editRef" @refresh="onRefresh" />
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { ModelController, MESSAGE_TYPE, MUtils } from 'admin-library';

import { COLUMNS } from './config';
import { COURSE_DETAIL_STATUS_MAP, COURSE_DETAIL_STATUS_STORE } from './constant';
import { AbandonStore } from './store';
import { ItemResponse } from './types';
import EditDialog from './comps/edit-dialog/index.vue';
import { useRoute } from 'vue-router';
import { List } from '@/store/course-detail';

export default defineComponent({
	components: {
		EditDialog
	},
	setup() {
		const route = useRoute();
		const query = route.query;

		console.log('query---', query);

		const components = {
			editRef: ref<InstanceType<typeof EditDialog>>()
		};

		const constants = {
			COLUMNS
		};

		const state = reactive({
			smallClassOptions: []
		});

		const controller = new ModelController({
			table: {
				store: new List(`classNo=${query.classNo}`)
			},
			search: {
				status: {
					store: COURSE_DETAIL_STATUS_STORE
				}
			}
		});

		controller.tableRequest();

		const methods = {
			// async initSmallClassOptions() {
			// 	try {
			// 		const data = await GetSmallClassListStore.request({}).getData();
			// 		state.smallClassOptions = data.map(item => ({
			// 			label: item.courseName,
			// 			value: item.id
			// 		}));
			// 	} catch (error) {
			// 		console.error('Failed to load small class options:', error);
			// 	}
			// },

			getStatusText(status: number) {
				return COURSE_DETAIL_STATUS_MAP[status] || '未知';
			},

			getStatusColor(status: number) {
				switch (status) {
					case 1:
						return 'green';
					case 2:
						return 'red';
					default:
						return 'default';
				}
			},

			onCreate() {
				components.editRef.value?.open({
					classNo: query.classNo
				});
			},

			onEdit(record: ItemResponse) {
				record.classNo = query.classNo;
				components.editRef.value?.open(record);
			},

			async onCopy(record: ItemResponse) {
				console.log('record', record);
				record.classNo = query.classNo;
				components.editRef.value?.open(record, 'copy');
			},

			async onAbandon(record: ItemResponse) {
				const confirmed = await MUtils.confirm({
					title: '确定要废弃该课程吗？废弃后无法恢复。',
					type: MESSAGE_TYPE.warning,
					confirmText: '确定',
					cancelText: '取消'
				});

				if (!confirmed) {
					return;
				}

				try {
					await AbandonStore.request({ scheduleId: record.id }).getData();
					MUtils.toast('废弃成功', MESSAGE_TYPE.success);
					controller.tableRequest();
				} catch (error) {
					console.error('Abandon failed:', error);
					MUtils.toast('废弃失败，请重试', MESSAGE_TYPE.error);
				}
			},

			onRefresh() {
				controller.tableRequest();
			}
		};

		// 初始化小班课选项
		// methods.initSmallClassOptions();

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			controller
		};
	}
});
</script>
