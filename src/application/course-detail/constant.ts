import { getMapfromArray, getStorefromArray } from '@/shard/utils';
import { ColorEnum } from '@/shard/constant';

// 课程状态选项
export const COURSE_DETAIL_STATUS_OPTIONS = [
	{
		label: '生效中',
		value: 1,
		styleclass: ColorEnum.success
	},
	{
		label: '已废弃',
		value: 2,
		styleclass: ColorEnum.danger
	}
];
export const COURSE_DETAIL_STATUS_MAP = getMapfromArray(COURSE_DETAIL_STATUS_OPTIONS);
export const COURSE_DETAIL_STATUS_STORE = getStorefromArray(COURSE_DETAIL_STATUS_OPTIONS);

// 课程讲次选项（第一到八节）
export const LESSON_NUMBER_OPTIONS = [
	{
		label: '第一节',
		value: 1
	},
	{
		label: '第二节',
		value: 2
	},
	{
		label: '第三节',
		value: 3
	},
	{
		label: '第四节',
		value: 4
	},
	{
		label: '第五节',
		value: 5
	},
	{
		label: '第六节',
		value: 6
	},
	{
		label: '第七节',
		value: 7
	},
	{
		label: '第八节',
		value: 8
	}
];
export const LESSON_NUMBER_MAP = getMapfromArray(LESSON_NUMBER_OPTIONS);
export const LESSON_NUMBER_STORE = getStorefromArray(LESSON_NUMBER_OPTIONS);
