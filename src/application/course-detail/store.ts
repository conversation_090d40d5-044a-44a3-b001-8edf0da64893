import {
	Create,
	Update,
	Abandon,
	GetPlayback,
} from '@/store/course-detail';

import { List as LecturerList } from '@/store/part-time-teacher'

import { ItemResponse as LecturerListResponse } from '@/application/part-time-teacher/types';

export const CreateStore = new Create({});
export const UpdateStore = new Update({});
export const AbandonStore = new Abandon({});
export const GetPlaybackStore = new GetPlayback<{value: string}>({});
export const LecturerListStore = new LecturerList<Array<LecturerListResponse>>();

