<template>
	<pm-dialog v-model:visible="visible" :title="title" width="680px">
		<template v-if="visible">
			<m-form
				ref="formRef"
				:model="formState"
				:colon="false"
				:label-col="{ style: { width: '95px' } }"
				autocomplete="off"
			>
				<m-form-item label="类型" name="type">
					<m-select
						v-model:value="formState.type"
						:options="options.typeList"
						:allowClear="true"
						placeholder="类型"
					/>
				</m-form-item>
				<m-form-item label="车型" name="carType">
					<m-select
						v-model:value="formState.carType"
						:options="options.carTypeList"
						:allowClear="true"
						placeholder="车型"
						@change="onCarTypeChange"
					/>
				</m-form-item>
				<m-form-item label="科目" name="kemu">
					<m-select
						v-model:value="formState.kemu"
						:options="options.kemuList"
						:allowClear="true"
						placeholder="科目"
					/>
				</m-form-item>
				<m-form-item label="专项名称" name="bizId" v-if="formState.type === 3">
					<m-select
						v-model:value="formState.bizId"
						:options="options.speList"
						:fieldNames="{ label: 'name', value: 'id' }"
						placeholder="专项名称"
					/>
				</m-form-item>
				<m-form-item label="私教专项" name="bizIdNew" v-if="formState.type === 3" required>
					<m-select
						v-model:value="formState.bizIdNew"
						:options="options.specializedList"
						:fieldNames="{ label: 'name', value: 'id' }"
						placeholder="私教专项"
					/>
				</m-form-item>
				<m-form-item label="课程名称" name="name">
					<m-input v-model:value="formState.name" placeholder="课程名称" />
				</m-form-item>
				<m-form-item label="备注" name="remark">
					<m-input v-model:value="formState.remark" placeholder="备注" />
				</m-form-item>
				<m-form-item label="课程链接" name="url">
					<m-textarea v-model:value="formState.url" :rows="5" />
				</m-form-item>
				<m-form-item label="分组" name="groupId" required>
					<m-select
						v-model:value="formState.groupId"
						:options="options.groupList"
						:fieldNames="{ label: 'groupName', value: 'id' }"
						:allowClear="true"
						placeholder="分组"
					/>
				</m-form-item>
				<m-form-item label="显示顺序" name="orderValue" required>
					<m-input-number v-model:value="formState.orderValue" placeholder="显示顺序" />
				</m-form-item>
			</m-form>
		</template>

		<template #footer>
			<m-button plain @click="visible = false">取消</m-button>
			<m-button :loading="loading" type="primary" @click="onConfirm">确定</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { MESSAGE_TYPE, MUtils } from 'admin-library';

import { cloneFromPick, cloneByMerge } from '@/shard/utils';
import { CAR_TYPE_OPTIONS, KEMU_OPTIONS } from '@/shard/constant';
import { TYPE_OPTIONS } from '../../constant';
import { ListStore as groupListStore } from '@/application/lesson-group/store';
import { TagListStore } from '@/popup-pages/course-exercise/store';
import { ListStore as SpecializedListStore } from '@/application/specialized/store';
import { CreateStore, UpdateStore } from '../../store';

const DefaultFormState = {
	id: null,
	type: '',
	bizId: '',
	bizIdNew: '',
	name: '',
	carType: '',
	kemu: '',
	remark: '',
	url: '',
	groupId: '',
	orderValue: ''
};

export default defineComponent({
	emits: ['refresh'],
	setup(props, { emit }) {
		const components = {
			formRef: ref(null)
		};

		const constants = {};

		const state = reactive({
			visible: false,
			title: '',
			loading: false,
			formState: MUtils.deepClone(DefaultFormState),
			options: {
				groupList: [],
				carTypeList: CAR_TYPE_OPTIONS,
				kemuList: KEMU_OPTIONS,
				typeList: TYPE_OPTIONS,
				speList: [],
				specializedList: []
			}
		});

		const methods = {
			async initGroupList() {
				let res = await groupListStore.request().getData();
				if (res) {
					state.options.groupList = res;
				}
			},
			async initSpeOptions() {
				let res = await TagListStore.request({
					limit: 9999,
					kemu: 10
				}).getData();
				if (res) {
					res = res.map(item => {
						item.id = String(item.id);
						return item;
					});
					state.options.speList = res;
				}
			},
			async initSpecializedList() {
				let res = await SpecializedListStore.request({
					limit: 9999,
					status: 2,
					carType: state.formState.carType
				}).getData();
				if (res) {
					state.options.specializedList = res.map(item => {
						return {
							...item,
							name: `${item.name}(id：${item.id})`
						};
					});
				}
			},
			onCarTypeChange(value) {
				state.formState.bizIdNew = '';
				state.formState.carType = value;
				methods.initSpecializedList();
			},
			open(row?: any) {
				state.formState = MUtils.deepClone(DefaultFormState);
				state.title = row?.id ? '编辑' : '新增';

				if (row) {
					state.formState = cloneFromPick(row, DefaultFormState);
				}

				state.visible = true;

				methods.initGroupList();
				methods.initSpeOptions();
				methods.initSpecializedList();
			},
			async onConfirm() {
				await components.formRef.value.validate();

				try {
					state.loading = true;
					const params = cloneByMerge(state.formState, DefaultFormState);
					const fetchStore = state.formState.id ? UpdateStore : CreateStore;

					if (params.type !== 3) {
						delete params.bizId;
					}

					await fetchStore.request(params).getData();

					emit('refresh');
					state.visible = false;
					MUtils.toast('保存成功', MESSAGE_TYPE.success);
				} finally {
					state.loading = false;
				}
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods
		};
	}
});
</script>
