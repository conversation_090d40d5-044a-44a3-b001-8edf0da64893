<template>
	<pm-dialog v-model:visible="visible" :title="title" width="680px">
		<template v-if="visible">
			<m-form
				ref="formRef"
				:model="formState"
				:colon="false"
				:label-col="{ style: { width: '95px' } }"
				autocomplete="off"
			>
				<m-form-item label="课程笔记" name="resourceUrls">
					<fileUploadComp fileType="image" v-model:value="formState.resourceUrls" :multiple="true" />
				</m-form-item>
			</m-form>
		</template>

		<template #footer>
			<m-button plain @click="visible = false">取消</m-button>
			<m-button :loading="loading" type="primary" @click="onConfirm">确定</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { MESSAGE_TYPE, MUtils } from 'admin-library';

import { cloneFrom<PERSON>ick, cloneByMerge } from '@/shard/utils';
import fileUploadComp from '@/comp/file-upload/index.vue';
import { ViewStore, SetResourceStore } from '../../store';

const DefaultFormState = {
	lessonId: '',
	resourceUrls: []
};

export default defineComponent({
	components: {
		fileUploadComp
	},
	emits: ['refresh'],
	setup(props, { emit }) {
		const components = {
			formRef: ref(null)
		};

		const constants = {};

		const state = reactive({
			visible: false,
			title: '设置课程笔记',
			loading: false,
			formState: MUtils.deepClone(DefaultFormState)
		});

		const methods = {
			async open({ id: lessonId }) {
				const viewData: any = await ViewStore.request({ id: lessonId }).getData();
				let resourceUrls =
					viewData.resources?.map(item => ({
						encodedData: item.resourceKey,
						showUrl: item.resourceUrl
					})) || [];

				state.formState = cloneFromPick({ lessonId, resourceUrls }, DefaultFormState);

				state.visible = true;
			},
			async onConfirm() {
				await components.formRef.value.validate();

				try {
					state.loading = true;
					const params = cloneByMerge(state.formState, DefaultFormState);
					const fetchStore = SetResourceStore;

					if (params.resourceUrls && params.resourceUrls.length) {
						params.resources = params.resourceUrls.map((item: any) => item.encodedData);
						params.resources = JSON.stringify(params.resources);
					}

					await fetchStore.request(params).getData();

					emit('refresh');
					state.visible = false;
					MUtils.toast('保存成功', MESSAGE_TYPE.success);
				} finally {
					state.loading = false;
				}
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods
		};
	}
});
</script>
