<template>
	<pm-effi :controller="controller">
		<pm-search>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '课程名称'
				}"
				data-index="name"
				xtype="INPUT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '类型'
				}"
				data-index="type"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '车型'
				}"
				data-index="carType"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '科目'
				}"
				data-index="kemu"
				xtype="SELECT"
			/>
		</pm-search>

		<pm-table
			:columns="COLUMNS"
			:use-custom-column="true"
			:operations-fixed="true"
			:operations-width="200"
			:sort-num="false"
			:scroll="{
				x: 'max-content'
			}"
		>
			<template #beforeCustom>
				<m-button type="primary" @click="onCreate">添加</m-button>
				<m-button type="primary" @click="multiSetting">分组管理</m-button>
			</template>
			<template #url="{ record }">
				<m-button type="link" @click="viewLink(record)">查看</m-button>
			</template>
			<template #note="{ record }">
				<m-button type="link" @click="viewNote(record)">查看</m-button>
			</template>

			<template #operations="{ record }">
				<m-button type="link" @click="onEdit(record)">编辑</m-button>
				<m-button type="link" @click="goHomework(record)">课后作业</m-button>
				<m-button danger type="link" @click="onDel(record)">删除</m-button>
				<m-button type="link" @click="editNote(record)">设置笔记</m-button>
			</template>
		</pm-table>
	</pm-effi>

	<edit-comp ref="editRef" @refresh="onRefresh"></edit-comp>
	<edit-note-comp ref="editNoteRef" @refresh="onRefresh"></edit-note-comp>
</template>

<script lang="ts">
import { defineComponent, reactive, ref, toRefs } from 'vue';
import { ModelController, MUtils, PaasPostMessage, MESSAGE_TYPE } from 'admin-library';

import EditComp from './comps/edit-dialog/index.vue';
import EditNoteComp from './comps/edit-note/index.vue';
import { confirmMessageBox } from '@/shard/utils';
import { COLUMNS } from './config';
import { CAR_TYPE_STORE, KEMU_STORE } from '@/shard/constant';
import { TYPE_STORE } from './constant';
import { ListStore, ViewStore, DelStore } from './store';
import { ItemResponse } from './types';

export default defineComponent({
	components: {
		EditComp,
		EditNoteComp
	},
	setup() {
		const components = {
			editRef: ref<InstanceType<typeof EditComp>>(),
			editNoteRef: ref<InstanceType<typeof EditNoteComp>>()
		};

		const constants = {
			COLUMNS
		};

		const state = reactive({});

		const controller = new ModelController({
			table: {
				store: ListStore
			},
			search: {
				carType: {
					store: CAR_TYPE_STORE
				},
				kemu: {
					store: KEMU_STORE
				},
				type: {
					store: TYPE_STORE
				}
			}
		});
		controller.tableRequest();

		const methods = {
			goHomework(row) {
				PaasPostMessage.post('navigation.to', '/#/homework', {
					title: '课后作业-' + row.name,
					query: {
						lessonId: row?.id
					},
					target: '_tab'
				});
			},
			multiSetting() {
				PaasPostMessage.post('navigation.to', '/#/lesson-group', {
					title: '分组管理',
					extendData: {
						style: 'width: 60%; height: 600px; margin: 140px auto 0;'
					},
					target: '_popup'
				});
			},
			viewLink(lineData) {
				MUtils.alert({
					title: '课程链接',
					content: lineData.url
				});
			},
			async viewNote(row) {
				const viewData: ItemResponse = await ViewStore.request({ id: row.id }).getData();

				let imgstr = '<div style="max-height: 600px; overflow-y: auto">';
				for (let i = 0; i < viewData.resources.length; i++) {
					imgstr += `<div style="padding-right: 12px"><img style="width:100%" src="${viewData.resources[i].resourceUrl}" /></div>`;
				}
				imgstr += '</div>';
				if (imgstr) {
					MUtils.alert({
						title: '课程笔记',
						content: imgstr
					});
				} else {
					MUtils.toast('暂无笔记，请先设置');
				}
			},
			onCreate() {
				components.editRef.value.open();
			},
			onEdit(row: ItemResponse) {
				components.editRef.value.open(row);
			},
			editNote(row: ItemResponse) {
				components.editNoteRef.value.open(row);
			},
			async onDel(row: ItemResponse) {
				await confirmMessageBox('确认删除吗？');

				await DelStore.request({ id: row.id }).getData();
				methods.onRefresh();
				MUtils.toast('删除成功', MESSAGE_TYPE.success);
			},

			onRefresh() {
				controller.tableRequest();
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			controller
		};
	}
});
</script>
