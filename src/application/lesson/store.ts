import { List, Del, Create, Update, View, SetResource } from '@/store/lesson';
import { ItemResponse } from './types';

export const ListStore = new List<Array<ItemResponse>>({});
export const DelStore = new Del({});
export const CreateStore = new Create({});
export const UpdateStore = new Update({});

export const ViewStore = new View<ItemResponse>({});
export const SetResourceStore = new SetResource({});
