import { ColumnXtype, TableColumn } from 'admin-library';
import { ColumnWidthEnum, KEMU_MAP, CAR_TYPE_MAP } from '@/shard/constant';
import { TYPE_MAP } from './constant';

export const COLUMNS: TableColumn[] = [
	{
		title: '#',
		dataIndex: 'id',
		fixed: 'left'
	},
	{
		title: '课程名称',
		dataIndex: 'name',
		fixed: 'left'
	},
	{
		title: '类型',
		dataIndex: 'type',
		render: data => {
			return TYPE_MAP[data];
		},
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '专项训练名称',
		dataIndex: 'bizName',
		width: ColumnWidthEnum.TEXT6
	},
	{
		title: '专项训练Id',
		dataIndex: 'bizId',
		width: ColumnWidthEnum.TEXT5
	},
	{
		title: '私教专项Id',
		dataIndex: 'bizIdNew',
		width: ColumnWidthEnum.TEXT5
	},
	{
		title: '所属分组',
		dataIndex: 'groupName'
	},
	{
		title: '车型',
		dataIndex: 'carType',
		render: data => {
			return CAR_TYPE_MAP[data];
		},
		width: ColumnWidthEnum.TEXT5
	},
	{
		title: '科目',
		dataIndex: 'kemu',
		render: data => {
			return KEMU_MAP[data];
		},
		width: ColumnWidthEnum.TEXT3
	},
	{
		title: '备注',
		dataIndex: 'remark',
		width: 100
	},
	{
		title: '课程链接',
		dataIndex: 'url',
		xtype: ColumnXtype.CUSTOM,
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '课程笔记',
		dataIndex: 'note',
		xtype: ColumnXtype.CUSTOM,
		width: ColumnWidthEnum.TEXT4
	}
];
