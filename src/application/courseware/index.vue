<template>
	<pm-effi :controller="controller">
		<pm-search>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '课件名称'
				}"
				data-index="name"
				xtype="INPUT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '类型'
				}"
				data-index="type"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '车型'
				}"
				data-index="carType"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '科目'
				}"
				data-index="kemu"
				xtype="SELECT"
			/>
		</pm-search>

		<pm-table
			:columns="COLUMNS"
			:use-custom-column="true"
			:operations-fixed="true"
			:operations-width="200"
			:sort-num="false"
			:scroll="{
				x: 'max-content'
			}"
		>
			<template #beforeCustom>
				<m-button type="primary" @click="onCreate">添加</m-button>
				<m-button type="primary" @click="getAllSpe">获取所有专项</m-button>
			</template>
			<template #operations="{ record }">
				<m-button type="link" @click="onEdit(record)">编辑</m-button>
				<m-button type="link" @click="goResList(record)">课件维护</m-button>
				<m-button danger type="link" v-if="record.type === 2" @click="onDel(record)">删除</m-button>
			</template>
		</pm-table>
	</pm-effi>

	<edit-comp ref="editRef" @refresh="onRefresh"></edit-comp>
	<edit-layer-comp ref="editLayerRef" @refresh="onRefresh"></edit-layer-comp>
</template>

<script lang="ts">
import { defineComponent, reactive, ref, toRefs } from 'vue';
import { ModelController, MUtils, MESSAGE_TYPE, PaasPostMessage } from 'admin-library';

import EditComp from './comps/edit-dialog/index.vue';
import { confirmMessageBox } from '@/shard/utils';
import { COLUMNS } from './config';
import { CAR_TYPE_STORE, EXAM_KEMU_STORE } from '@/shard/constant';
import { TYPE_STORE } from './constant';
import { ListStore, DelStore, SyncSpeTagStore } from './store';
import { ItemResponse } from './types';

export default defineComponent({
	components: {
		EditComp
	},
	setup() {
		const components = {
			editRef: ref<InstanceType<typeof EditComp>>()
		};

		const constants = {
			COLUMNS
		};

		const state = reactive({});

		const controller = new ModelController({
			table: {
				store: ListStore
			},
			search: {
				carType: {
					store: CAR_TYPE_STORE
				},
				kemu: {
					store: EXAM_KEMU_STORE
				},
				type: {
					store: TYPE_STORE
				}
			}
		});
		controller.tableRequest();

		const methods = {
			goResList(row) {
				let practiceId;
				if (row.type === 1) {
					practiceId = row.bizId;
				}
				PaasPostMessage.post('navigation.to', '/#/courseware-resource', {
					title: '课件维护',
					query: {
						coursewareId: row.id,
						practiceId,
						coursewareType: row.type
					},
					extendData: {
						style: 'width: 960px; height: 90%; margin: 2% auto 0;'
					},
					target: '_popup'
				});
			},
			async getAllSpe() {
				await SyncSpeTagStore.request().getData();
				MUtils.toast('同步题库标签数据成功！', MESSAGE_TYPE.success);
				methods.onRefresh();
			},
			onCreate() {
				components.editRef.value.open();
			},
			onEdit(row: ItemResponse) {
				components.editRef.value.open(row, row.kemu);
			},

			async onDel(row: ItemResponse) {
				await confirmMessageBox('确认删除吗？');

				await DelStore.request({ id: row.id }).getData();
				methods.onRefresh();
				MUtils.toast('删除成功', MESSAGE_TYPE.success);
			},

			onRefresh() {
				controller.tableRequest();
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			controller
		};
	}
});
</script>
