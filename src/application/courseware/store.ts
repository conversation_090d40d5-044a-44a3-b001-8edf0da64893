import { List, Del, Create, Update, SyncSpeTag, UpdateLevelInfo } from '@/store/courseware';
import { ItemResponse } from './types';

export const ListStore = new List<Array<ItemResponse>>({});
export const DelStore = new Del({});
export const CreateStore = new Create({});
export const UpdateStore = new Update({});
export const SyncSpeTagStore = new SyncSpeTag({});
export const UpdateLevelInfoStore = new UpdateLevelInfo({});
