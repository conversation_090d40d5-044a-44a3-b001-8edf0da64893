import { TableColumn, TableDateFormat } from 'admin-library';
import { ColumnWidthEnum, CAR_TYPE_MAP, EXAM_KEMU_MAP } from '@/shard/constant';
import { TYPE_MAP } from './constant';

export const COLUMNS: TableColumn[] = [
	{
		title: '#',
		dataIndex: 'id'
	},
	{
		title: '课件名称',
		dataIndex: 'name'
	},
	{
		title: '类型',
		dataIndex: 'type',
		render: data => {
			return TYPE_MAP[data];
		},
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '车型',
		dataIndex: 'carType',
		render: data => {
			return CAR_TYPE_MAP[data];
		},
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '辅导科目',
		dataIndex: 'kemu',
		render: data => {
			return EXAM_KEMU_MAP[data];
		},
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '专项名称',
		dataIndex: 'bizName',
		width: ColumnWidthEnum.TEXT6
	},
	{
		title: '专项Id',
		dataIndex: 'bizId',
		width: ColumnWidthEnum.TEXT5
	},
	{
		title: '私教专项Id',
		dataIndex: 'bizIdNew',
		width: ColumnWidthEnum.TEXT5
	},
	{
		title: '层级',
		dataIndex: 'layer',
		width: ColumnWidthEnum.TEXT3
	},
	{
		title: '创建人',
		dataIndex: 'createUserName',
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '创建时间',
		dataIndex: 'createTime',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	},
	{
		title: '更新人',
		dataIndex: 'updateUserName',
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '更新时间',
		dataIndex: 'updateTime',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	}
];
