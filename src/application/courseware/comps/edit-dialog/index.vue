<template>
	<pm-dialog v-model:visible="visible" :title="title" width="680px">
		<template v-if="visible">
			<m-form
				ref="formRef"
				:model="formState"
				:colon="false"
				:label-col="{ style: { width: '95px' } }"
				autocomplete="off"
			>
				<m-form-item label="课件名称" name="name" required>
					<m-input v-model:value="formState.name" placeholder="课件名称" />
				</m-form-item>
				<m-form-item label="类型" name="type" required>
					<m-select
						disabled
						v-model:value="formState.type"
						:options="options.typeList"
						:allowClear="true"
						placeholder="类型"
					/>
				</m-form-item>
				<m-form-item label="私教专项" name="bizIdNew" v-if="formState.type === 1">
					<m-select
						show-search
						optionFilterProp="name"
						v-model:value="formState.bizIdNew"
						:options="options.specializedList"
						:fieldNames="{ label: 'name', value: 'id' }"
						placeholder="私教专项"
					/>
				</m-form-item>
				<m-form-item label="专项名称" name="bizName" v-if="formState.type === 1">
					<m-input disabled v-model:value="formState.bizName" placeholder="专项名称" />
				</m-form-item>
				<m-form-item label="车型" name="carType" v-if="formState.type === 2" required>
					<m-select
						:disabled="formState.id"
						v-model:value="formState.carType"
						:options="options.carList"
						placeholder="车型"
					/>
				</m-form-item>
				<m-form-item label="辅导科目" name="kemu" v-if="formState.type === 2" required>
					<m-select
						:disabled="formState.id"
						v-model:value="formState.kemu"
						:options="options.kemuList"
						placeholder="辅导科目"
					/>
				</m-form-item>
				<m-form-item label="层级" name="layer" v-if="formState.type === 1">
					<m-select
						v-model:value="formState.layer"
						:options="options.layerList"
						placeholder="层级"
						:allowClear="true"
					/>
				</m-form-item>
			</m-form>
		</template>

		<template #footer>
			<m-button plain @click="visible = false">取消</m-button>
			<m-button :loading="loading" type="primary" @click="onConfirm">确定</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { MESSAGE_TYPE, MUtils } from 'admin-library';

import { cloneFromPick, cloneByMerge } from '@/shard/utils';
import { CAR_TYPE_OPTIONS, EXAM_KEMU_OPTIONS } from '@/shard/constant';
import { TYPE_OPTIONS, LAYER_OPTIONS } from '../../constant';
import { ListStore as SpecializedListStore } from '@/application/specialized/store';
import { CreateStore, UpdateStore } from '../../store';

const DefaultFormState = {
	id: null,
	name: '',
	type: 2,
	bizIdNew: null,
	bizName: '',
	carType: '',
	kemu: null,
	layer: null
};

export default defineComponent({
	emits: ['refresh'],
	setup(props, { emit }) {
		const components = {
			formRef: ref(null)
		};

		const constants = {};

		const state = reactive({
			visible: false,
			title: '',
			loading: false,
			formState: MUtils.deepClone(DefaultFormState),
			options: {
				typeList: TYPE_OPTIONS.map(item => {
					return { ...item, disabled: item.value === 1 };
				}),
				carList: CAR_TYPE_OPTIONS,
				kemuList: EXAM_KEMU_OPTIONS.map(item => {
					return { ...item, disabled: item.value === 40 };
				}),
				layerList: LAYER_OPTIONS,
				specializedList: []
			}
		});

		const methods = {
			async initSpecializedList(kemu) {
				if (!kemu) {
					state.options.specializedList = [];
					return;
				}
				let res = await SpecializedListStore.request({
					limit: 9999,
					status: 2,
					carType: 'car',
					tutorKemu: kemu
				}).getData();
				if (res) {
					state.options.specializedList = res.map(item => {
						return {
							...item,
							name: `${item.name}(id：${item.id})`
						};
					});
				}
			},
			open(row?: any, kemu?: any) {
				state.formState = MUtils.deepClone(DefaultFormState);
				state.title = row?.id ? '编辑' : '新增';

				if (row) {
					state.formState = cloneFromPick(row, DefaultFormState);
				}

				state.visible = true;

				methods.initSpecializedList(kemu);
			},
			async onConfirm() {
				await components.formRef.value.validate();

				try {
					state.loading = true;
					const params = cloneByMerge(state.formState, DefaultFormState);
					const fetchStore = state.formState.id ? UpdateStore : CreateStore;

					delete params.bizName;
					if (params.type !== 1) {
						delete params.layer;
					} else if (params.type !== 2) {
						delete params.kemu;
					}

					await fetchStore.request(params).getData();

					emit('refresh');
					state.visible = false;
					MUtils.toast('保存成功', MESSAGE_TYPE.success);
				} finally {
					state.loading = false;
				}
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods
		};
	}
});
</script>
