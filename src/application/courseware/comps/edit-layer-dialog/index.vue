<template>
	<pm-dialog v-model:visible="visible" :title="title" width="480px">
		<template v-if="visible">
			<pm-table
				:columns="tableColumns"
				:store="tableData"
				:use-custom-column="true"
				:pagination="false"
				:operations-fixed="false"
				:operations="false"
				:sort-num="false"
				style="margin-top: 0"
			>
				<template #layer="{ record }">
					<m-form-item label="" name="layer" style="margin: 10px 0; height: 32px">
						<m-select
							v-model:value="record.level"
							:options="options.layerList"
							placeholder="层级"
							:allowClear="true"
						/>
					</m-form-item>
				</template>
			</pm-table>
		</template>

		<template #footer>
			<m-button plain @click="visible = false">取消</m-button>
			<m-button :loading="loading" type="primary" @click="onConfirm">确定</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue';
import { TableColumn, MESSAGE_TYPE, MUtils } from 'admin-library';
import { Store } from '@simplex/simple-store';

import { cloneFromPick, cloneByMerge, mergeByKey } from '@/shard/utils';
import { LAYER_OPTIONS } from '../../constant';
import { UpdateLevelInfoStore } from '../../store';

const DefaultFormState = {
	bizId: null,
	levelInfo: ''
};

const DefaultList = [
	{
		name: '科一',
		level: null,
		kemu: 10
	},
	{
		name: '科四',
		level: null,
		kemu: 20
	},
	{
		name: '扣满12分',
		level: null,
		kemu: 30
	},
	{
		name: '恢复驾驶证',
		level: null,
		kemu: 40
	}
];

export default defineComponent({
	emits: ['refresh'],
	setup(props, { emit }) {
		const components = {};

		const constants = {
			tableColumns: [
				{
					title: '科目',
					dataIndex: 'name'
				},
				{
					title: '层级',
					dataIndex: 'layer',
					xtype: 'custom'
				}
			] as TableColumn[]
		};

		const state = reactive({
			visible: false,
			title: '层级管理',
			loading: false,
			formState: MUtils.deepClone(DefaultFormState),
			options: {
				layerList: LAYER_OPTIONS
			},
			tableData: new Store(MUtils.deepClone(DefaultList))
		});

		const methods = {
			async open(row?: any) {
				state.formState = MUtils.deepClone(DefaultFormState);
				state.tableData = new Store(MUtils.deepClone(DefaultList));
				if (row) {
					state.formState = cloneFromPick(row, DefaultFormState);
					state.tableData = new Store(mergeByKey('kemu', MUtils.deepClone(DefaultList), row.layer));
				}

				state.visible = true;
			},
			async onConfirm() {
				try {
					state.loading = true;
					const params = cloneByMerge(state.formState, DefaultFormState);
					const fetchStore = UpdateLevelInfoStore;
					let level = await state.tableData.getData();
					level = level
						.filter(item => item.level)
						.map(item => {
							const { kemu, level } = item;
							return { kemu, level };
						});
					if (level.length) {
						params.levelInfo = JSON.stringify(level);
					}

					await fetchStore.request(params).getData();

					emit('refresh');
					state.visible = false;
					MUtils.toast('保存成功', MESSAGE_TYPE.success);
				} finally {
					state.loading = false;
				}
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods
		};
	}
});
</script>
