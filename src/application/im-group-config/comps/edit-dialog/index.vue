<template>
	<pm-dialog v-model:visible="visible" :title="title" width="680px">
		<template v-if="visible">
			<m-form
				ref="formRef"
				:model="formState"
				:colon="false"
				:label-col="{ style: { width: '145px' } }"
				autocomplete="off"
			>
				<m-form-item label="业务分类" name="bizType">
					<m-select
						v-model:value="formState.bizType"
						:options="options.typeList"
						placeholder="业务分类"
						disabled
					/>
				</m-form-item>
				<m-form-item label="科目" name="kemu" required>
					<m-select v-model:value="formState.kemu" :options="options.kemuList" placeholder="科目" />
				</m-form-item>
				<m-form-item label="群聊名称">
					<m-input placeholder="例：x月x号驾考宝典科目一快速提分班" disabled />
				</m-form-item>
				<m-form-item label="客服账号" name="imAccountId" required>
					<m-select
						show-search
						optionFilterProp="name"
						v-model:value="formState.imAccountId"
						:options="options.accountOptions"
						:fieldNames="{ label: 'name', value: 'id' }"
					/>
				</m-form-item>
				<m-form-item label="群人数" name="groupCount" required :rules="positiveInteger">
					<m-input-number
						v-model:value="formState.groupCount"
						style="width: 320px"
						placeholder="请输入需要拉入群的学员数（不含水军）"
						:min="3"
						:max="2000"
					/>
				</m-form-item>
				<m-form-item label="拉群方式" name="cycle" required :rules="positiveInteger">
					<m-form-item-rest>
						<m-select :value="'按周期拉群'" disabled style="margin-right: 10px; width: 120px" />
					</m-form-item-rest>
					<m-input-number
						v-model:value="formState.cycle"
						style="width: 320px"
						placeholder="请输入班群持续天数，限正整数"
						:min="1"
					/>
				</m-form-item>
				<m-form-item label="开班时间" name="startTime" required>
					<m-time-picker v-model:value="formState.startTime" format="HH:mm" valueFormat="HH:mm:00" />
				</m-form-item>
				<m-form-item label="结班时间" name="endTime">
					<m-time-picker v-model:value="formState.endTime" format="HH:mm" valueFormat="HH:mm:00" />
				</m-form-item>
				<m-form-item label="班群周期" name="effectiveDay" required :rules="positiveInteger">
					<m-input-number
						v-model:value="formState.effectiveDay"
						style="width: 320px"
						placeholder="请输入班群持续天数，限正整数"
						:min="1"
					/>
				</m-form-item>
				<m-form-item label="机器人模板" name="robotTplId">
					<m-select
						show-search
						optionFilterProp="name"
						v-model:value="formState.robotTplId"
						:options="options.tmplList3"
						:fieldNames="{ label: 'name', value: 'id' }"
					/>
				</m-form-item>
				<m-form-item label="客户跟进消息模板" name="customerTplId">
					<m-select
						show-search
						optionFilterProp="name"
						v-model:value="formState.customerTplId"
						:options="options.tmplList"
						:fieldNames="{ label: 'name', value: 'id' }"
					/>
				</m-form-item>
				<m-form-item label="大班课群发消息模板" name="classTplId" required>
					<m-select
						show-search
						optionFilterProp="name"
						v-model:value="formState.classTplId"
						:options="options.tmplList2"
						:fieldNames="{ label: 'name', value: 'id' }"
					/>
				</m-form-item>
			</m-form>
		</template>

		<template #footer>
			<m-button plain @click="visible = false">取消</m-button>
			<m-button :loading="loading" type="primary" @click="onConfirm">确定</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { MESSAGE_TYPE, MUtils } from 'admin-library';

import { cloneFromPick, cloneByMerge } from '@/shard/utils';
import { positiveInteger } from '@/shard/validate';
import { KEMU_OPTIONS } from '@/shard/constant';
import { BIZTYPE_OPTIONS } from '../../constant';

import { AccountListStore } from '@/application/im-bulk-message/store';
import { ListStore as TmplListStore } from '@/application/im-group-automaiton-tmpl/store';
import { CreateStore, UpdateStore } from '../../store';

const DefaultFormState = {
	id: null,
	bizType: 0,
	kemu: null,
	imAccountId: null,
	groupCount: null,
	cycle: null,
	startTime: '',
	endTime: '',
	effectiveDay: null,
	robotTplId: null,
	customerTplId: null,
	classTplId: null
};

export default defineComponent({
	emits: ['refresh'],
	setup(props, { emit }) {
		const components = {
			formRef: ref(null)
		};

		const constants = {
			positiveInteger
		};

		const state = reactive({
			visible: false,
			title: '',
			loading: false,
			formState: MUtils.deepClone(DefaultFormState),
			options: {
				typeList: BIZTYPE_OPTIONS,
				kemuList: KEMU_OPTIONS,
				accountOptions: [],
				tmplList: [],
				tmplList2: [],
				tmplList3: []
			}
		});

		const methods = {
			open(row?: any) {
				state.formState = MUtils.deepClone(DefaultFormState);
				state.title = row?.id ? '编辑' : '新增';

				if (row) {
					state.formState = cloneFromPick(row, DefaultFormState);
				}

				state.visible = true;

				methods.initAccountOptions();
				methods.initTmplOptions();
			},

			async initAccountOptions() {
				const res = await AccountListStore.request({ limit: 9999, official: true }).getData();
				if (res) {
					state.options.accountOptions = res.map(item => {
						const { name, id } = item;
						return {
							...item,
							name: `${name}(${id})`
						};
					});
				}
			},
			async initTmplOptions() {
				let res = await TmplListStore.request({
					limit: 9999
				}).getData();
				if (res) {
					state.options.tmplList = res.filter(item => item.type === 1);
					state.options.tmplList2 = res.filter(item => item.type === 2);
					state.options.tmplList3 = res.filter(item => item.type === 3);
				}
			},

			async onConfirm() {
				await components.formRef.value.validate();

				try {
					state.loading = true;
					const params = cloneByMerge(state.formState, DefaultFormState);
					const fetchStore = state.formState.id ? UpdateStore : CreateStore;

					await fetchStore.request(params).getData();

					emit('refresh');
					state.visible = false;
					MUtils.toast('保存成功', MESSAGE_TYPE.success);
				} finally {
					state.loading = false;
				}
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods
		};
	}
});
</script>
