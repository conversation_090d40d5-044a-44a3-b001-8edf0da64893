import { TableColumn } from 'admin-library';
import { ColumnWidthEnum, KEMU_MAP } from '@/shard/constant';
import { BIZTYPE_MAP } from './constant';

export const COLUMNS: TableColumn[] = [
	{
		title: '业务分类',
		dataIndex: 'bizType',
		render: data => {
			return BIZTYPE_MAP[data];
		}
	},
	{
		title: '科目',
		dataIndex: 'kemu',
		render: data => {
			return KEMU_MAP[data];
		},
		width: ColumnWidthEnum.TEXT3
	},
	{
		title: '客服账号',
		dataIndex: 'imAccountId',
		width: ColumnWidthEnum.TEXT6
	},
	{
		title: '群人数',
		dataIndex: 'groupCount'
	},
	{
		title: '开班时间',
		dataIndex: 'startTime'
	},
	{
		title: '结班时间',
		dataIndex: 'endTime'
	},
	{
		title: '班群周期',
		dataIndex: 'effectiveDay'
	},
	{
		title: '机器人模板',
		dataIndex: 'robotTplId',
		width: ColumnWidthEnum.TEXT6
	},
	{
		title: '客户跟进消息模板',
		dataIndex: 'customerTplId',
		width: 150
	},
	{
		title: '大班课群发消息模板',
		dataIndex: 'classTplId',
		width: 160
	}
];
