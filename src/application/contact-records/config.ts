import { TableColumn, TableDateFormat } from 'admin-library';
import { renderCopyComp, renderTextWithColor } from '@/shard/utils';
import { ColumnWidthEnum, RADIO_OPTIONS } from '@/shard/constant';
import { TYPE_MAP, WAY_MAP, REMAIN_TYPE_MAP, LAUNCH_TYPE_MAP } from './constant';

export const COLUMNS: TableColumn[] = [
	{
		title: '#',
		dataIndex: 'id',
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '类型',
		dataIndex: 'sceneType',
		render: data => {
			return TYPE_MAP[data];
		},
		width: 200
	},
	{
		title: '提醒方式',
		dataIndex: 'channel',
		render: data => {
			return REMAIN_TYPE_MAP[data];
		},
		width: 200
	},
	{
		title: '呼叫方向',
		dataIndex: 'direction',
		render: data => {
			return WAY_MAP[data];
		},
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '发起人类型',
		dataIndex: 'launchType',
		render: data => {
			return LAUNCH_TYPE_MAP[data];
		},
		width: ColumnWidthEnum.TEXT5
	},
	{
		title: '发起人ID',
		dataIndex: 'launchUserId',
		render: data => {
			if (data && data !== '0') {
				return renderCopyComp(data);
			}
		},
		width: ColumnWidthEnum.TEXT6
	},
	{
		title: '接收人木仓Id',
		dataIndex: 'callUserId',
		render: data => {
			return renderCopyComp(data);
		},
		width: ColumnWidthEnum.TEXT6
	},
	{
		title: '发起时间',
		dataIndex: 'createTime',
		type: 'date',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	},
	{
		title: '业务Id',
		dataIndex: 'bizId'
	},
	{
		title: '是否成功',
		dataIndex: 'answer',
		render: data => {
			return renderTextWithColor(data, RADIO_OPTIONS);
		},
		width: ColumnWidthEnum.TEXT5
	}
];

export const COLUMNS2: TableColumn[] = COLUMNS.filter(item => item.dataIndex !== 'bizId');
