import { getMapfromArray, getStorefromArray } from '@/shard/utils';

export const TYPE_OPTIONS = [
	{
		label: '上课提醒',
		value: 1
	},
	{
		label: '回App提醒-学员',
		value: 2
	},
	{
		label: '回App提醒-讲师',
		value: 3
	},
	{
		label: '上课前1小时提醒',
		value: 4
	},
	{
		label: '填可排课时间提醒',
		value: 5
	},
	{
		label: '发起验收提醒',
		value: 6
	},
	{
		label: '上课前15分钟提醒',
		value: 7
	},
	{
		label: '邀约提醒',
		value: 8
	},
	{
		label: '课程异常终止通知',
		value: 11
	},
	{
		label: '课程接受提醒',
		value: 12
	}
];
export const TYPE_MAP = getMapfromArray(TYPE_OPTIONS);
export const TYPE_STORE = getStorefromArray(TYPE_OPTIONS);

export const REMAIN_TYPE_OPTIONS = [
	{
		label: '短信',
		value: 1
	},
	{
		label: '电话',
		value: 2
	},
	{
		label: '服务号模板消息',
		value: 3
	}
];
export const REMAIN_TYPE_MAP = getMapfromArray(REMAIN_TYPE_OPTIONS);

export const WAY_OPTIONS = [
	{
		label: '呼出',
		value: 1
	},
	{
		label: '呼入',
		value: 2
	}
];
export const WAY_MAP = getMapfromArray(WAY_OPTIONS);
export const WAY_STORE = getStorefromArray(WAY_OPTIONS);

export const LAUNCH_TYPE_OPTIONS = [
	{
		label: '讲师',
		value: 1
	},
	{
		label: '系统',
		value: 2
	},
	{
		label: '管理后台',
		value: 3
	}
];
export const LAUNCH_TYPE_MAP = getMapfromArray(LAUNCH_TYPE_OPTIONS);
export const LAUNCH_TYPE_STORE = getStorefromArray(LAUNCH_TYPE_OPTIONS);
