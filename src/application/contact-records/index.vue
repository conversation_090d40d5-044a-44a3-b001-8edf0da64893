<template>
	<pm-effi :controller="controller">
		<pm-search>
			<pm-search-single
				:span="5"
				:antd-props="{
					placeholder: ['发起时间', '结束时间']
				}"
				xtype="RANGEPICKER"
				data-index="createTimeFrom|createTimeTo"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '呼叫方向'
				}"
				data-index="direction"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '类型'
				}"
				data-index="sceneType"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '接收人木仓Id'
				}"
				data-index="callUserId"
				xtype="INPUT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '是否成功'
				}"
				data-index="answer"
				xtype="SELECT"
			/>
		</pm-search>
		<pm-table
			:columns="COLUMNS"
			:use-custom-column="true"
			:operations="false"
			:sort-num="false"
			:scroll="{
				x: 'max-content'
			}"
		></pm-table>
	</pm-effi>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue';
import { ModelController } from 'admin-library';

import { COLUMNS } from './config';
import { RADIO_STORE } from '@/shard/constant';
import { WAY_STORE, TYPE_STORE } from './constant';
import { ListStore } from './store';

export default defineComponent({
	setup() {
		const components = {};

		const constants = {
			COLUMNS
		};

		const state = reactive({});

		const controller = new ModelController({
			table: {
				store: ListStore
			},
			search: {
				direction: {
					store: WAY_STORE
				},
				answer: {
					store: RADIO_STORE
				},
				sceneType: {
					store: TYPE_STORE
				}
			}
		});
		controller.tableRequest();

		const methods = {};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			controller
		};
	}
});
</script>
