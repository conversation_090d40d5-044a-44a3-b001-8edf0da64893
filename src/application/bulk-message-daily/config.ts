import { TableColumn, ColumnXtype, TableDateFormat } from 'admin-library';
import { ColumnWidthEnum } from '@/shard/constant';
import { RECEIVER_TYPE_MAP } from '@/application/bulk-message/constant';
import { STATUS_MAP } from './constant';

export const COLUMNS: TableColumn[] = [
	{
		title: '#',
		dataIndex: 'id',
		fixed: 'left'
	},
	{
		title: '日期',
		dataIndex: 'sendDate',
		width: ColumnWidthEnum.DATEDAY,
		fixed: 'left'
	},
	{
		title: '员工姓名',
		dataIndex: 'employeeName',
		fixed: 'left'
	},
	{
		title: '会话类型',
		dataIndex: 'contactType',
		render: data => {
			return RECEIVER_TYPE_MAP[data];
		},
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '接收对象',
		dataIndex: 'type',
		xtype: ColumnXtype.CUSTOM,
	},
	{
		title: '任务状态',
		dataIndex: 'status',
		render: data => {
			return STATUS_MAP[data];
		},
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '更新时间',
		dataIndex: 'updateTime',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	},
	{
		title: '操作人',
		dataIndex: 'updateUserName',
		width: ColumnWidthEnum.TEXT4
	}
];
