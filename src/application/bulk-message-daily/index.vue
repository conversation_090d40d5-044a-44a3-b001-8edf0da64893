<template>
	<pm-effi :controller="controller">
		<pm-search>
			<pm-search-single
				:span="3"
				show-search
				optionFilterProp="employeeMixName"
				:antdProps="{
					placeholder: '销售',
					fieldNames: { label: 'employeeMixName', value: 'id' }
				}"
				data-index="employeeId"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '任务状态'
				}"
				data-index="status"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '任务生成日期'
				}"
				xtype="DATEPICKER"
				data-index="sendDate"
			/>
		</pm-search>
		<pm-table
			:columns="COLUMNS"
			:use-custom-column="true"
			:operations-fixed="true"
			:operations-width="130"
			:sort-num="false"
			:scroll="{
				x: 'max-content'
			}"
			:antd-props="{
				customRow: record => {
					return {
						onClick: () => {
							onClick(record);
						}
					};
				}
			}"
			:rowClassName="setInstallClassName"
		>
			<template #type="{ record }">
				<m-button type="link" @click="onViewContact(record)">{{ TYPE_MAP[record.type] }}</m-button>
			</template>
			<template #operations="{ record }">
				<m-button v-if="record.status === 1" type="link" @click="onEdit(record)">去群发</m-button>
				<m-button v-if="record.status === 2" type="link" @click="onEdit(record)">编辑</m-button>
				<m-button v-if="record.status === 2" type="link" @click="onLaunch(record)">发送</m-button>
				<m-button v-if="record.status === 3 || record.status === 4" type="link" @click="toMessageList(record)">
					查看结果
				</m-button>
			</template>
		</pm-table>
	</pm-effi>

	<edit-comp ref="editRef" @refresh="onRefresh"></edit-comp>
	<view-contact-comp ref="viewContactRef"></view-contact-comp>
</template>

<script lang="ts">
import Dayjs from 'dayjs';
import { defineComponent, ref, reactive, toRefs } from 'vue';
import { MESSAGE_TYPE, MUtils, ModelController, PaasPostMessage, YEAR_TIME_FORMAT } from 'admin-library';

import EditComp from './comps/edit-dialog/index.vue';
import ViewContactComp from './comps/view-contact-dialog/index.vue';
import { confirmMessageBox } from '@/shard/utils';
import { COLUMNS } from './config';
import { STATUS_STORE, TYPE_MAP } from './constant';
import { ListStore as EmployeeListStore } from '@/application/promoter/store';
import { ListStore, PublishStore } from './store';
import { ItemResponse } from './types';

const today = (date): any => {
	const start = new Date(date);
	start.setHours(0, 0, 0, 0);

	const end = new Date(date);
	end.setHours(23, 59, 59, 999);

	return [start, end];
};

export default defineComponent({
	components: {
		EditComp,
		ViewContactComp
	},
	setup() {
		const components = {
			editRef: ref<InstanceType<typeof EditComp>>(),
			viewContactRef: ref<InstanceType<typeof ViewContactComp>>()
		};

		const constants = {
			COLUMNS,
			TYPE_MAP
		};

		const state = reactive({
			selectedId: ''
		});

		const controller = new ModelController({
			table: {
				store: ListStore
			},
			search: {
				employeeId: {
					store: EmployeeListStore
				},
				status: {
					store: STATUS_STORE
				}
			}
		});
		controller.tableRequest();

		controller.search.employeeId.onRequest.use(params => {
			params = {
				...params,
				station: 1,
				limit: 9999
			};

			return params;
		});

		controller.table.onRequest.use(params => {
			let sd = {};
			if (params.sendDate) {
				sd = { sendDate: Dayjs(params.sendDate).format(YEAR_TIME_FORMAT) };
			}

			params = {
				...params,
				...sd
			};

			return params;
		});

		const methods = {
			onClick(recored) {
				state.selectedId = recored.id;
			},
			setInstallClassName(record) {
				if (record.id === state.selectedId) {
					return 'selected-row';
				}
			},
			async onLaunch(row: ItemResponse) {
				await confirmMessageBox('确认发起任务吗？');

				await PublishStore.request({ id: row.id }).getData();
				methods.onRefresh();
				MUtils.toast('发起任务成功', MESSAGE_TYPE.success);
			},
			toMessageList(row: ItemResponse) {
				let { type, sendDate } = row;
				let subBizType;
				if (type === 1) {
					subBizType = 5;
				} else if (type === 2) {
					subBizType = 6;
				}
				let [sendTimeBegin, sendTimeEnd] = today(sendDate);
				sendTimeBegin = +new Date(sendTimeBegin);
				sendTimeEnd = +new Date(sendTimeEnd);
				PaasPostMessage.post('navigation.to', '/#/bulk-message', {
					title: `${row.sendDate}-${row.employeeName}`,
					query: {
						promoterNo: row?.employeeNo,
						sendTimeBegin,
						sendTimeEnd,
						bizType: 6,
						subBizType
					},
					target: '_tab'
				});
			},

			async onEdit(row: ItemResponse) {
				components.editRef.value.open({ ...row, sendContactIds: row.sendContactIds || row.contactIds });
			},
			onViewContact(row: ItemResponse) {
				components.viewContactRef.value.open(row);
			},

			onRefresh() {
				controller.tableRequest();
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			controller
		};
	}
});
</script>
