<template>
	<pm-dialog
		v-model:visible="visible"
		:mask="false"
		title="客户列表"
		width="680px"
		style="height: calc(100vh - 64px)"
		class="sjsj"
	>
		<div v-if="visible" class="int">
			<div class="sh">
				<DynamicScroller
					class="recycle"
					:items="
						contactOptions
					"
					:min-item-size="34"
					key-field="contactId"
				>
					<template #="{ item, index, active }">
						<DynamicScrollerItem
							:item="item"
							:active="active"
							:size-dependencies="[item.tagIds]"
							:data-index="index"
						>
							<div class="wrapper">
								<span class="ant-cont">
									<span>
										<span>{{ item.contactMixName }}</span>
										<span class="tag" v-for="tag in item.tagIds" :key="tag.tagId">
											{{ tag.tagName }}
										</span>
									</span>
									<span>{{ formatDate(item.addTime, 'MM-DD') }}</span>
								</span>
							</div>
						</DynamicScrollerItem>
					</template>
				</DynamicScroller>
			</div>
		</div>

		<template #footer>
			<m-button type="primary" @click="onConfirm">确定</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import Dayjs from 'dayjs';
import { defineComponent, reactive, toRefs } from 'vue';
import { DynamicScroller, DynamicScrollerItem } from 'vue-virtual-scroller';
import 'vue-virtual-scroller/dist/vue-virtual-scroller.css';

import { ContactListStore, TagListStore } from '@/application/bulk-message/store';

function groupBy(list, key, label) {
	const newArr = [];
	list.forEach(item => {
		const value = item[key];
		let obj = newArr.find(item => item[key] === value);

		if (!obj) {
			obj = {
				[key]: item[key],
				[label]: item[label],
				dataList: []
			};
			newArr.push(obj);
		}
		obj.dataList.push(item);
	});
	return newArr;
}

let tagGroup = [];
let tagList = [];

export default defineComponent({
	components: {
		DynamicScroller,
		DynamicScrollerItem,
	},
	emits: ['refresh'],
	setup() {
		const components = {
		};

		const constants = {
		};

		const state = reactive({
			visible: false,
			contactOptions: [],
			wecomUserId: '',
			contactPool: [],
			tagList: [],
			tagGroup: [],
		});

		const methods = {
			formatDate(date, template) {
				return Dayjs(date).format(template);
			},
			open(row?: any) {
				state.wecomUserId = row.wecomUserId
				state.contactPool = row.contactIds.split(',')

				methods.initContactOptions();

				state.visible = true;
			},
			async initContactOptions() {
				let res = await ContactListStore.request({
					ownerId: state.wecomUserId,
					contactType: 1,
					limit: 9999
				}).getData();
				
				if (state.contactPool && state.contactPool.length) {
					res = res.filter(item => state.contactPool.indexOf(item.contactId) > -1)
				}
				if (state.tagList.length === 0) {
					await methods.initTagGroup();
				}
				res = res.map(item => {
					let { contactMixName, contactId, addTime, tagIds } = item;
					tagIds = tagIds.split(',').filter(item => item);
					tagIds = tagIds.map(item => {
						return state.tagList.find(sItem => sItem.tagId === item);
					});
					tagIds = tagIds
						.filter(item => item)
						.map(({ tagName, tagId }) => {
							return { tagName, tagId };
						});

					return {
						contactMixName,
						contactId,
						addTime,
						tagIds
					};
				});
				state.contactOptions = res
			},
			async initTagGroup() {
				if (tagList.length) {
					state.tagList = tagList;
					state.tagGroup = tagGroup;
					return;
				}
				const res = await TagListStore.request({ limit: 9999 }).getData();
				state.tagList = tagList = res;
				state.tagGroup = tagGroup = groupBy(res, 'groupId', 'groupName');
			},

			async onConfirm() {
				state.visible = false
				state.contactOptions = []
				state.wecomUserId = ''
				state.contactPool = []
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
		};
	}
});
</script>
<style lang="less">
.recycle {
	overflow-x: hidden;
	overflow-y: auto;
	max-height: 100%;
}
.sjsj {
	.ant-modal-content {
		overflow: hidden;
		height: 100%;
	}
	.ant-modal-body {
		overflow: hidden;
		height: calc(100% - 118px);
	}
	.m-dialog-body {
		overflow: hidden;
		height: 100%;
	}
	.int {
		display: flex;
		flex-direction: column;
		height: 100%;
	}
}
</style>
<style lang="less" scoped>
.sh {
	flex: 1;
	height: 0;
}
.wrapper {
	display: flex;
	padding: 2px 0;
}
.ant-cont {
	display: flex;
	justify-content: space-between;
	padding: 4px 8px;
	background-color: #f7f7f7;
	flex: 1;
}
.tag {
	display: inline-block;
	padding: 0 4px;
	margin-left: 8px;
	text-align: center;
	color: #0081ff;
	background-color: #c5e8ff;
}
</style>