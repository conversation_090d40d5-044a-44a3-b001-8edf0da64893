<template>
	<pm-dialog v-model:visible="visible" :title="title" width="680px">
		<template v-if="visible">
			<m-form
				ref="formRef"
				:model="formState"
				:colon="false"
				:label-col="{ style: { width: '95px' } }"
				autocomplete="off"
			>
				<m-form-item label="企微账号" name="employeeId">
					<m-select
						show-search
						optionFilterProp="employeeMixName"
						v-model:value="formState.employeeId"
						:options="options.employeeOptions"
						:fieldNames="{ label: 'employeeMixName', value: 'id' }"
						disabled
					/>
				</m-form-item>
				<m-form-item label="群发会话类型" name="receiverType">
					<m-select v-model:value="formState.receiverType" :options="options.receiverType" disabled />
				</m-form-item>
				<m-form-item label="群发对象" name="sendContactIds" required>
					<CustomerPickComp
						:wecomUserId="wecomUserId"
						:contactPool="contactIds"
						v-model:value="formState.sendContactIds"
					/>
				</m-form-item>
				<m-form-item label="内容配置">
					<div
						v-for="(item, index) in formState.contents"
						:key="item.uid"
						style="margin-bottom: 10px; background-color: #f2f2f2"
					>
						<m-row justify="space-between" align="middle" style="padding: 8px 8px 10px">
							<m-col></m-col>
							<m-col>
								<div style="font-size: 14px; line-height: 1.6">消息{{ index + 1 }}</div>
							</m-col>
							<m-col>
								<m-button
									v-if="formState.contents.length > 1"
									danger
									type="link"
									@click="removeItem(index)"
								>
									删除
								</m-button>
							</m-col>
						</m-row>
						<m-divider style="margin: 0 0 10px; height: 2px; background-color: #fff" />
						<div style="padding: 10px 20px 1px; margin-bottom: 10px">
							<div style="margin-bottom: 10px">
								<div>推送时间点</div>
								<div>
									<m-form-item
										:name="['contents', index, 'sendTime']"
										label="推送时间点"
										:labelCol="{ span: 0 }"
										required
									>
										<m-time-picker
											v-model:value="item.sendTime"
											format="HH:mm"
											valueFormat="HH:mm"
										/>
									</m-form-item>
								</div>
							</div>
							<div>
								<div>推送内容</div>
								<div>
									<messagegGoupComp
										:key="formState.receiverType"
										ref="messageRef"
										v-model:value="item.messageData"
										:receiverType="formState.receiverType"
									/>
								</div>
							</div>
						</div>
					</div>
					<m-button style="margin-top: 0; margin-bottom: 8px; width: 100%" type="dashed" @click="addItem">
						<template #icon><PlusOutlined /></template>
						继续添加
					</m-button>
				</m-form-item>
			</m-form>
		</template>

		<template #footer>
			<m-button plain @click="visible = false">取消</m-button>
			<m-button :loading="loading" type="primary" @click="onConfirm">确定</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import Dayjs from 'dayjs';
import { defineComponent, reactive, toRefs, ref, computed } from 'vue';
import { MESSAGE_TYPE, MUtils } from 'admin-library';
import { PlusOutlined } from '@ant-design/icons-vue';
import getUid from 'ant-design-vue/es/vc-upload/uid';
import { isArray } from 'lodash';

import { cloneFromPick, cloneByMerge, formatDate } from '@/shard/utils';
import CustomerPickComp from '@/application/bulk-message/comps/edit-dialog/customer-picker.vue';
import messagegGoupComp from '@/application/bulk-message/comps/edit-dialog/message-group.vue';
import { RECEIVER_TYPE_OPTIONS } from '@/application/bulk-message/constant';
import { ListStore as EmployeeListStore } from '@/application/promoter/store';
import { UpdateStore } from '../../store';

const DefaultFormState = {
	id: null,
	employeeId: '',
	receiverType: 1,
	sendContactIds: [],
	contents: [
		{
			sendTime: '',
			messageData: {
				messageType: 1,
				messageContent: {}
			}
		}
	]
};

export default defineComponent({
	components: {
		messagegGoupComp,
		PlusOutlined,
		CustomerPickComp
	},
	emits: ['refresh'],
	setup(props, { emit }) {
		const components = {
			formRef: ref(null),
			messageRef: ref(null)
		};

		const constants = {};

		const state = reactive({
			visible: false,
			title: '每日任务',
			loading: false,
			formState: MUtils.deepClone(DefaultFormState),
			options: {
				employeeOptions: [],
				receiverType: RECEIVER_TYPE_OPTIONS
			},
			contactIds: [],
			sendDate: ''
		});

		const wecomUserId = computed(() => {
			const { employeeId } = state.formState;
			let employee = state.options.employeeOptions.find(item => {
				return item.id === employeeId;
			});
			return employee?.wecomUserId;
		});

		const getValidate = () => {
			let list = [];

			if (isArray(components.messageRef.value)) {
				list = components.messageRef.value.map(item => item.validate());
			} else {
				list = [components.messageRef.value.validate()];
			}
			return list;
		};

		const methods = {
			removeItem(index) {
				state.formState.contents.splice(index, 1);
			},
			async addItem() {
				await Promise.all(getValidate());

				state.formState.contents.push({
					sendTime: '',
					messageData: {
						messageType: 1,
						messageContent: {}
					},
					uid: getUid()
				});
			},
			open(row?: any) {
				state.formState = MUtils.deepClone(DefaultFormState);

				if (row) {
					state.contactIds = row.contactIds.split(',');
					state.sendDate = row.sendDate;

					const sendContactIds = row.sendContactIds?.split(',') || DefaultFormState.sendContactIds;
					state.formState = cloneFromPick({ ...row, sendContactIds }, DefaultFormState);

					if (row?.contents) {
						let { contents } = row;
						contents = JSON.parse(contents || '[]');
						contents = contents.map(item => {
							let { messageType, messageContent, sendTime, ...reset } = item;
							if (sendTime) {
								sendTime = formatDate(sendTime, 'HH:mm');
							}
							let messageData = { messageType, messageContent };

							return { ...reset, messageData, sendTime, uid: getUid() };
						});
						state.formState.contents = contents;
					} else {
						state.formState.contents = MUtils.deepClone(DefaultFormState.contents);
					}
				}

				methods.initEmployeeOptions();

				state.visible = true;
			},
			async initEmployeeOptions() {
				const res = await EmployeeListStore.request({
					enableAutomaticHosting: true,
					limit: 9999,
					status: 20
				}).getData();
				if (res) {
					state.options.employeeOptions = res;
				}
			},

			async onConfirm() {
				await Promise.all([components.formRef.value.validate(), ...getValidate()]);

				try {
					state.loading = true;
					const params = cloneByMerge(state.formState, DefaultFormState);
					const fetchStore = UpdateStore;

					let { contents } = params;
					if (contents) {
						contents = contents.map(item => {
							let { messageData, sendTime } = item;
							if (sendTime) {
								sendTime = Dayjs(`${state.sendDate} ${sendTime}`).valueOf();
							}
							delete item.messageData;
							return {
								...item,
								sendTime,
								...messageData
							};
						});
						params.contents = JSON.stringify(contents);
					}

					await fetchStore.request(params).getData();

					emit('refresh');
					state.visible = false;
					MUtils.toast('保存成功', MESSAGE_TYPE.success);
				} finally {
					state.loading = false;
				}
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			wecomUserId
		};
	}
});
</script>
