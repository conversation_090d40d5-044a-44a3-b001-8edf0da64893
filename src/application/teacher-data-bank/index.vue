<template>
	<pm-effi :controller="controller">
		<pm-search>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '资料类型'
				}"
				data-index="type"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '是否必看'
				}"
				data-index="mustSee"
				xtype="SELECT"
			/>
		</pm-search>

		<pm-table
			:columns="COLUMNS"
			:use-custom-column="true"
			:operations="true"
			:sort-num="false"
			:scroll="{
				x: 'max-content'
			}"
		>
			<template #beforeCustom>
				<m-button type="primary" @click="onCreate">新增</m-button>
				<m-button type="primary" @click="onGroupControl">分组管理</m-button>
			</template>
			<template #operations="{ record }">
				<m-button type="link" @click="onEdit(record)">编辑</m-button>
				<m-button type="link" danger @click="onDel(record)">删除</m-button>
			</template>
		</pm-table>
	</pm-effi>
	<CreateDialog ref="createRef" @refresh="onRefresh"></CreateDialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { ModelController, PaasPostMessage, MESSAGE_TYPE, MUtils } from 'admin-library';

import CreateDialog from './comps/edit-dialog/index.vue';
import { confirmMessageBox } from '@/shard/utils';
import { COLUMNS } from './config';
import { RADIO_STORE } from '@/shard/constant';
import { TYPE_STORE } from './constant';
import { ListStore, DelStore } from './store';
import { ItemResponse } from './types';

export default defineComponent({
	components: {
		CreateDialog
	},

	setup() {
		const components = {
			createRef: ref<InstanceType<typeof CreateDialog>>()
		};

		const constants = {
			COLUMNS
		};

		const state = reactive({});

		const controller = new ModelController({
			table: {
				store: ListStore
			},
			search: {
				type: {
					store: TYPE_STORE
				},
				mustSee: {
					store: RADIO_STORE
				}
			}
		});
		controller.tableRequest();

		const methods = {
			onEdit(record: ItemResponse) {
				components.createRef.value?.open(record);
			},
			onCreate() {
				components.createRef.value?.open();
			},
			async onDel(row: ItemResponse) {
				await confirmMessageBox('确认删除这个资料吗？');
				await DelStore.request({ id: row.id }).getData();
				methods.onRefresh();
				MUtils.toast('删除成功', MESSAGE_TYPE.success);
			},
			onRefresh() {
				controller.tableRequest();
			},
			onGroupControl() {
				PaasPostMessage.post('navigation.to', '/#/group-control', {
					title: '分组管理',
					extendData: {
						style: 'width: 780px; height: 90%; margin: 2% auto 0;'
					},
					target: '_popup'
				});
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			controller
		};
	}
});
</script>
