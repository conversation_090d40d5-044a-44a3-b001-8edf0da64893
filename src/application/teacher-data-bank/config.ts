import { TableColumn, TableDateFormat } from 'admin-library';
import { ColumnWidthEnum, RADIO_MAP } from '@/shard/constant';
import { VISIBLE_RANGE_MAP, TYPE_MAP } from './constant';

export const COLUMNS: TableColumn[] = [
	{
		title: '#',
		dataIndex: 'id',
		fixed: 'left'
	},
	{
		title: '分组',
		dataIndex: 'groupName',
		fixed: 'left'
	},
	{
		title: '类型',
		dataIndex: 'type',
		render: data => {
			return TYPE_MAP[data];
		},
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '资料名称',
		dataIndex: 'name'
	},
	{
		title: '可见范围',
		dataIndex: 'scope',
		width: ColumnWidthEnum.TEXT4,
		render: data => {
			return VISIBLE_RANGE_MAP[data];
		}
	},
	{
		title: '是否必看',
		dataIndex: 'mustSee',
		width: ColumnWidthEnum.TEXT4,
		render: data => {
			return RADIO_MAP[data];
		}
	},
	{
		title: '是否可下载',
		dataIndex: 'canDownload',
		width: ColumnWidthEnum.TEXT5,
		render: data => {
			return RADIO_MAP[data];
		}
	},
	{
		title: '创建人',
		dataIndex: 'createUserName',
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '创建时间',
		dataIndex: 'createTime',
		type: 'date',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	},
	{
		title: '更新人',
		dataIndex: 'updateUserName',
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '更新时间',
		dataIndex: 'updateTime',
		type: 'date',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	}
];
