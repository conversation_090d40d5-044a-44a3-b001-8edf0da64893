<template>
	<pm-dialog v-model:visible="visible" :title="title" width="980px">
		<template v-if="visible">
			<m-form
				ref="formRef"
				:model="formState"
				:colon="false"
				:label-col="{ style: { width: '95px' } }"
				autocomplete="off"
			>
				<m-form-item label="分组" name="groupId" required>
					<m-select
						v-model:value="formState.groupId"
						:options="options.groupList"
						:fieldNames="{ label: 'groupName', value: 'id' }"
						placeholder="选择分组"
					/>
				</m-form-item>
				<m-form-item label="类型" name="type" required>
					<m-radio-group
						v-model:value="formState.type"
						@change="changeRadio"
						:options="options.typeList"
						:disabled="formState.id"
					></m-radio-group>
				</m-form-item>
				<m-form-item label="资料名称" name="name" required>
					<m-input v-model:value="formState.name"></m-input>
				</m-form-item>
				<m-form-item label="文件" name="resourceEncode" v-if="formState.type === 1" required>
					<fileUploadComp
						fileType="file"
						v-model:value="formState.resourceEncode"
						:antdProps="{
							accept: '.pdf'
						}"
					/>
				</m-form-item>
				<m-form-item label=" " v-else-if="formState.type === 2">
					<WangEdit ref="WangEditRef" v-model:value="formState.content"></WangEdit>
				</m-form-item>
				<m-form-item label="视频地址" name="content" v-else-if="formState.type === 3" required>
					<m-textarea v-model:value="formState.content" placeholder="视频地址" />
				</m-form-item>
				<m-form-item label="是否必看" name="mustSee" required>
					<m-radio-group v-model:value="formState.mustSee" :options="options.radioOptions"></m-radio-group>
				</m-form-item>
				<m-form-item label="是否可下载" name="canDownload" v-if="formState.type === 1" required>
					<m-radio-group
						v-model:value="formState.canDownload"
						:options="options.radioOptions"
					></m-radio-group>
				</m-form-item>
				<m-form-item label="可见范围" name="scope" required>
					<m-radio-group v-model:value="formState.scope" :options="options.scopeList"></m-radio-group>
				</m-form-item>
				<m-form-item label="上岗状态" name="allowWatchStationStatus" v-if="formState.scope === 2" required>
					<m-checkbox-group
						v-model:value="formState.allowWatchStationStatus"
						:options="options.allowWatchStationStatusList"
					></m-checkbox-group>
				</m-form-item>
			</m-form>
		</template>

		<template #footer>
			<m-button plain @click="visible = false">取消</m-button>
			<m-button :loading="loading" type="primary" @click="onConfirm">确定</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { MESSAGE_TYPE, MUtils } from 'admin-library';

import fileUploadComp from '@/comp/file-upload/index.vue';
import WangEdit from '@/comp/wang-editor/index.vue';
import { cloneFromPick, cloneByMerge } from '@/shard/utils';
import { STATUS_OPTIONS } from '@/application/promoter/constant';
import { RADIO_OPTIONS } from '@/shard/constant';
import { TYPE_OPTIONS, VISIBLE_RANGE_OPTIONS } from '../../constant';
import { CreateStore, UpdateStore, GroupListStore } from '../../store';

const DefaultFormState = {
	id: null,
	groupId: '',
	type: '',
	content: '',
	name: '',
	mustSee: '',
	canDownload: '',
	scope: '',
	allowWatchStationStatus: [],
	resourceEncode: ''
};

export default defineComponent({
	emits: ['refresh'],
	components: {
		fileUploadComp,
		WangEdit
	},
	setup(props, { emit }) {
		const components = {
			formRef: ref(null),
			WangEditRef: ref<InstanceType<typeof WangEdit>>()
		};

		const constants = {};

		const state = reactive({
			visible: false,
			title: '',
			loading: false,
			formState: MUtils.deepClone(DefaultFormState),
			options: {
				groupList: [],
				radioOptions: RADIO_OPTIONS,
				typeList: TYPE_OPTIONS,
				scopeList: VISIBLE_RANGE_OPTIONS,
				allowWatchStationStatusList: STATUS_OPTIONS.filter(el => el.value !== 30)
			}
		});

		const methods = {
			async open(row?: any) {
				state.formState = MUtils.deepClone(DefaultFormState);
				state.title = row?.id ? '编辑' : '新增';

				if (row) {
					state.formState = cloneFromPick(row, DefaultFormState);

					if (row.type === 1) {
						state.formState.resourceEncode = {
							encodedData: row.fileKey,
							showUrl: row.content
						};
					}
				}

				state.visible = true;

				methods.initGroupOptions();
			},
			async initGroupOptions() {
				const res = await GroupListStore.request({ limit: 9999 }).getData();
				if (res) {
					state.options.groupList = res;
				}
			},
			async onConfirm() {
				await components.formRef.value.validate();

				try {
					state.loading = true;
					const params = cloneByMerge(state.formState, DefaultFormState);
					const fetchStore = state.formState.id ? UpdateStore : CreateStore;

					params.fileKey = params.resourceEncode?.encodedData;
					delete params.resourceEncode;

					if (params.type === 1) {
						delete params.content;
					} else {
						delete params.canDownload;
					}

					await fetchStore.request(params).getData();

					emit('refresh');
					state.visible = false;
					MUtils.toast('保存成功', MESSAGE_TYPE.success);
				} finally {
					state.loading = false;
				}
			},
			changeRadio() {
				state.formState.content = '';
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods
		};
	}
});
</script>
