import {
	List,
	BatchCreate,
	UpdateTime,
	Cancel,
	UpdateLecturer,
	GetVideo,
	Inspect,
	UpdatePlaybackVisible,
	StudentList,
	Submit,
	Abend,
	ViewVideoMachineAudit,
	GetInspectionInfo,
	UpdateInspectionInfo,
	SuggestContinue,
	TeachingRoomList,
	GetRoomPullStream,
	TrainAdminAuth,
	StopTeach,
	GetRoomInfo,
	UpdateCarTypeAndKemu,
	RecommendSprint,
	CancelRecommendSprint,
	GetChatList
} from '@/store/courses';
import { ItemResponse } from './types';

export const ListStore = new List<Array<ItemResponse>>({});
export const BatchCreateStore = new BatchCreate({});
export const UpdateTimeStore = new UpdateTime({});
export const CancelStore = new Cancel({});
export const UpdateLecturerStore = new UpdateLecturer({});
export const GetVideoStore = new GetVideo({});
export const InspectStore = new Inspect({});
export const UpdatePlaybackVisibleStore = new UpdatePlaybackVisible({});
export const StudentListStore = new StudentList<Array<ItemResponse>>({});
export const SubmitStore = new Submit({});
export const AbendStore = new Abend({});
export const ViewVideoMachineAuditStore = new ViewVideoMachineAudit<ItemResponse>({});

export const GetInspectionInfoStore = new GetInspectionInfo<ItemResponse>({});
export const UpdateInspectionInfoStore = new UpdateInspectionInfo({});

export const SuggestContinueStore = new SuggestContinue({});

export const TeachingRoomListStore = new TeachingRoomList<Array<ItemResponse>>({});
export const GetRoomPullStreamStore = new GetRoomPullStream<Array<ItemResponse>>({});
export const TrainAdminAuthStore = new TrainAdminAuth({});
export const StopTeachStore = new StopTeach({});
export const GetRoomInfoStore = new GetRoomInfo({});

export const UpdateCarTypeAndKemuStore = new UpdateCarTypeAndKemu({});

export const RecommendSprintStore = new RecommendSprint({});
export const CancelRecommendSprintStore = new CancelRecommendSprint({});

export const GetChatListStore = new GetChatList<ItemResponse>({});
