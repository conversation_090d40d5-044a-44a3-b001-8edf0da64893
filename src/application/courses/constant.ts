import { getMapfromArray, getStorefromArray } from '@/shard/utils';
import { ColorEnum } from '@/shard/constant';

export const STATUS_OPTIONS = [
	{
		label: '草稿',
		value: 1,
		styleclass: ColorEnum.info
	},
	{
		label: '待接受',
		value: 2,
		styleclass: ColorEnum.warning
	},
	{
		label: '已接受',
		value: 3,
		styleclass: ColorEnum.geekblue
	},
	{
		label: '待第三方任务库审核',
		value: 4,
		styleclass: ColorEnum.purple
	},
	{
		label: '第三方审核驳回',
		value: 5,
		styleclass: ColorEnum.orange
	},
	{
		label: '待我方验收',
		value: 6,
		styleclass: ColorEnum.cyan
	},
	{
		label: '验收驳回',
		value: 7,
		styleclass: ColorEnum.danger
	},
	{
		label: '验收通过',
		value: 8,
		styleclass: ColorEnum.success
	},
	{
		label: '已废弃',
		value: 9,
		styleclass: ColorEnum.info
	},
	{
		label: '异常终止',
		value: 10,
		styleclass: ColorEnum.volcano
	}
];
export const STATUS_MAP = getMapfromArray(STATUS_OPTIONS);
export const STATUS_STORE = getStorefromArray(STATUS_OPTIONS);

export const ABEND_STATUS_OPTIONS = [
	{
		label: '学员提前请假',
		value: 1,
		styleclass: ColorEnum.blue
	},
	{
		label: '学员临时爽约',
		value: 2,
		styleclass: ColorEnum.cyan
	},
	{
		label: '讲师提前请假',
		value: 3,
		styleclass: ColorEnum.purple
	},
	{
		label: '讲师临时爽约',
		value: 4,
		styleclass: ColorEnum.geekblue
	},
	{
		label: '督导终止课程',
		value: 5,
		styleclass: ColorEnum.magenta
	}
];
export const ABEND_STATUS_MAP = getMapfromArray(ABEND_STATUS_OPTIONS);
export const ABEND_STATUS_STORE = getStorefromArray(ABEND_STATUS_OPTIONS);

export const BEFORE_HOURS_OPTIONS = [
	{
		label: '开课前3小时',
		value: 3
	},
	{
		label: '开课前6小时',
		value: 6
	},
	{
		label: '开课前12小时',
		value: 12
	},
	{
		label: '开课前18小时',
		value: 18
	},
	{
		label: '开课前24小时',
		value: 24
	}
];

export const MACHINE_AUDIT_STATUS_OPTIONS = [
	{
		label: '未机审',
		value: 0
	},
	{
		label: '机审中',
		value: 10
	},
	{
		label: '机审通过',
		value: 21
	},
	{
		label: '机审驳回',
		value: 22
	},
	{
		label: '机审异常',
		value: 100
	}
];
export const MACHINE_AUDIT_STATUS_MAP = getMapfromArray(MACHINE_AUDIT_STATUS_OPTIONS);
export const MACHINE_AUDIT_STATUS_STORE = getStorefromArray(MACHINE_AUDIT_STATUS_OPTIONS);

export const USER_TYPE_OPTIONS = [
	{
		label: '学员',
		value: 1
	},
	{
		label: '老师',
		value: 2
	},
	{
		label: '官方人员',
		value: 3
	}
];
export const USER_TYPE_MAP = getMapfromArray(USER_TYPE_OPTIONS);
