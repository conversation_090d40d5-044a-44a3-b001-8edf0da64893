<template>
	<pm-effi :controller="controller">
		<pm-table
			:columns="COLUMNS"
			:use-custom-column="true"
			:pagination="false"
			:operations="false"
			:sort-num="false"
		>
			<template #pot="{ record }">
				<div v-if="record.type === 1">{{ record.bizValue }}</div>
				<div v-else-if="record.type === 2">{{ record.bizName }}</div>
			</template>
		</pm-table>
	</pm-effi>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue';
import { ModelController } from 'admin-library';

import { COLUMNS } from '@/popup-pages/course-exercise/config';
import { ListStore } from '@/popup-pages/course-exercise/store';

export default defineComponent({
	props: {
		id: {
			type: Number,
			isRequired: true
		}
	},
	setup(props) {
		const constants = {
			COLUMNS
		};

		const state = reactive({});

		const controller = new ModelController({
			table: {
				store: ListStore
			}
		});
		controller.tableRequest();

		controller.table.onRequest.use(params => {
			params = {
				...params,
				limit: 9999,
				courseScheduleId: props.id
			};

			return params;
		});

		return {
			...toRefs(state),
			...constants,
			controller
		};
	}
});
</script>
