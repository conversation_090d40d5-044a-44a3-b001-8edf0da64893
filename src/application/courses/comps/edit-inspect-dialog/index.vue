<template>
	<pm-dialog v-model:visible="visible" :title="title" width="980px">
		<template v-if="visible">
			<m-form
				ref="formRef"
				:model="formState"
				:colon="false"
				:label-col="{ style: { width: '95px' } }"
				autocomplete="off"
			>
				<m-form-item label="上课视频">
					<m-row type="flex" style="justify-content: space-between">
						<m-col>
							<div v-if="playbackUrl">
								<a :href="playbackUrl" target="_blank">视频1</a>
							</div>
							<!-- <video
                                v-if="playbackUrl"
                                :src="playbackUrl"
                                style="max-width: 100%; max-height: 400px;"
                                controls
                            ></video> -->
							<template v-else>视频转码中</template>
						</m-col>
						<m-col>
							机审状态
							<template v-if="machineAuditStatus === 0">
								{{ MACHINE_AUDIT_STATUS_MAP[machineAuditStatus] }}
							</template>
							<m-button type="link" v-else @click="$emit('openMachineAudit', { id: formState.id })">
								{{ MACHINE_AUDIT_STATUS_MAP[machineAuditStatus] }}
							</m-button>
						</m-col>
						<m-col>
							<m-button type="link" @click="goMockPage">模考记录</m-button>
						</m-col>
					</m-row>
				</m-form-item>
				<m-form-item label="随堂测验" v-if="totalCount">
					{{ correctCount }}/{{ totalCount }}（正确/总题数）
					<a @click.prevent="goCourseTest">查看明细</a>
				</m-form-item>
				<m-form-item label="已布置作业" style="margin-bottom: -10px"></m-form-item>
				<div>
					<exercise-table :id="formState.id" />
				</div>
				<m-form-item label="课后评语">
					<div class="ant-form-item-extra">（由本节课讲师填写，用户不可见）</div>
					<div>{{ comment }}</div>
				</m-form-item>
				<m-form-item label="聊天记录">
					<div class="chat-wrap">
						<template v-for="item in chatList" :key="item.sendTime">
							<span>
								<span :class="userClass(item.sendUserType)">
									{{ formatDate(item.sendTime, 'YYYY-MM-DD HH:mm:ss') }} [{{
										USER_TYPE_MAP[item.sendUserType]
									}}] {{ item.sendUserName }}：
								</span>
								<span>{{ item.text }}</span>
							</span>
							<br />
						</template>
					</div>
				</m-form-item>
				<m-form-item label="验收结论" name="pass" required>
					<m-select v-model:value="formState.pass" :options="options.passList" />
				</m-form-item>
				<m-form-item v-if="formState.pass === false" label="驳回原因" name="reason" required>
					<m-textarea v-model:value="formState.reason" :rows="3" />
				</m-form-item>
				<m-form-item v-else-if="formState.pass === true" label="通过备注" name="reason" required>
					<m-textarea v-model:value="formState.reason" :rows="3" />
				</m-form-item>
			</m-form>
		</template>

		<template #footer>
			<m-button plain @click="visible = false">取消</m-button>
			<m-button :loading="loading" type="primary" @click="onConfirm">确定</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { MESSAGE_TYPE, MUtils, PaasPostMessage } from 'admin-library';

import { cloneByMerge } from '@/shard/utils';
import ExerciseTable from './exercise-table.vue';
import { confirmMessageBox, formatDate } from '@/shard/utils';
import { MACHINE_AUDIT_STATUS_MAP, USER_TYPE_MAP } from '../../constant';
import { InspectStore, GetVideoStore, GetChatListStore } from '../../store';

const DefaultFormState = {
	id: null,
	pass: '',
	reason: ''
};

export default defineComponent({
	components: { ExerciseTable },
	emits: ['refresh', 'openMachineAudit'],
	setup(props, { emit }) {
		const components = {
			formRef: ref(null)
		};

		const constants = {
			MACHINE_AUDIT_STATUS_MAP,
			USER_TYPE_MAP
		};

		const state = reactive({
			visible: false,
			title: '验收',
			loading: false,
			formState: MUtils.deepClone(DefaultFormState),
			options: {
				passList: [
					{
						label: '验收通过',
						value: true
					},
					{
						label: '验收驳回',
						value: false
					}
				]
			},
			mucangId: '',
			machineAuditStatus: 0,
			playbackUrl: '',
			comment: '',
			totalCount: 0,
			correctCount: 0,
			chatList: []
		});

		const methods = {
			formatDate,
			userClass(userType) {
				if (userType === 1) {
					return 'primary';
				} else if (userType === 2) {
					return 'purple';
				} else if (userType === 3) {
					return 'orange';
				}
			},
			async open(row: any) {
				let { id, studentMucangId, machineAuditStatus } = row;
				state.formState = MUtils.deepClone({ ...DefaultFormState, id });
				methods.getChatList();

				state.mucangId = studentMucangId;
				state.machineAuditStatus = machineAuditStatus;

				const videoData: any = await GetVideoStore.request({ id }).getData();
				state.playbackUrl = videoData.playbackUrl;
				state.comment = videoData.comment;
				state.totalCount = videoData.totalCount;
				state.correctCount = videoData.correctCount;

				state.visible = true;
			},
			async getChatList() {
				const chatList: any = await GetChatListStore.request({ courseId: state.formState.id }).getData();
				state.chatList = chatList.map(item => {
					return {
						...item,
						text: JSON.parse(item.content || '{}').Text
					};
				});
			},
			goMockPage() {
				PaasPostMessage.post('navigation.to', '/#/mock-records', {
					title: '模考记录',
					query: {
						mucangId: state?.mucangId
					},
					extendData: {
						style: 'width: 960px; height: 90%; margin: 2% auto 0;'
					},
					target: '_popup'
				});
			},
			goCourseTest() {
				PaasPostMessage.post('navigation.to', '/#/course-test', {
					title: '随堂测验结果',
					query: {
						courseId: state.formState.id
					},
					extendData: {
						style: 'width: 60%; height: 90%; margin: 2% auto 0;'
					},
					target: '_popup'
				});
			},

			async onConfirm() {
				await components.formRef.value.validate();
				await confirmMessageBox(
					`确认要将这节课<span class="color: ${state.formState.pass ? 'success' : 'danger'}">${
						state.formState.pass ? '验收通过' : '验收驳回'
					}</span>吗？`
				);

				try {
					state.loading = true;
					const params = cloneByMerge(state.formState, DefaultFormState);
					const fetchStore = InspectStore;

					await fetchStore.request(params).getData();

					emit('refresh');
					state.visible = false;
					MUtils.toast('保存成功', MESSAGE_TYPE.success);
				} finally {
					state.loading = false;
				}
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods
		};
	}
});
</script>

<style lang="less" scoped>
.chat-wrap {
	overflow-y: auto;
	padding: 6px;
	height: 100px;
	white-space: break-spaces;
	border: 1px solid #999;
	resize: vertical;
}
</style>
