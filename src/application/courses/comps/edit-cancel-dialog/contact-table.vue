<template>
	<pm-effi :controller="controller">
		<pm-table
			:columns="COLUMNS"
			:use-custom-column="true"
			:pagination="false"
			:operations="false"
			:sort-num="false"
		></pm-table>
	</pm-effi>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue';
import { ModelController } from 'admin-library';

import { COLUMNS2 } from '@/application/contact-records/config';
import { ListStore } from '@/application/contact-records/store';

export default defineComponent({
	props: {
		id: {
			type: Number,
			isRequired: true
		}
	},
	setup(props) {
		const constants = {
			COLUMNS: COLUMNS2
		};

		const state = reactive({});

		const controller = new ModelController({
			table: {
				store: ListStore
			}
		});
		controller.tableRequest();

		controller.table.onRequest.use(params => {
			params = {
				...params,
				limit: 9999,
				bizId: props.id,
				direction: 1,
				sceneType: 1
			};

			return params;
		});

		return {
			...toRefs(state),
			...constants,
			controller
		};
	}
});
</script>
