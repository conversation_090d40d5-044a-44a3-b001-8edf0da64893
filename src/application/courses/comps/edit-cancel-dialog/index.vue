<template>
	<pm-dialog v-model:visible="visible" :title="title" width="780px">
		<template v-if="visible">
			<m-form
				ref="formRef"
				:model="formState"
				:colon="false"
				:label-col="{ style: { width: '95px' } }"
				autocomplete="off"
			>
				<m-form-item label="发起方" name="launchType" required>
					<m-radio-group v-model:value="formState.launchType">
						<m-radio :value="1">学员</m-radio>
						<m-radio :value="2">讲师</m-radio>
						<m-radio :value="3">督导</m-radio>
					</m-radio-group>
				</m-form-item>
				<m-form-item label="发起时间" name="launchTime" required>
					<m-date-picker
						v-model:value="formState.launchTime"
						:show-time="{ format: 'HH:mm' }"
						format="YYYY-MM-DD HH:mm"
						valueFormat="YYYY-MM-DD HH:mm"
					/>
					<div v-if="formState.launchType === 1" style="margin-top: 10px">
						<span class="danger">请注意：</span>
						若学员发起时间的距离上课时间不足1小时，（若授课的是兼职讲师）将会为该讲师结算10元（不含周末的其他国家法定假日15元），若超过或等于1小时则不结算
					</div>
					<div v-else-if="formState.launchType === 2" style="margin-top: 10px">
						<span class="danger">请注意：</span>
						由讲师发起的异常中止将不参与课时费结算。兼职讲师若多次无故爽约（非提前请假）将采取暂停排课、降级、解约等惩罚措施
					</div>
					<div v-else-if="formState.launchType === 3" style="margin-top: 10px">
						<span class="danger">请注意：</span>
						由兼职讲师发起的异常中止将不参与课时费结算。若其多次无故爽约（非提前请假）将采取暂停排课、降级、解约等惩罚措施
					</div>
				</m-form-item>
				<template v-if="formState.launchType === 1">
					<m-form-item label="电话提醒" style="margin-bottom: -10px"></m-form-item>
					<div>
						<contact-table :id="formState.id" />
					</div>
				</template>
				<m-form-item label="备注" name="remark" required>
					<m-textarea v-model:value="formState.remark" :rows="3" />
				</m-form-item>
			</m-form>
		</template>

		<template #footer>
			<m-button plain @click="visible = false">取消</m-button>
			<m-button :loading="loading" type="primary" @click="onConfirm">确定</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { MESSAGE_TYPE, MUtils } from 'admin-library';

import ContactTable from './contact-table.vue';
import { cloneFromPick, cloneByMerge } from '@/shard/utils';
import { AbendStore } from '../../store';

const DefaultFormState = {
	id: null,
	launchType: 1,
	launchTime: '',
	remark: ''
};

export default defineComponent({
	components: { ContactTable },
	emits: ['refresh'],
	setup(props, { emit }) {
		const components = {
			formRef: ref(null)
		};

		const constants = {};

		const state = reactive({
			visible: false,
			title: '异常终止',
			loading: false,
			formState: MUtils.deepClone(DefaultFormState)
		});

		const methods = {
			async open({ id }) {
				state.formState = cloneFromPick({ id }, DefaultFormState);

				state.visible = true;
			},

			async onConfirm() {
				await components.formRef.value.validate();

				try {
					state.loading = true;
					const params = cloneByMerge(state.formState, DefaultFormState);
					const fetchStore = AbendStore;

					if (params.launchTime) {
						params.launchTime = +new Date(params.launchTime);
					}

					await fetchStore.request(params).getData();

					emit('refresh');
					state.visible = false;
					MUtils.toast('保存成功', MESSAGE_TYPE.success);
				} finally {
					state.loading = false;
				}
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods
		};
	}
});
</script>
