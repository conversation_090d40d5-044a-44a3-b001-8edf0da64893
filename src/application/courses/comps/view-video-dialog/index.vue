<template>
	<pm-dialog v-model:visible="visible" :title="title" width="960px" :autoFocus="false">
		<template v-if="visible">
			<m-form
				ref="formRef"
				:model="formState"
				:colon="false"
				:label-col="{ style: { width: '105px' } }"
				autocomplete="off"
			>
				<m-row>
					<m-col :span="12">
						<m-form-item label="上课视频">
							<video
								ref="videoRef"
								v-if="playbackUrl"
								:src="playbackUrl"
								style="max-width: 100%; max-height: 400px"
								controls
							></video>
							<template v-else>视频转码中</template>
						</m-form-item>
					</m-col>
					<m-col :span="12">
						<div class="ant-row ant-form-item" style="height: calc(100% - 18px); row-gap: 0px">
							<div class="ant-col ant-form-item-label" style="width: 105px">
								<label class="ant-form-item-no-colon" title="转写文本">转写文本</label>
							</div>
							<div class="ant-col ant-form-item-control" style="position: relative; height: 100%">
								<div style="position: absolute; top: 0; right: 0; bottom: 0; left: 0">
									<div class="tran-wrap">
										<template v-for="item in transferContents" :key="item.fromTime">
											<span class="chsl" @click="jumpTime(item.fromTime)">
												<span class="primary">[{{ item.fromTime }}-{{ item.toTime }}]</span>
												<span>说话人{{ item.trackId }} {{ item.text }}</span>
											</span>
											<br />
										</template>
									</div>
								</div>
							</div>
						</div>
					</m-col>
				</m-row>
				<m-row>
					<m-col :span="12">
						<m-form-item label="学员是否可见" name="studentVisible" required>
							<m-select
								style="width: 160px"
								v-model:value="formState.studentVisible"
								:options="options.visibleList"
							/>
						</m-form-item>
					</m-col>
					<m-col :span="12">
						<m-form-item label="随堂测验" v-if="totalCount">
							{{ correctCount }}/{{ totalCount }}（正确/总题数）
							<a @click.prevent="goCourseTest">查看明细</a>
						</m-form-item>
					</m-col>
				</m-row>
				<m-row>
					<m-col :span="12">
						<m-form-item label="最后更新人">
							<div>{{ updateUserName || '--' }} {{ formatDate(updateTime, 'YYYY-MM-DD HH:mm:ss') }}</div>
						</m-form-item>
					</m-col>
					<m-col :span="12">
						<div class="ant-row ant-form-item" style="row-gap: 0px">
							<div class="ant-col ant-form-item-label" style="width: 105px">
								<label class="ant-form-item-no-colon" title="聊天记录">聊天记录</label>
							</div>
							<div class="ant-col ant-form-item-control" style="position: relative">
								<div class="chat-wrap">
									<template v-for="item in chatList" :key="item.sendTime">
										<span>
											<span :class="userClass(item.sendUserType)">
												{{ formatDate(item.sendTime, 'YYYY-MM-DD HH:mm:ss') }} [{{
													USER_TYPE_MAP[item.sendUserType]
												}}] {{ item.sendUserName }}：
											</span>
											<span>{{ item.text }}</span>
										</span>
										<br />
									</template>
								</div>
							</div>
						</div>
					</m-col>
				</m-row>
				<m-row>
					<m-col :span="12">
						<m-form-item label="质检备注">
							<m-textarea v-model:value="formState.note" :maxlength="300" show-count :rows="2" />
						</m-form-item>
					</m-col>
					<m-col :span="12">
						<m-form-item label="学员评价">
							<m-textarea
								v-model:value="formState.studentComment"
								:maxlength="300"
								show-count
								:rows="2"
							/>
						</m-form-item>
					</m-col>
				</m-row>
			</m-form>
		</template>

		<template #footer>
			<m-button plain @click="visible = false">取消</m-button>
			<m-button :loading="loading" type="primary" @click="onConfirm">确定</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { MESSAGE_TYPE, MUtils, PaasPostMessage } from 'admin-library';

import { cloneFromPick, cloneByMerge, formatDate, timeToSecond } from '@/shard/utils';
import { USER_TYPE_MAP } from '../../constant';
import { UpdateInspectionInfoStore, GetInspectionInfoStore, GetChatListStore } from '../../store';

const DefaultFormState = {
	id: null,
	studentVisible: true,
	note: '',
	studentComment: ''
};

export default defineComponent({
	emits: ['refresh'],
	setup(props, { emit }) {
		const components = {
			formRef: ref(null),
			videoRef: ref(null)
		};

		const constants = {
			USER_TYPE_MAP
		};

		const state = reactive({
			visible: false,
			title: '查看上课视频',
			loading: false,
			formState: MUtils.deepClone(DefaultFormState),
			options: {
				visibleList: [
					{
						label: '可见',
						value: true
					},
					{
						label: '不可见',
						value: false
					}
				]
			},
			courseId: '',
			playbackUrl: '',
			updateUserName: '',
			updateTime: '',
			transferContents: [],
			chatList: [],
			totalCount: 0,
			correctCount: 0
		});

		const methods = {
			formatDate,
			userClass(userType) {
				if (userType === 1) {
					return 'primary';
				} else if (userType === 2) {
					return 'purple';
				} else if (userType === 3) {
					return 'orange';
				}
			},
			jumpTime(fromTime) {
				const currentTime = timeToSecond(fromTime);
				const video = components.videoRef.value;
				video.currentTime = currentTime;
				video.play();
			},
			async open(row) {
				state.courseId = row.id;
				methods.getChatList();
				const videoData: any = await GetInspectionInfoStore.request({ id: row.id }).getData();
				let { playbackUrl, asr, updateUserName, updateTime, totalCount, correctCount } = videoData;
				state.playbackUrl = playbackUrl;
				state.updateUserName = updateUserName;
				state.updateTime = updateTime;
				state.transferContents = JSON.parse(asr || '[]');
				state.totalCount = totalCount;
				state.correctCount = correctCount;
				state.formState = cloneFromPick({ ...row, ...videoData }, DefaultFormState);

				state.visible = true;
			},
			async getChatList() {
				const chatList: any = await GetChatListStore.request({ courseId: state.courseId }).getData();
				state.chatList = chatList.map(item => {
					return {
						...item,
						text: JSON.parse(item.content || '{}').Text
					};
				});
			},
			goCourseTest() {
				PaasPostMessage.post('navigation.to', '/#/course-test', {
					title: '随堂测验结果',
					query: {
						courseId: state.courseId
					},
					extendData: {
						style: 'width: 60%; height: 90%; margin: 2% auto 0;'
					},
					target: '_popup'
				});
			},

			async onConfirm() {
				await components.formRef.value.validate();

				try {
					state.loading = true;
					const params = cloneByMerge(state.formState, DefaultFormState);
					const fetchStore = UpdateInspectionInfoStore;

					await fetchStore.request(params).getData();

					emit('refresh');
					state.visible = false;
					MUtils.toast('保存成功', MESSAGE_TYPE.success);
				} finally {
					state.loading = false;
				}
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods
		};
	}
});
</script>

<style lang="less" scoped>
.tran-wrap {
	overflow-y: auto;
	padding: 6px;
	height: 100%;
	white-space: break-spaces;
	border: 1px solid #999;
}
.chsl {
	cursor: pointer;
	&:hover {
		color: var(--ant-primary-color);
	}
}
.chat-wrap {
	overflow-y: auto;
	padding: 6px;
	height: 100px;
	white-space: break-spaces;
	border: 1px solid #999;
	resize: vertical;
}
</style>
