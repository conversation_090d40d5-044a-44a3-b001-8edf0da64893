<template>
	<pm-dialog v-model:visible="visible" :title="title" width="680px">
		<template v-if="visible">
			<m-form
				ref="formRef"
				:model="formState"
				:colon="false"
				:label-col="{ style: { width: '95px' } }"
				autocomplete="off"
			>
				<m-form-item label="车型" name="carType" required>
					<m-select
						:options="options.carList"
						v-model:value="formState.carType"
						:allowClear="true"
						placeholder="车型"
					/>
				</m-form-item>
				<m-form-item label="辅导科目" name="kemu" required>
					<m-select
						:options="options.kemuList"
						v-model:value="formState.kemu"
						:allowClear="true"
						placeholder="辅导科目"
					/>
				</m-form-item>
			</m-form>
		</template>

		<template #footer>
			<m-button plain @click="visible = false">取消</m-button>
			<m-button :loading="loading" type="primary" @click="onConfirm">确定</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { MESSAGE_TYPE, MUtils } from 'admin-library';

import { cloneFromPick, cloneByMerge } from '@/shard/utils';
import { CAR_TYPE_OPTIONS, EXAM_KEMU_OPTIONS } from '@/shard/constant';
import { UpdateCarTypeAndKemuStore } from '../../store';

const DefaultFormState = {
	id: null,
	carType: '',
	kemu: ''
};

export default defineComponent({
	emits: ['refresh'],
	setup(props, { emit }) {
		const components = {
			formRef: ref(null)
		};

		const constants = {};

		const state = reactive({
			visible: false,
			title: '修改车型科目',
			loading: false,
			formState: MUtils.deepClone(DefaultFormState),
			options: {
				carList: CAR_TYPE_OPTIONS,
				kemuList: EXAM_KEMU_OPTIONS
			}
		});

		const methods = {
			open(row: any) {
				state.formState = cloneFromPick(row, DefaultFormState);

				state.visible = true;
			},
			async onConfirm() {
				await components.formRef.value.validate();

				try {
					state.loading = true;
					const params = cloneByMerge(state.formState, DefaultFormState);
					const fetchStore = UpdateCarTypeAndKemuStore;

					await fetchStore.request(params).getData();

					emit('refresh');
					state.visible = false;
					MUtils.toast('保存成功', MESSAGE_TYPE.success);
				} finally {
					state.loading = false;
				}
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods
		};
	}
});
</script>
