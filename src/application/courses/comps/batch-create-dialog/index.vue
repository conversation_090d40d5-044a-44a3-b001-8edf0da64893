<template>
	<pm-dialog v-model:visible="visible" :title="title" width="880px">
		<template v-if="visible">
			<m-form
				ref="formRef"
				:model="formState"
				:colon="false"
				:label-col="{ style: { width: '95px' } }"
				autocomplete="off"
			>
				<m-form-item label="学员姓名" name="studentId" required>
					<student-select
						:disabled="!!studentId"
						@onSelectChange="onStudentChange"
						v-model:value="formState.studentId"
						data-index="studentId"
					/>
				</m-form-item>
				<m-form-item label="讲师" name="lecturerId" required>
					<!-- 这个只筛选“已上岗”（status===20）的讲师 -->
					<teacher-select
						v-model:value="formState.lecturerId"
						:requestParams="{ status: 20 }"
						:showDetail="true"
						:disabled="!!lecturerId"
					/>
				</m-form-item>
				<div style="padding-left: 50px">
					<m-form ref="form2Ref" :model="formState">
						<AdvancedForm :formRef="form2Ref" :columns="columns" v-model:value="formState.courses">
							<template #headerCell="{ column }">
								<template v-if="column.key === 'recommendSprint'">
									{{ column.title }}

									<m-tooltip placement="right">
										<template #title>
											<div style="width: 200px">
												学员正式考试前最后一节课即临考冲刺课，系统将自动推荐教学内容为临考冲刺课件
											</div>
										</template>
										<question-circle-filled />
									</m-tooltip>
								</template>
							</template>
							<template #beginTime="{ editable, lineData, index, dataIndex, text }">
								<m-form-item
									v-if="editable"
									:name="['courses', index, dataIndex]"
									label="开始时间"
									:labelCol="{ span: 0 }"
									required
									style="margin: 10px 0; height: 32px"
								>
									<m-date-picker
										@change="change(index)"
										v-model:value="lineData[dataIndex]"
										:allowClear="false"
										:show-time="{ format: 'HH:mm' }"
										format="YYYY-MM-DD HH:mm"
										valueFormat="YYYY-MM-DD HH:mm"
										placeholder="开始时间"
									/>
								</m-form-item>
								<template v-else>{{ text }}</template>
							</template>
							<template #endTime="{ editable, lineData, index, dataIndex, text }">
								<m-form-item
									v-if="editable"
									:name="['courses', index, dataIndex]"
									label="结束时间"
									:labelCol="{ span: 0 }"
									required
									style="margin: 10px 0; height: 32px"
								>
									<m-date-picker
										v-model:value="lineData[dataIndex]"
										:allowClear="false"
										:show-time="{ format: 'HH:mm' }"
										format="YYYY-MM-DD HH:mm"
										valueFormat="YYYY-MM-DD HH:mm"
										placeholder="结束时间"
									/>
								</m-form-item>
								<template v-else>{{ text }}</template>
							</template>
							<template #subject="{ editable, lineData, index, dataIndex, text }">
								<div>
									<m-form-item
										v-if="editable"
										:name="['courses', index, dataIndex]"
										label="课程主题"
										:labelCol="{ span: 0 }"
										required
										style="margin: 10px 0; height: 32px"
									>
										<m-input v-model:value="lineData[dataIndex]" placeholder="课程主题" />
									</m-form-item>
									<template v-else>{{ text }}</template>
								</div>
							</template>
							<template #recommendSprint="{ lineData, index, dataIndex }">
								<div>
									<m-form-item
										:name="['courses', index, dataIndex]"
										style="margin: 10px 0; height: 32px; text-align: center"
									>
										<m-checkbox
											v-model:checked="lineData[dataIndex]"
											@change="onRocketChange(index)"
										></m-checkbox>
									</m-form-item>
								</div>
							</template>
						</AdvancedForm>
					</m-form>
				</div>
				<m-form-item label="直接发起任务" name="submitDirectly">
					<m-checkbox v-model:checked="formState.submitDirectly">确定上述课程安排都直接发起任务</m-checkbox>
					<div class="danger">
						若勾选「直接发起任务」，课程安排将跳过“草稿”状态。若讲师是兼职讲师，课程安排会直接同步到第三方任务库，请慎重选择！！！
					</div>
				</m-form-item>
				<m-form-item label="未接收提醒" name="notifyBeforeHours" required>
					<m-select v-model:value="formState.notifyBeforeHours" :options="options.beforeHoutsList" />
				</m-form-item>
			</m-form>
		</template>

		<template #footer>
			<m-button plain @click="visible = false">取消</m-button>
			<m-button :loading="loading" type="primary" @click="onConfirm">确定</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import Dayjs from 'dayjs';
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { MESSAGE_TYPE, MUtils } from 'admin-library';
import { QuestionCircleFilled } from '@ant-design/icons-vue';

import StudentSelect from '@/comp/student-select/index.vue';
import TeacherSelect from '@/comp/teacher-select/index.vue';
import AdvancedForm from '@/comp/advanced-form/index.vue';
import { cloneFromPick, cloneByMerge, toAwait } from '@/shard/utils';
import { BEFORE_HOURS_OPTIONS } from '../../constant';
import { BatchCreateStore, SuggestContinueStore } from '../../store';

const DefaultFormState = {
	studentId: '',
	lecturerId: null,
	notifyBeforeHours: 3,
	submitDirectly: false,
	courses: []
};

export default defineComponent({
	emits: ['refresh'],
	components: { StudentSelect, TeacherSelect, AdvancedForm, QuestionCircleFilled },
	setup(props, { emit }) {
		const components = {
			formRef: ref(null),
			form2Ref: ref(null)
		};

		const constants = {
			columns: [
				{
					title: '开始时间',
					dataIndex: 'beginTime',
					xtype: 'custom'
				},
				{
					title: '结束时间',
					dataIndex: 'endTime',
					xtype: 'custom'
				},
				{
					title: '课程主题',
					dataIndex: 'subject',
					xtype: 'custom'
				},
				{
					title: '临考冲刺课？',
					dataIndex: 'recommendSprint',
					xtype: 'custom',
					width: 130
				}
			]
		};

		const state = reactive({
			visible: false,
			title: '批量添加',
			loading: false,
			formState: MUtils.deepClone(DefaultFormState),
			studentId: '',
			lecturerId: '',
			options: {
				beforeHoutsList: BEFORE_HOURS_OPTIONS
			},
			studentData: {} as any
		});

		const methods = {
			open(row?: any) {
				state.formState = MUtils.deepClone(DefaultFormState);

				if (row) {
					state.formState = cloneFromPick(row, DefaultFormState);
					state.studentId = row.studentId;
				}

				state.visible = true;
			},
			async onStudentChange(_val, detail) {
				state.studentData = detail;
				state.formState.lecturerId = detail.lecturerId;
				state.lecturerId = detail.lecturerId;
			},
			change(index) {
				let endTime = state.formState.courses[index].beginTime;
				endTime = Dayjs(endTime);
				if (endTime.format('HH:mm') === '23:00') {
					endTime = endTime.add(59, 'minute');
				} else {
					endTime = endTime.add(1, 'hour');
				}
				endTime = endTime.format('YYYY-MM-DD HH:mm');
				state.formState.courses[index].endTime = endTime;
				if (state.studentData.id) {
					let beginTime = state.formState.courses[index].beginTime.substring(0, 10);
					state.formState.courses[index].subject = `${state.studentData.name}-${beginTime.replace(
						/-/g,
						''
					)}课程`;
				}
			},
			onRocketChange(index) {
				state.formState.courses.forEach((_item, i) => {
					if (i !== index) {
						state.formState.courses[i].recommendSprint = false;
					}
				});
			},
			async onConfirm() {
				await Promise.all([components.formRef.value.validate(), components.form2Ref.value.validate()]);

				let [err, res] = await toAwait(
					SuggestContinueStore.request({ studentId: state.formState.studentId }).getData()
				);
				if (!err && !res?.value) {
					const isConfirm = await MUtils.confirm({
						title: '上课效果不及预期提醒',
						content:
							'该学员经过三节课的学习，近五次模考平均分依然没有达到80分，建议申请主管老师介入，调整学员教学方案后再继续排课。',
						type: MESSAGE_TYPE.warning,
						confirmText: '继续排课',
						cancelText: '取消排课'
					});
					if (!isConfirm) {
						return;
					}
				}

				try {
					state.loading = true;
					const params = cloneByMerge(state.formState, DefaultFormState);
					const fetchStore = BatchCreateStore;

					if (params.courses && params.courses.length) {
						params.courses.forEach(item => {
							item.beginTime = +new Date(item.beginTime);
							item.endTime = +new Date(item.endTime);
						});
						params.courses = JSON.stringify(params.courses);
					}

					await fetchStore.request(params).getData();

					emit('refresh');
					state.visible = false;
					MUtils.toast('保存成功', MESSAGE_TYPE.success);
				} finally {
					state.loading = false;
				}
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods
		};
	}
});
</script>
