<template>
	<pm-dialog v-model:visible="visible" :title="title" width="680px">
		<template v-if="visible">
			<m-form
				ref="formRef"
				:model="formState"
				:colon="false"
				:label-col="{ style: { width: '95px' } }"
				autocomplete="off"
			>
				<m-form-item label="开始时间" name="beginTime" required>
					<m-date-picker
						@change="change()"
						v-model:value="formState.beginTime"
						:allowClear="false"
						:show-time="{ format: 'HH:mm' }"
						format="YYYY-MM-DD HH:mm"
						valueFormat="YYYY-MM-DD HH:mm"
						placeholder="开始时间"
					/>
				</m-form-item>
				<m-form-item label="结束时间" name="endTime" required>
					<m-date-picker
						v-model:value="formState.endTime"
						:allowClear="false"
						:show-time="{ format: 'HH:mm' }"
						format="YYYY-MM-DD HH:mm"
						valueFormat="YYYY-MM-DD HH:mm"
						placeholder="结束时间"
					/>
				</m-form-item>
			</m-form>
		</template>

		<template #footer>
			<m-button plain @click="visible = false">取消</m-button>
			<m-button :loading="loading" type="primary" @click="onConfirm">确定</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import Dayjs from 'dayjs';
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { MESSAGE_TYPE, MUtils } from 'admin-library';

import { cloneFromPick, cloneByMerge } from '@/shard/utils';
import { UpdateTimeStore } from '../../store';

const DefaultFormState = {
	id: null,
	beginTime: '',
	endTime: ''
};

export default defineComponent({
	emits: ['refresh'],
	setup(props, { emit }) {
		const components = {
			formRef: ref(null)
		};

		const constants = {};

		const state = reactive({
			visible: false,
			title: '修改上课时间',
			loading: false,
			formState: MUtils.deepClone(DefaultFormState)
		});

		const methods = {
			open(row: any) {
				state.formState = cloneFromPick(row, DefaultFormState);

				if (state.formState.beginTime) {
					state.formState.beginTime = Dayjs(state.formState.beginTime).format('YYYY-MM-DD HH:mm:ss');
				}
				if (state.formState.endTime) {
					state.formState.endTime = Dayjs(state.formState.endTime).format('YYYY-MM-DD HH:mm:ss');
				}

				state.visible = true;
			},
			change() {
				let endTime = state.formState.beginTime;
				endTime = Dayjs(endTime).add(1, 'hour');
				endTime = endTime.format('YYYY-MM-DD HH:mm');
				state.formState.endTime = endTime;
			},

			async onConfirm() {
				await components.formRef.value.validate();

				try {
					state.loading = true;
					const params = cloneByMerge(state.formState, DefaultFormState);
					const fetchStore = UpdateTimeStore;

					if (params.beginTime) {
						params.beginTime = +new Date(params.beginTime);
					}
					if (params.endTime) {
						params.endTime = +new Date(params.endTime);
					}

					await fetchStore.request(params).getData();

					emit('refresh');
					state.visible = false;
					MUtils.toast('保存成功', MESSAGE_TYPE.success);
				} finally {
					state.loading = false;
				}
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods
		};
	}
});
</script>
