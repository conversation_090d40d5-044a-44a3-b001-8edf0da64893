<template>
	<pm-effi :controller="controller">
		<pm-search>
			<pm-search-single
				:span="4"
				:antdProps="{
					placeholder: '课程Id',
					type: 'number'
				}"
				data-index="id"
				xtype="INPUT"
			/>
			<pm-search-single
				:span="4"
				:antdProps="{
					placeholder: '学员姓名'
				}"
				data-index="studentName"
				xtype="INPUT"
			/>
			<pm-search-single
				:model-value="studentMucangId"
				:span="3"
				:antdProps="{
					placeholder: '木仓Id'
				}"
				data-index="studentMucangId"
				xtype="INPUT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '学习方案'
				}"
				data-index="studyPlan"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="3"
				show-search
				optionFilterProp="employeeMixName"
				:antdProps="{
					placeholder: '督导老师',
					fieldNames: { label: 'employeeMixName', value: 'id' }
				}"
				data-index="supervisorId"
				xtype="SELECT"
			/>
			<pm-search-single>
				<template #custom>
					<teacher-select @onSearchLecturer="onSearchLecturer" data-index="lecturerId" comp-type="search" />
				</template>
			</pm-search-single>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '讲师类型'
				}"
				data-index="lecturerType"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="3"
				v-model="statusValue"
				:antdProps="{
					placeholder: '状态'
				}"
				data-index="status"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="3"
				v-model="abendScenceValue"
				:disabled="statusValue !== 10"
				:antdProps="{
					placeholder: '异常状态'
				}"
				data-index="abendScence"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '机审状态'
				}"
				data-index="machineAuditStatus"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="5"
				:antd-props="{
					placeholder: ['课程时间', '结束时间']
				}"
				xtype="RANGEPICKER"
				data-index="beginTime|endTime"
			/>
		</pm-search>

		<pm-table
			:columns="COLUMNS"
			:use-custom-column="true"
			:operations-fixed="true"
			:operations-width="260"
			:sort-num="false"
			:scroll="{
				x: 'max-content'
			}"
			:antd-props="{
				customRow: record => {
					return {
						onClick: () => {
							onClick(record);
						}
					};
				}
			}"
			:rowClassName="setInstallClassName"
		>
			<template #beforeCustom>
				<m-button type="primary" @click="onCreate">添加</m-button>
			</template>
			<template #subject="{ record }">
				{{ record.subject }}
				<m-tooltip
					placement="right"
					:title="record.recommendSprint ? '这是临考冲刺课' : '这不是临考冲刺课'"
					:get-popup-container="getPopupContainer"
					:auto-adjust-overflow="false"
				>
					<rocket-outlined
						@click.stop="onToggleRocket(record)"
						:style="{ fontSize: '16px' }"
						:class="record.recommendSprint ? 'primary' : 'info'"
					/>
				</m-tooltip>
			</template>
			<template #lecturerType="{ record }">
				<div v-if="record.lecturerType === 1">官方</div>
				<m-button type="link" v-else class="primary cursor-pointer" @click="viewExpense(record)">兼职</m-button>
			</template>
			<template #kemu="{ record }">
				<m-button type="link" class="primary cursor-pointer" @click="onEditKemu(record)">
					{{ TUROR_KEMU_MAP[record.kemu] }}
				</m-button>
			</template>
			<template #carType="{ record }">
				<m-button type="link" class="primary cursor-pointer" @click="onEditKemu(record)">
					{{ CAR_TYPE_MAP[record.carType] }}
				</m-button>
			</template>
			<template #status="{ record }">
				<div class="primary" v-if="record.status === 7 || record.status === 8">
					<m-tooltip placement="top">
						<template #title>
							<span>{{ record.reason }}</span>
						</template>
						<span
							v-html="
								renderTextWithColor(record.status, STATUS_OPTIONS, {
									isTag: true,
									inverse: false,
									isReturnStr: true
								})
							"
						></span>
					</m-tooltip>
				</div>
				<div class="primary" v-else-if="record.status === 10">
					<m-tooltip placement="top">
						<template #title>
							<span>{{ record.abendRemark }}</span>
						</template>
						<span
							v-html="
								renderTextWithColor(record.status, STATUS_OPTIONS, {
									isTag: true,
									inverse: false,
									isReturnStr: true
								})
							"
						></span>
						<span
							v-html="
								renderTextWithColor(record.abendScence, ABEND_STATUS_OPTIONS, {
									isTag: true,
									inverse: true,
									isReturnStr: true
								})
							"
						></span>
					</m-tooltip>
				</div>
				<template v-else>
					<span
						v-html="
							renderTextWithColor(record.status, STATUS_OPTIONS, {
								isTag: true,
								inverse: false,
								isReturnStr: true
							})
						"
					></span>
				</template>
			</template>
			<template #machineAuditStatus="{ record }">
				<template v-if="record.machineAuditStatus === 0">
					{{ MACHINE_AUDIT_STATUS_MAP[record.machineAuditStatus] }}
				</template>
				<m-button type="link" v-else @click="openMachineAudit(record)">
					{{ MACHINE_AUDIT_STATUS_MAP[record.machineAuditStatus] }}
				</m-button>
			</template>

			<template #operations="{ record }">
				<m-button type="link" @click="goTaskRecord(record)">课程任务同步记录</m-button>
				<m-button
					v-if="record.status === 1 || record.status === 2 || record.status === 3"
					type="link"
					@click="onEditTime(record)"
				>
					修改上课时间
				</m-button>
				<m-button v-if="record.status === 1" type="link" @click="onEditLecturer(record)">修改讲师</m-button>
				<m-button
					v-if="record.status === 1 || record.status === 2"
					danger
					type="link"
					@click="onDiscard(record)"
				>
					废弃
				</m-button>
				<m-button v-if="record.status === 1" type="link" @click="onLaunch(record)">发起任务</m-button>
				<m-button v-if="record.status === 3" danger type="link" @click="onCancel(record)">异常终止</m-button>
				<m-button v-if="record.status === 6 || record.status === 7" type="link" @click="onInspect(record)">
					验收
				</m-button>
				<m-button v-if="record.status === 8" type="link" @click="onViewVideo(record)">查看上课视频</m-button>
				<m-button v-if="record.status <= 8" type="link" @click="goExercise(record)">作业</m-button>
				<m-button type="link" @click="goContent(record)">推荐教学内容</m-button>
			</template>
		</pm-table>
	</pm-effi>

	<pm-dialog
		v-model:visible="machineAuditVisible"
		title="机审状态"
		width="640px"
		:footer="false"
		:autoFocus="false"
		:zIndex="10001"
	>
		<div>
			<m-row>
				<m-col>状态：{{ MACHINE_AUDIT_STATUS_MAP[machineAuditData.machineAuditStatus] }}</m-col>
			</m-row>
			<m-row>
				<m-col>
					视频时长：{{ formatTimeStr(machineAuditData.videoDuration * 1000, 'HH:mm:ss') }}
					<span v-html="getStatusText(machineAuditData.videoDurationAuditResult, true)"></span>
				</m-col>
				<m-col style="padding-left: 10px">
					讲述人数量：{{ machineAuditData.tellerCount }}
					<span v-html="getStatusText(machineAuditData.tellerCountAuditResult, true)"></span>
				</m-col>
			</m-row>
			<div>转写文本</div>
			<div>
				<textarea :value="getContentText(machineAuditData.transferContents)" rows="6" style="width: 100%" />
			</div>
			<div>
				对话内容敏感：
				<span v-html="getStatusText(machineAuditData.prohibitedWordsAuditResult, false)"></span>
			</div>
			<div>
				<pre
					style="overflow-y: auto; height: 236px; white-space: break-spaces"
					v-html="getContentText(machineAuditData.prohibitedContents, machineAuditData.prohibitedWords)"
				></pre>
			</div>
		</div>
	</pm-dialog>
	<batch-create-comp ref="batchCreateRef" @refresh="onRefresh"></batch-create-comp>
	<edit-time-comp ref="editTimeRef" @refresh="onRefresh"></edit-time-comp>
	<edit-lecturer-comp ref="editLecturerRef" @refresh="onRefresh"></edit-lecturer-comp>
	<edit-inspect-comp
		ref="editInspectRef"
		@refresh="onRefresh"
		@openMachineAudit="openMachineAudit"
	></edit-inspect-comp>
	<view-video-comp ref="viewVideoRef" @refresh="onRefresh"></view-video-comp>
	<edit-cancel-comp ref="editCancelRef" @refresh="onRefresh"></edit-cancel-comp>
	<edit-kemu-comp ref="editKemuRef" @refresh="onRefresh"></edit-kemu-comp>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref, watch } from 'vue';
import { ModelController, MUtils, MESSAGE_TYPE, PaasPostMessage } from 'admin-library';
import { useRoute } from 'vue-router';
import { RocketOutlined } from '@ant-design/icons-vue';
import { Store } from '@simplex/simple-store';

import TeacherSelect from '@/comp/teacher-select/index.vue';
import BatchCreateComp from './comps/batch-create-dialog/index.vue';
import EditTimeComp from './comps/edit-time-dialog/index.vue';
import EditLecturerComp from './comps/edit-lecturer-dialog/index.vue';
import EditInspectComp from './comps/edit-inspect-dialog/index.vue';
import ViewVideoComp from './comps/view-video-dialog/index.vue';
import EditCancelComp from './comps/edit-cancel-dialog/index.vue';
import EditKemuComp from './comps/edit-kemu-dialog/index.vue';
import { confirmMessageBox, formatTimeStr, renderTextWithColor } from '@/shard/utils';
import { COLUMNS } from './config';
import { TUROR_KEMU_MAP, CAR_TYPE_MAP } from '@/shard/constant';
import { TYPE_STORE } from '@/application/part-time-teacher/constant';
import { STUDY_PLAN_STORE } from '@/application/student/constant';
import { PAY_STATUS_MAP } from '@/application/settlement/constant';
import {
	STATUS_STORE,
	ABEND_STATUS_STORE,
	MACHINE_AUDIT_STATUS_MAP,
	MACHINE_AUDIT_STATUS_STORE,
	STATUS_OPTIONS,
	ABEND_STATUS_OPTIONS
} from './constant';
import { ListStore as EmployeeListStore } from '@/application/promoter/store';
import {
	ListStore,
	CancelStore,
	SubmitStore,
	ViewVideoMachineAuditStore,
	RecommendSprintStore,
	CancelRecommendSprintStore
} from './store';
import { ItemResponse } from './types';

export default defineComponent({
	components: {
		TeacherSelect,
		BatchCreateComp,
		EditLecturerComp,
		EditTimeComp,
		EditInspectComp,
		ViewVideoComp,
		EditCancelComp,
		EditKemuComp,
		RocketOutlined
	},
	setup() {
		const route = useRoute();
		const query = route.query;
		const components = {
			batchCreateRef: ref<InstanceType<typeof BatchCreateComp>>(),
			editTimeRef: ref<InstanceType<typeof EditTimeComp>>(),
			editLecturerRef: ref<InstanceType<typeof EditLecturerComp>>(),
			editInspectRef: ref<InstanceType<typeof EditInspectComp>>(),
			viewVideoRef: ref<InstanceType<typeof ViewVideoComp>>(),
			editCancelRef: ref<InstanceType<typeof EditCancelComp>>(),
			editKemuRef: ref<InstanceType<typeof EditKemuComp>>()
		};

		const constants = {
			COLUMNS,
			TUROR_KEMU_MAP,
			CAR_TYPE_MAP,
			MACHINE_AUDIT_STATUS_MAP,
			STATUS_OPTIONS,
			ABEND_STATUS_OPTIONS
		};

		const state = reactive({
			selectedId: 0,
			studentMucangId: query.mucangId,
			statusValue: null,
			abendScenceValue: null,
			machineAuditVisible: false,
			machineAuditData: {} as ItemResponse
		});

		const controller = new ModelController({
			table: {
				store: ListStore
			},
			search: {
				studyPlan: {
					store: STUDY_PLAN_STORE
				},
				supervisorId: {
					store: EmployeeListStore
				},
				lecturerId: {
					store: new Store([])
				},
				lecturerType: {
					store: TYPE_STORE
				},
				status: {
					store: STATUS_STORE
				},
				abendScence: {
					store: ABEND_STATUS_STORE
				},
				machineAuditStatus: {
					store: MACHINE_AUDIT_STATUS_STORE
				}
			}
		});
		controller.tableRequest();

		controller.table.onRequest.use(params => {
			let sd = {};
			if (state.abendScenceValue) {
				sd = { abendScence: state.abendScenceValue };
			}
			params = {
				...params,
				...sd
			};

			return params;
		});

		controller.search.supervisorId.onRequest.use(params => {
			params = {
				...params,
				station: 5,
				limit: 9999
			};

			return params;
		});

		watch(
			() => state.statusValue,
			() => {
				if (state.statusValue !== 10) {
					state.abendScenceValue = null;
				} else {
					state.abendScenceValue = 2;
				}
			}
		);

		const methods = {
			renderTextWithColor,
			formatTimeStr,
			getStatusText(status, useBracket) {
				let text;
				if (status) {
					text = '<span class="success">通过</span>';
				} else {
					text = '<span class="danger">不通过</span>';
				}
				return useBracket ? `(${text})` : text;
			},
			getContentText(list, sensitive?) {
				return list
					.map(item => {
						let text = item.text;
						if (sensitive) {
							sensitive.forEach(word => {
								text = text.replaceAll(word, function (str) {
									return `<span class="danger">${str}</span>`;
								});
							});
						}
						return `[${item.fromTime}-${item.toTime}]说话人${item.trackId} ${text}`;
					})
					.join('\n');
			},
			onClick(recored) {
				state.selectedId = recored.id;
			},
			setInstallClassName(record) {
				if (record.id === state.selectedId) {
					return 'selected-row';
				}
			},
			onCreate() {
				components.batchCreateRef.value.open();
			},
			async onSearchLecturer(list) {
				controller.search.lecturerId.updateStore(new Store(list));
			},
			goTaskRecord(row: ItemResponse) {
				PaasPostMessage.post('navigation.to', '/#/course-task-record', {
					title: '课程任务同步记录',
					query: {
						id: row?.id
					},
					extendData: {
						style: 'width: 80%; height: 600px; margin: 140px auto 0;'
					},
					target: '_popup'
				});
			},
			onEditTime(row: ItemResponse) {
				components.editTimeRef.value.open(row);
			},
			onEditLecturer(row: ItemResponse) {
				components.editLecturerRef.value.open(row);
			},
			async openMachineAudit(row: ItemResponse) {
				const res = await ViewVideoMachineAuditStore.request({ courseId: row.id }).getData();
				state.machineAuditData = res;
				state.machineAuditVisible = true;
			},
			async onDiscard(row: ItemResponse) {
				await confirmMessageBox('确认废弃吗？');

				await CancelStore.request({ courseId: row.id }).getData();
				methods.onRefresh();
				MUtils.toast('废弃成功', MESSAGE_TYPE.success);
			},
			async onLaunch(row: ItemResponse) {
				await confirmMessageBox('确认发起任务吗？');

				await SubmitStore.request({ id: row.id }).getData();
				methods.onRefresh();
				MUtils.toast('发起任务成功', MESSAGE_TYPE.success);
			},
			async onToggleRocket(row: ItemResponse) {
				const { recommendSprint } = row;
				const message = recommendSprint
					? '要取消这节课是「临考冲刺课」吗？'
					: '要设置这节课是「临考冲刺课」吗？';
				const featchStore = recommendSprint ? CancelRecommendSprintStore : RecommendSprintStore;
				await confirmMessageBox(message);

				await featchStore.request({ courseId: row.id }).getData();
				methods.onRefresh();
				MUtils.toast('保存成功', MESSAGE_TYPE.success);
			},
			async onCancel(row: ItemResponse) {
				components.editCancelRef.value.open(row);
			},
			onEditKemu(row: ItemResponse) {
				components.editKemuRef.value.open(row);
			},
			onInspect(row: ItemResponse) {
				components.editInspectRef.value.open(row);
			},
			onViewVideo(row: ItemResponse) {
				components.viewVideoRef.value.open(row);
			},
			goExercise(row: ItemResponse) {
				PaasPostMessage.post('navigation.to', '/#/course-exercise', {
					title: '课后作业',
					query: {
						courseScheduleId: row?.id,
						kemu: row?.kemu
					},
					extendData: {
						style: 'width: 80%; height: 600px; margin: 140px auto 0;'
					},
					target: '_popup'
				});
			},
			goContent(row: ItemResponse) {
				PaasPostMessage.post('navigation.to', '/#/course-content', {
					title: '推荐教学内容',
					query: {
						courseId: row?.id,
						kemu: row?.kemu
					},
					extendData: {
						style: 'width: 80%; height: 90%; margin: 2% auto 0;'
					},
					target: '_popup'
				});
			},
			viewExpense(row) {
				let settlementStr =
					row.settlementDetailStatus !== null ? PAY_STATUS_MAP[row.settlementDetailStatus] : '未发起结算';
				MUtils.alert({
					title: '查看课时费',
					content: `课时费（元）：${row.courseFee}<br>支付状态：${settlementStr}`
				});
			},

			onRefresh() {
				controller.tableRequest();
			},
			getPopupContainer(trigger: HTMLElement) {
				return trigger.parentElement;
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			controller
		};
	}
});
</script>
