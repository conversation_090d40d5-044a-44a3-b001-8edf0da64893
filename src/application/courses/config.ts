import { ColumnXtype, TableColumn, TableDateFormat } from 'admin-library';
import { renderCopyComp } from '@/shard/utils';
import { ColumnWidthEnum } from '@/shard/constant';
import { STUDY_PLAN_MAP } from '@/application/student/constant';

export const COLUMNS: TableColumn[] = [
	{
		title: '#',
		dataIndex: 'id',
		fixed: 'left'
	},
	{
		title: '课程主题',
		dataIndex: 'subject',
		xtype: ColumnXtype.CUSTOM,
		fixed: 'left'
	},
	{
		title: '车型',
		dataIndex: 'carType',
		xtype: ColumnXtype.CUSTOM,
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '辅导科目',
		dataIndex: 'kemu',
		xtype: ColumnXtype.CUSTOM,
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '课程No',
		dataIndex: 'courseNo'
	},
	{
		title: '学员姓名',
		dataIndex: 'studentName',
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '学员Id',
		dataIndex: 'studentId',
		width: ColumnWidthEnum.TEXT3
	},
	{
		title: '学习方案',
		dataIndex: 'studyPlan',
		render: data => {
			return STUDY_PLAN_MAP[data];
		},
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '木仓Id',
		dataIndex: 'studentMucangId',
		render: data => {
			return renderCopyComp(data);
		},
		width: ColumnWidthEnum.TEXT3
	},
	{
		title: '督导',
		dataIndex: 'supervisorNickName'
	},
	{
		title: '讲师',
		dataIndex: 'lecturerName',
		render: (value, lineData) => {
			return `${value}（${lineData.lecturerNickName}）`;
		}
	},
	{
		title: '讲师类型',
		dataIndex: 'lecturerType',
		xtype: ColumnXtype.CUSTOM,
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '课程开始时间',
		dataIndex: 'beginTime',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	},
	{
		title: '课程结束时间',
		dataIndex: 'endTime',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	},
	{
		title: '机审状态',
		dataIndex: 'machineAuditStatus',
		xtype: ColumnXtype.CUSTOM,
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '状态',
		dataIndex: 'status',
		xtype: ColumnXtype.CUSTOM
	},
	{
		title: '提醒时间',
		dataIndex: 'notifyTime',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	},
	{
		title: '创建人',
		dataIndex: 'createUserName',
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '创建时间',
		dataIndex: 'createTime',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	}
];
