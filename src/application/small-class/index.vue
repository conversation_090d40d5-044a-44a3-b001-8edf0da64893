<template>
	<pm-effi :controller="controller">
		<pm-search>
			<pm-search-single
				:span="4"
				:antdProps="{
					placeholder: '请输入课程名称'
				}"
				data-index="courseName"
				xtype="INPUT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '请选择车型',
					allowClear: true
				}"
				data-index="carType"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '请选择科目',
					allowClear: true
				}"
				data-index="kemu"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '请选择状态',
					allowClear: true
				}"
				data-index="status"
				xtype="SELECT"
			/>
		</pm-search>

		<pm-table
			:columns="COLUMNS"
			:use-custom-column="true"
			:operations-fixed="true"
			:operations-width="160"
			:sort-num="false"
			:scroll="{
				x: 'max-content'
			}"
		>
			<template #beforeCustom>
				<m-button type="primary" @click="onCreate">添加课程</m-button>
			</template>

			<template #operations="{ record }">
				<m-button type="link" @click="onViewSchedule(record)">查看课程表</m-button>
				<m-button v-if="record.status !== 40" danger type="link" @click="onOffline(record)">下线</m-button>
				<m-button type="link" @click="onEdit(record)">编辑</m-button>
			</template>
		</pm-table>
	</pm-effi>

	<edit-dialog ref="editRef" @refresh="onRefresh" />
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { ModelController, MESSAGE_TYPE, MUtils, PaasPostMessage } from 'admin-library';

import { COLUMNS } from './config';
import { 
	COURSE_STATUS_STORE, 
	SMALL_CLASS_CAR_TYPE_STORE, 
	SMALL_CLASS_KEMU_STORE
} from './constant';
import { ListStore, OfflineStore } from './store';
import { ItemResponse } from './types';
import EditDialog from './comps/edit-dialog/index.vue';

export default defineComponent({
	components: {
		EditDialog
	},
	setup() {

		const components = {
			editRef: ref<InstanceType<typeof EditDialog>>()
		};

		const constants = {
			COLUMNS
		};

		console.log('listStore------', ListStore);

		const state = reactive({});

		const controller = new ModelController({
			table: {
				store: ListStore
			},
			search: {
				carType: {
					store:  SMALL_CLASS_CAR_TYPE_STORE
				},
				kemu: {
					store: SMALL_CLASS_KEMU_STORE
				},
				status: {
					store: COURSE_STATUS_STORE
				}
			}
		});
		
		controller.tableRequest();

		const methods = {
			getStatusText(record: ItemResponse) {
				const now = new Date();
				const startDate = new Date(record.startDate + ' 00:00:00');
				const endDate = new Date(record.endDate + ' 23:59:59');

				if (!record.isOnline) {
					return '已下线';
				} else if (now < startDate) {
					return '报名中';
				} else if (now >= startDate && now <= endDate) {
					return '进行中';
				} else {
					return '已结束';
				}
			},
			
			getStatusColor(record: ItemResponse) {
				const status = methods.getStatusText(record);
				switch (status) {
					case '报名中': return 'orange';
					case '进行中': return 'green';
					case '已结束': return 'default';
					case '已下线': return 'red';
					default: return 'default';
				}
			},

			onCreate() {
				components.editRef.value?.open();
			},

			onEdit(record: ItemResponse) {
				components.editRef.value?.open(record);
			},

			async onOffline(record: ItemResponse) {
				const confirmed = await MUtils.confirm({
					title: `确定要下线该课程吗？`,
					type: MESSAGE_TYPE.warning,
					confirmText: '确定',
					cancelText: '取消'
				});

				if (!confirmed) {return;}

				try {
					await OfflineStore.request({
						classNo: record.classNo
					}).getData();

					MUtils.toast('操作成功', MESSAGE_TYPE.success);
					controller.tableRequest();
				} catch (error) {
					MUtils.toast('操作失败，请重试', MESSAGE_TYPE.error);
				}
			},

			onViewSchedule(record: ItemResponse) {
				PaasPostMessage.post('navigation.to', '/#/teacher-data-bank', {
					title: '课程任务同步记录',
					query: {
						id: record?.id
					},
					extendData: {
						style: 'width: 80%; height: 600px; margin: 140px auto 0;'
					},
					target: '_tab'
				});
			},

			onRefresh() {
				controller.tableRequest();
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			controller
		};
	}
});
</script>
