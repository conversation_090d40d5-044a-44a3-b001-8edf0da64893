<template>
	<pm-dialog v-model:visible="visible" :title="title" width="680px">
		<template v-if="visible">
			<m-form
				ref="formRef"
				:model="formState"
				:colon="false"
				:label-col="{ style: { width: '95px' } }"
				autocomplete="off"
			>
				<m-form-item 
					label="课程名称" 
					name="name"
					:rules="[
						{ required: true, message: '请输入课程名称' },
						{ max: 50, message: '课程名称不能超过50个字符' }
					]"
				>
					<m-input 
						v-model:value="formState.name" 
						placeholder="请输入课程名称，限50字以内" 
						:maxlength="50"
						show-count
					/>
				</m-form-item>

				<m-form-item
					label="课程周期"
					name="coursePeriod"
					:rules="[
						{ required: true, message: '请选择课程周期' },
						{ validator: validateDateRange }
					]"
				>
					<m-range-picker
						v-model:value="formState.coursePeriod"
						:placeholder="['开始日期', '结束日期']"
						format="YYYY-MM-DD HH:mm:ss"
						value-format="YYYY-MM-DD HH:mm:ss"
						:show-time="{ format: 'YYYY-MM-DD HH:mm:ss' }"
						:disabled-date="disabledDate"
					/>
				</m-form-item>

				<m-form-item 
					label="课程名额" 
					name="quota"
					:rules="[
						{ required: true, message: '请输入课程名额' },
						{ type: 'number', min: 1, message: '课程名额必须大于0' }
					]"
				>
					<m-input-number 
						v-model:value="formState.quota" 
						placeholder="请输入课程名额"
						:min="1"
						:max="999"
						style="width: 100%"
					/>
				</m-form-item>

				<m-form-item 
					label="车型" 
					name="carType"
					:rules="[{ required: true, message: '请选择车型' }]"
				>
					<m-select
						v-model:value="formState.carType"
						:options="options.carTypeList"
						placeholder="请选择车型"
					/>
				</m-form-item>

				<m-form-item
					label="科目"
					name="kemu"
					:rules="[{ required: true, message: '请选择科目' }]"
				>
					<m-select
						v-model:value="formState.kemu"
						:options="options.kemuList"
						placeholder="请选择科目"
					/>
				</m-form-item>

				<m-form-item>
					<m-alert
						message="说明"
						description="课时数将根据小班课下关联的课程讲次数据自动计算，一个讲次算一节课。"
						type="info"
						show-icon
						:closable="false"
					/>
				</m-form-item>
			</m-form>
		</template>

		<template #footer>
			<m-button plain @click="visible = false">取消</m-button>
			<m-button :loading="loading" type="primary" @click="onConfirm">确定</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { DEFAULT_TIME_FORMAT, MESSAGE_TYPE, MUtils } from 'admin-library';

import { cloneFromPick, cloneByMerge } from '@/shard/utils';
import { SMALL_CLASS_CAR_TYPE_OPTIONS, SMALL_CLASS_KEMU_OPTIONS } from '../../constant';
import { CreateStore, UpdateStore } from '../../store';
import Dayjs from 'dayjs';
const DefaultFormState = {
	classNo: null,
	name: '',
	coursePeriod: [],
	quota: null,
	carType: null,
	kemu: null
};

export default defineComponent({
	emits: ['refresh'],
	setup(props, { emit }) {
		const components = {
			formRef: ref(null)
		};

		const state = reactive({
			visible: false,
			title: '添加课程',
			loading: false,
			formState: MUtils.deepClone(DefaultFormState),
			options: {
				carTypeList: SMALL_CLASS_CAR_TYPE_OPTIONS,
				kemuList: SMALL_CLASS_KEMU_OPTIONS
			}
		});

		const methods = {
			// 禁用过去的日期
			disabledDate(current: any) {
				return current && current < new Date().setHours(0, 0, 0, 0);
			},

			// 验证日期范围
			validateDateRange(rule: any, value: any) {
				if (!value || value.length !== 2) {
					return Promise.resolve();
				}
				const [beginTime, endTime] = value;
				const start = new Date(beginTime);
				const end = new Date(endTime);

				if (start >= end) {
					return Promise.reject(new Error('结束日期必须大于开始日期'));
				}

				// 检查课程周期不能超过30天
				const diffTime = Math.abs(end.getTime() - start.getTime());
				const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
				if (diffDays > 30) {
					return Promise.reject(new Error('课程周期不能超过30天'));
				}

				return Promise.resolve();
			},

			open(row?: any) {
				if (row) {
					state.title = '编辑课程';
					state.formState = cloneFromPick(row, DefaultFormState);
					// 处理日期范围
					if (row.beginTime && row.endTime) {
						state.formState.coursePeriod = [row.beginTime, row.endTime];
					}
					state.formState.kemu = row.tutorKemu;
				} else {
					state.title = '添加课程';
					state.formState = MUtils.deepClone(DefaultFormState);
				}
				state.visible = true;
			},

			async onConfirm() {
				await components.formRef.value.validate();

				try {
					state.loading = true;
					const params = cloneByMerge(state.formState, DefaultFormState);

					// 处理日期范围
					if (params.coursePeriod && params.coursePeriod.length === 2) {
						params.beginTime = Dayjs(params.coursePeriod[0]).format(DEFAULT_TIME_FORMAT);
						params.endTime = Dayjs(params.coursePeriod[1]).format(DEFAULT_TIME_FORMAT);
						delete params.coursePeriod;
					}

					const fetchStore = params.classNo ? UpdateStore : CreateStore;
					await fetchStore.request(params).getData();

					emit('refresh');
					state.visible = false;
					MUtils.toast('保存成功', MESSAGE_TYPE.success);
				} catch (error) {
					console.error('Save small class failed:', error);
					MUtils.toast('保存失败，请重试', MESSAGE_TYPE.error);
				} finally {
					state.loading = false;
				}
			}
		};

		return {
			...toRefs(state),
			...components,
			...methods
		};
	}
});
</script>
