import {
	List,
	Create,
	Update,
	Delete,
	Offline,
	GetCourseSchedule
} from '@/store/small-class';

import { ItemResponse } from './types';

export const ListStore = new List<Array<ItemResponse>>({});
export const CreateStore = new Create({});
export const UpdateStore = new Update({});
export const DeleteStore = new Delete({});
export const OfflineStore = new Offline({});
export const GetCourseScheduleStore = new GetCourseSchedule<Array<ItemResponse>>({});
