import { ColumnXtype, TableColumn, TableDateFormat } from 'admin-library';
import { ColumnWidthEnum } from '@/shard/constant';
import { SMALL_CLASS_CAR_TYPE_MAP, SMALL_CLASS_KEMU_MAP } from './constant';
import Dayjs from 'dayjs';

export const COLUMNS: TableColumn[] = [
	{
		title: '#',
		dataIndex: 'id',
		fixed: 'left',
		width: ColumnWidthEnum.TEXT3
	},
	{
		title: '课程名称',
		dataIndex: 'name',
		width: 200
	},
	{
		title: '课程周期',
		dataIndex: 'coursePeriod',
		render: (value, record) => {
			if (record.beginTime && record.endTime) {
				const beginTime = Dayjs(record.beginTime).format('YYYY/MM/DD');
				const endTime = Dayjs(record.endTime).format('YYYY/MM/DD');
				return `${beginTime}~${endTime}`;
			}
			return value || '-';
		},
		width: 180
	},
	{
		title: '课时数',
		dataIndex: 'courseHours',
		render: (value) => {
			return value ? `${value}课时` : '-';
		},
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '课程名额',
		dataIndex: 'quota',
		render: (value) => {
			return value ? `${value}人` : '-';
		},
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '车型',
		dataIndex: 'carType',
		render: data => {
			return SMALL_CLASS_CAR_TYPE_MAP[data];
		},
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '科目',
		dataIndex: 'tutorKemu',
		render: data => {
			return SMALL_CLASS_KEMU_MAP[data];
		},
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '课程状态',
		dataIndex: 'status',
		xtype: ColumnXtype.CUSTOM,
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '创建时间',
		dataIndex: 'createTime',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	},
	{
		title: '操作人',
		dataIndex: 'updateUserName',
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '操作',
		dataIndex: 'action',
		xtype: ColumnXtype.CUSTOM,
		fixed: 'right',
		width: 200
	}
];
