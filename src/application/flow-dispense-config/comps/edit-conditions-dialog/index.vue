<template>
	<pm-dialog v-model:visible="visible" :title="title" width="630px">
		<template v-if="visible">
			<m-form
				ref="formRef"
				:model="formState"
				:colon="false"
				:label-col="{ style: { width: '95px' } }"
				autocomplete="off"
			>
				<m-form-item label="投放周期" name="hourList">
					<m-row type="flex">
						<m-col flex="1">
							<m-checkable-tag
								v-for="item in options.hourList"
								:key="item.value"
								:checked="formState.hourList.indexOf(item.value) > -1"
								@change="checked => handleChange(item.value, checked)"
							>
								{{ item.label }}
							</m-checkable-tag>
						</m-col>
						<m-col flex="0 1 auto">
							<m-checkbox style="margin-top: 8px" v-model:checked="checkAll" @change="onCheckAllChange">
								全选
							</m-checkbox>
						</m-col>
					</m-row>
				</m-form-item>
				<m-form-item label="投放周期" name="weekList">
					<m-checkbox-group v-model:value="formState.weekList">
						<m-checkbox
							style="margin-left: 0; width: 60px"
							v-for="item in options.weekList"
							:key="item.label"
							:value="item.value"
						>
							{{ item.label }}
						</m-checkbox>
					</m-checkbox-group>
				</m-form-item>
			</m-form>
		</template>

		<template #footer>
			<m-button plain @click="visible = false">取消</m-button>
			<m-button :loading="loading" type="primary" @click="onConfirm">确定</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref, computed } from 'vue';
import { MESSAGE_TYPE, MUtils } from 'admin-library';

import { cloneFromPick, cloneByMerge } from '@/shard/utils';
import { GetConditionsStore, SetConditionsStore } from '../../store';

const DefaultFormState = {
	id: null,
	hourList: [],
	weekList: []
};

export default defineComponent({
	emits: ['refresh'],
	setup(props, { emit }) {
		const components = {
			formRef: ref(null)
		};

		const constants = {};

		const checkAll = computed(() => {
			let val = state.formState.hourList;
			return val.length > 0 && val.length === state.options.hourList.length;
		});
		const state = reactive({
			visible: false,
			title: '投放策略',
			loading: false,
			formState: MUtils.deepClone(DefaultFormState),
			options: {
				hourList: Array.from({ length: 24 }, (_, i) => i).map(item => {
					return {
						label: `${item} 时`,
						value: item
					};
				}),
				weekList: [
					{
						label: '周一',
						value: 1
					},
					{
						label: '周二',
						value: 2
					},
					{
						label: '周三',
						value: 3
					},
					{
						label: '周四',
						value: 4
					},
					{
						label: '周五',
						value: 5
					},
					{
						label: '周六',
						value: 6
					},
					{
						label: '周日',
						value: 7
					}
				]
			}
		});

		const methods = {
			handleChange(value: number, checked: boolean) {
				const { hourList } = state.formState;
				const nextCheckedTags = checked ? [...hourList, value] : hourList.filter(t => t !== value);
				state.formState.hourList = nextCheckedTags;
			},
			async open(row?: any) {
				let { id } = row;
				let conditions: any = await GetConditionsStore.request({ id }).getData();
				conditions = JSON.parse(conditions.value || '{}');
				state.formState = cloneFromPick({ ...row, ...conditions }, DefaultFormState);

				state.visible = true;
			},
			onCheckAllChange(e: any) {
				Object.assign(state.formState, {
					hourList: e.target.checked ? state.options.hourList.map(item => item.value) : []
				});
			},
			async onConfirm() {
				try {
					state.loading = true;
					const { id, ...reset } = cloneByMerge(state.formState, DefaultFormState);
					const params = {
						id,
						value: JSON.stringify(reset)
					};
					const fetchStore = SetConditionsStore;

					await fetchStore.request(params).getData();

					emit('refresh');
					state.visible = false;
					MUtils.toast('保存成功', MESSAGE_TYPE.success);
				} finally {
					state.loading = false;
				}
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			checkAll
		};
	}
});
</script>

<style lang="less" scoped>
.ant-tag {
	padding: 3px 7px;
	margin-top: 8px;
	min-width: 52px;
	text-align: center;
	color: #5e5e5e;
	border: solid 1px #e9e9e9;
}
.ant-tag-checkable-checked {
	color: #fff;
	border: solid 1px transparent;
}
</style>
