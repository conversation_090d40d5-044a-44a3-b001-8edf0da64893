<template>
	<pm-dialog v-model:visible="visible" :title="title" width="680px">
		<template v-if="visible">
			<m-form
				ref="formRef"
				:model="formState"
				:colon="false"
				:label-col="{ style: { width: '150px' } }"
				autocomplete="off"
			>
				<m-form-item label="策略名称" name="name" required>
					<m-input v-model:value="formState.name" placeholder="策略名称" />
				</m-form-item>
				<m-form-item label="车型" name="carType">
					<m-select
						v-model:value="formState.carType"
						:options="options.carTypeList"
						:allowClear="true"
						placeholder="车型"
					/>
				</m-form-item>
				<m-form-item label="科目" name="kemu">
					<m-select
						v-model:value="formState.kemu"
						:options="options.kemuList"
						:allowClear="true"
						placeholder="辅导科目"
					/>
				</m-form-item>
				<m-form-item label="是否VIP" name="isVip">
					<m-select
						v-model:value="formState.bizRule.isVip"
						:options="options.radioOptions"
						:allowClear="true"
						placeholder="是否VIP"
					/>
				</m-form-item>
				<m-form-item label="微信线索创建天数" name="name" required>
					<m-input
						v-model:value="formState.bizRule.wxLeadsCreatedDays"
						type="number"
						min="0"
						placeholder="微信线索创建天数"
					/>
				</m-form-item>
				<m-form-item label="排序" name="sort" required>
					<m-input-number v-model:value="formState.sort" placeholder="排序" />
				</m-form-item>
				<m-form-item label="分配规则" :name="['value', 'selectType']" required>
					<m-select
						v-model:value="formState.value.selectType"
						:options="options.selectTypeList"
						placeholder="分配规则"
					/>
				</m-form-item>
				<m-form-item label="流量分配">
					<div
						style="padding: 0 15px 5px; margin-bottom: 10px; background-color: #f2f2f2"
						v-for="(item, index) in formState.value.itemList"
						:key="index"
					>
						<m-row justify="space-between" align="middle" style="padding: 8px 0 10px">
							<m-col flex="60px"></m-col>
							<m-col>
								<div style="font-size: 14px; line-height: 1.6">策略 {{ index + 1 }}</div>
							</m-col>
							<m-col class="btn-wrap">
								<m-button :disabled="index === 0" type="link" @click="upItem(index)">上移</m-button>
								<m-button
									:disabled="index === formState.value.itemList.length - 1"
									type="link"
									@click="downItem(index)"
								>
									下移
								</m-button>
								<m-button
									:disabled="formState.value.itemList.length <= 1"
									danger
									type="link"
									@click="removeItem(index)"
								>
									删除
								</m-button>
							</m-col>
						</m-row>
						<div v-if="formState.value.selectType === 1">
							<m-form-item
								label="流量比例"
								:label-col="{ span: 0 }"
								:name="['value', 'itemList', index, 'proportion']"
								required
							>
								<m-input-number v-model:value="item.proportion" placeholder="流量比例" />
							</m-form-item>
						</div>
						<m-row justify="space-between" align="middle" style="padding: 0">
							<m-col :span="12" style="padding-right: 10px">
								<m-form-item
									label="业务类型"
									:label-col="{ span: 0 }"
									:name="['value', 'itemList', index, 'receptionType']"
									required
								>
									<m-select
										v-model:value="item.receptionType"
										:options="options.receptionTypeList"
										placeholder="业务类型"
									/>
								</m-form-item>
							</m-col>
							<m-col :span="12">
								<m-form-item
									label="业务说明"
									:label-col="{ span: 0 }"
									:name="['value', 'itemList', index, 'description']"
								>
									<m-input v-model:value="item.description" placeholder="业务说明" />
								</m-form-item>
							</m-col>
						</m-row>
						<div v-if="item.receptionType === 10">
							<m-form-item
								label="入口标识"
								:label-col="{ span: 0 }"
								:name="['value', 'itemList', index, 'receptionIdentifier']"
								required
							>
								<m-input v-model:value="item.receptionIdentifier" placeholder="入口标识" />
							</m-form-item>
						</div>
						<div v-else>
							<m-form-item
								label="入口标识"
								:label-col="{ span: 0 }"
								:name="['value', 'itemList', index, 'receptionIdentifierList']"
								required
							>
								<m-select
									v-model:value="item.receptionIdentifierList"
									mode="tags"
									placeholder="入口标识"
								/>
							</m-form-item>
						</div>
						<div v-if="item.receptionType === 10">
							<m-form-item
								label="IM销售账号ID"
								:label-col="{ span: 0 }"
								:name="['value', 'itemList', index, 'extraData', 'imUserId']"
								required
							>
								<m-input v-model:value="item.extraData.imUserId" placeholder="IM销售账号ID" />
							</m-form-item>
						</div>
					</div>
					<m-button style="margin-top: 0; margin-bottom: 8px; width: 100%" type="dashed" @click="addItem">
						<template #icon><PlusOutlined /></template>
						继续添加
					</m-button>
				</m-form-item>
			</m-form>
		</template>

		<template #footer>
			<m-button plain @click="visible = false">取消</m-button>
			<m-button :loading="loading" type="primary" @click="onConfirm">确定</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { MESSAGE_TYPE, MUtils } from 'admin-library';
import { PlusOutlined } from '@ant-design/icons-vue';

import { cloneFromPick, cloneByMerge } from '@/shard/utils';
import { CAR_TYPE_OPTIONS, FULL_KEMU_OPTIONS } from '@/shard/constant';
import { RADIO_OPTIONS, RECEPTION_TYPE_OPTIONS, SELECT_TYPE_OPTIONS } from '../../constant';
import { CreateStore, UpdateStore } from '../../store';

const DefaultFormState = {
	id: null,
	name: '',
	carType: '',
	kemu: null,
	bizRule: {
		isVip: null,
		wxLeadsCreatedDays: null
	},
	sort: null,
	value: {
		itemList: []
	}
};

export default defineComponent({
	components: {
		PlusOutlined
	},
	emits: ['refresh'],
	setup(props, { emit }) {
		const components = {
			formRef: ref(null)
		};

		const constants = {};

		const state = reactive({
			visible: false,
			title: '',
			loading: false,
			formState: MUtils.deepClone(DefaultFormState),
			options: {
				carTypeList: CAR_TYPE_OPTIONS,
				kemuList: FULL_KEMU_OPTIONS,
				radioOptions: RADIO_OPTIONS,
				receptionTypeList: RECEPTION_TYPE_OPTIONS,
				selectTypeList: SELECT_TYPE_OPTIONS
			}
		});

		const methods = {
			upItem(index) {
				const { itemList } = state.formState.value;
				[itemList[index], itemList[index - 1]] = [itemList[index - 1], itemList[index]];
			},
			downItem(index) {
				const { itemList } = state.formState.value;
				[itemList[index], itemList[index + 1]] = [itemList[index + 1], itemList[index]];
			},
			removeItem(index) {
				state.formState.value.itemList.splice(index, 1);
			},
			async addItem() {
				await components.formRef.value.validate();

				state.formState.value.itemList.push({
					receptionType: null,
					proportion: null,
					receptionIdentifier: '',
					receptionIdentifierList: [],
					description: '',
					extraData: {
						imUserId: ''
					}
				});
			},
			async open(row?: any) {
				state.formState = MUtils.deepClone(DefaultFormState);
				state.title = row?.id ? '编辑' : '新增';

				if (row) {
					let { value, bizRule } = row;
					bizRule = JSON.parse(bizRule || '{}');
					value = JSON.parse(value || '{ "itemList": [] }');
					value.itemList = value.itemList.map(item => {
						if (item.receptionType === 11) {
							item.receptionType = 10;
						} else if (item.receptionType === 21) {
							item.receptionType = 20;
						}
						item.extraData = JSON.parse(item.extraData || `{"imUserId":""}`);
						if (item.receptionType !== 10) {
							item.receptionIdentifierList = item.receptionIdentifier.split(',') || [];
						}
						return item;
					});
					state.formState = cloneFromPick({ ...row, value, bizRule }, DefaultFormState);
				} else {
					state.formState.value.itemList = [
						{
							receptionType: null,
							proportion: null,
							receptionIdentifier: '',
							receptionIdentifierList: [],
							description: '',
							extraData: {
								imUserId: ''
							}
						}
					];
				}

				state.visible = true;
			},
			async onConfirm() {
				await components.formRef.value.validate();

				try {
					state.loading = true;
					const params = cloneByMerge(state.formState, DefaultFormState);
					const fetchStore = state.formState.id ? UpdateStore : CreateStore;

					if (!state.formState.id) {
						params.bizType = 'user_entrance';
						params.code = 'default_cs_reception_router';
					}

					params.bizRule = JSON.stringify(params.bizRule);
					params.value.itemList = params.value.itemList.map(item => {
						if (item.receptionType === 10) {
							item.extraData = JSON.stringify(item.extraData);
						} else {
							delete item.extraData;
							item.receptionIdentifier = item.receptionIdentifierList.join(',');
						}
						delete item.receptionIdentifierList;
						return item;
					});
					params.value = JSON.stringify(params.value);

					await fetchStore.request(params).getData();

					emit('refresh');
					state.visible = false;
					MUtils.toast('保存成功', MESSAGE_TYPE.success);
				} finally {
					state.loading = false;
				}
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods
		};
	}
});
</script>
<style>
.btn-wrap > .ant-btn {
	padding: 0;
	margin-left: 10px;
	&:first-child {
		margin-left: 0;
	}
}
</style>
