import { List, Del, Create, Update, GetConditions, SetConditions, GetReceptionSeq } from '@/store/flow-dispense-config';
import { ItemResponse } from './types';

export const ListStore = new List<Array<ItemResponse>>({});
export const DelStore = new Del({});
export const CreateStore = new Create({});
export const UpdateStore = new Update({});
export const GetConditionsStore = new GetConditions({});
export const SetConditionsStore = new SetConditions({});
export const GetReceptionSeqStore = new GetReceptionSeq({});
