<template>
	<pm-effi :controller="controller">
		<pm-search>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '车型'
				}"
				data-index="carType"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '科目'
				}"
				data-index="kemu"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '状态'
				}"
				data-index="status"
				xtype="SELECT"
			/>
		</pm-search>

		<pm-table
			:columns="COLUMNS"
			:use-custom-column="true"
			:operations-fixed="true"
			:operations-width="200"
			:sort-num="false"
			:scroll="{
				x: 'max-content'
			}"
		>
			<template #beforeCustom>
				<m-button type="primary" @click="onCreate">添加</m-button>
			</template>
			<template #dispenseRatio="{ record }">
				<table style="background: transparent" v-if="record?.dispenseRatio.length > 0">
					<tr v-for="(item, index) in record.dispenseRatio" :key="index">
						<td style="height: 24px; white-space: nowrap; background: transparent">
							{{ RECEPTION_TYPE_MAP[item.receptionType] }}
						</td>
						<td style="height: 24px; background: transparent">{{ item.proportion }}</td>
						<td style="height: 24px; background: transparent">
							{{ item.receptionIdentifier }}
						</td>
					</tr>
				</table>
			</template>

			<template #operations="{ record }">
				<m-button type="link" danger @click="toggleStatus(record)" v-if="record.status === 2">下线</m-button>
				<m-button type="link" @click="toggleStatus(record)" v-else>上线</m-button>
				<m-button type="link" @click="onEdit(record)">编辑</m-button>
				<m-button type="link" @click="onConditionsEdit(record)">投放策略</m-button>
				<m-button type="link" @click="onCopy(record)">复制</m-button>
				<m-button type="link" danger @click="onDel(record)">删除</m-button>
			</template>
		</pm-table>
	</pm-effi>

	<edit-comp ref="editRef" @refresh="onRefresh"></edit-comp>
	<edit-conditions-comp ref="editConditionsRef" @refresh="onRefresh"></edit-conditions-comp>
</template>

<script lang="ts">
import { defineComponent, reactive, ref, toRefs } from 'vue';
import { ModelController, MUtils, MESSAGE_TYPE } from 'admin-library';

import EditComp from './comps/edit-dialog/index.vue';
import EditConditionsComp from './comps/edit-conditions-dialog/index.vue';
import { confirmMessageBox } from '@/shard/utils';
import { COLUMNS } from './config';
import { CAR_TYPE_STORE, FULL_KEMU_STORE } from '@/shard/constant';
import { STATUS_STORE, RECEPTION_TYPE_MAP } from './constant';
import { ListStore, DelStore, UpdateStore } from './store';
import { ItemResponse } from './types';

export default defineComponent({
	components: {
		EditComp,
		EditConditionsComp
	},
	setup() {
		const components = {
			editRef: ref<InstanceType<typeof EditComp>>(),
			editConditionsRef: ref<InstanceType<typeof EditConditionsComp>>()
		};

		const constants = {
			COLUMNS,
			RECEPTION_TYPE_MAP
		};

		const state = reactive({});

		const controller = new ModelController({
			table: {
				store: ListStore
			},
			search: {
				carType: {
					store: CAR_TYPE_STORE
				},
				kemu: {
					store: FULL_KEMU_STORE
				},
				status: {
					store: STATUS_STORE
				}
			}
		});
		controller.tableRequest();

		controller.table.onResponse.use(data => {
			data.data.forEach(item => {
				item.dispenseRatio = JSON.parse(item.value || '{ "itemList": [] }').itemList;
			});

			return data;
		});

		const methods = {
			onCreate() {
				components.editRef.value.open();
			},
			onEdit(row: ItemResponse) {
				components.editRef.value.open(row);
			},
			onConditionsEdit(row: ItemResponse) {
				components.editConditionsRef.value.open(row);
			},
			async onCopy(row: ItemResponse) {
				components.editRef.value.open({ ...row, id: null });
			},

			async toggleStatus(row) {
				await UpdateStore.request({ id: row.id, status: row.status === 2 ? 0 : 2 }).getData();
				methods.onRefresh();
				MUtils.toast('操作成功', MESSAGE_TYPE.success);
			},
			async onDel(row: ItemResponse) {
				await confirmMessageBox('确认删除吗？');

				await DelStore.request({ id: row.id }).getData();
				methods.onRefresh();
				MUtils.toast('删除成功', MESSAGE_TYPE.success);
			},

			onRefresh() {
				controller.tableRequest();
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			controller
		};
	}
});
</script>
