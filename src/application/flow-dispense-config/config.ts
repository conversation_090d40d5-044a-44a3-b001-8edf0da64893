import { TableColumn, ColumnXtype, TableDateFormat } from 'admin-library';
import { renderTextWithColor } from '@/shard/utils';
import { ColumnWidthEnum, CAR_TYPE_MAP, FULL_KEMU_MAP } from '@/shard/constant';
import { STATUS_OPTIONS, RADIO_MAP } from './constant';

export const COLUMNS: TableColumn[] = [
	{
		title: '#',
		dataIndex: 'id'
	},
	{
		title: '策略名称',
		dataIndex: 'name'
	},
	{
		title: '车型',
		dataIndex: 'carType',
		render: data => {
			return CAR_TYPE_MAP[data];
		},
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '科目',
		dataIndex: 'kemu',
		render: data => {
			return FULL_KEMU_MAP[data];
		},
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '是否VIP',
		dataIndex: 'isVip',
		render: (value, lineData) => {
			let { bizRule } = lineData;
			bizRule = JSON.parse(bizRule || '{}');
			return RADIO_MAP[bizRule.isVip];
		},
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '微信线索创建天数',
		dataIndex: 'wxLeadsCreatedDays',
			render: (value, lineData) => {
			let { bizRule } = lineData;
			bizRule = JSON.parse(bizRule || '{}');
			return bizRule.wxLeadsCreatedDays;
		},
		width: ColumnWidthEnum.TEXT8
	},
	{
		title: '排序',
		dataIndex: 'sort',
		width: ColumnWidthEnum.TEXT2
	},
	{
		title: '流量比例',
		dataIndex: 'dispenseRatio',
		xtype: ColumnXtype.CUSTOM,
		width: 220
	},
	{
		title: '状态',
		dataIndex: 'status',
		render: data => {
			return renderTextWithColor(data, STATUS_OPTIONS);
		},
		width: ColumnWidthEnum.TEXT3
	},
	{
		title: '更新时间',
		dataIndex: 'updateTime',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	},
	{
		title: '更新人',
		dataIndex: 'updateUserName',
		width: ColumnWidthEnum.TEXT4
	}
];
