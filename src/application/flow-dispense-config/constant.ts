import { getMapfromArray, getStorefromArray } from '@/shard/utils';
import { ColorEnum } from '@/shard/constant';

export const STATUS_OPTIONS = [
	{
		label: '下线',
		value: 0,
		styleclass: ColorEnum.danger
	},
	{
		label: '上线',
		value: 2,
		styleclass: ColorEnum.success
	}
];
export const STATUS_MAP = getMapfromArray(STATUS_OPTIONS);
export const STATUS_STORE = getStorefromArray(STATUS_OPTIONS);

export const RADIO_OPTIONS = [
	{
		label: '否',
		value: 0
	},
	{
		label: '是',
		value: 1
	}
];
export const RADIO_MAP = getMapfromArray(RADIO_OPTIONS);
export const RADIO_STORE = getStorefromArray(RADIO_OPTIONS);

export const RECEPTION_TYPE_OPTIONS = [
	{
		label: 'IM',
		value: 10
	},
	{
		label: '企微',
		value: 20
	},
	{
		label: '获客助手',
		value: 30
	}
];
export const RECEPTION_TYPE_MAP = getMapfromArray(
	RECEPTION_TYPE_OPTIONS.concat([
		{
			label: 'IM',
			value: 11
		},
		{
			label: '企微',
			value: 21
		}
	])
);
export const RECEPTION_TYPE_STORE = getStorefromArray(RECEPTION_TYPE_OPTIONS);

export const SELECT_TYPE_OPTIONS = [
  {
    label: '按设置比例',
    value: 1
  },
  {
    label: '随机',
    value: 2
  }
];

export const SELECT_TYPE_MAP = getMapfromArray(SELECT_TYPE_OPTIONS);
export const SELECT_TYPE_STORE = getStorefromArray(SELECT_TYPE_OPTIONS);