import { getMapfromArray, getStorefromArray } from '@/shard/utils';
import { ColorEnum } from '@/shard/constant';

export const STATUS_OPTIONS = [
	{
		label: '未发布',
		value: 1,
		styleclass: ColorEnum.info
	},
	{
		label: '待执行',
		value: 2,
		styleclass: ColorEnum.info
	},
	{
		label: '执行中',
		value: 3,
		styleclass: ColorEnum.warning
	},
	{
		label: '已执行',
		value: 4,
		styleclass: ColorEnum.primary
	},
	{
		label: '执行成功',
		value: 5,
		styleclass: ColorEnum.success
	},
	{
		label: '执行失败',
		value: 6,
		styleclass: ColorEnum.danger
	},
	{
		label: '取消执行',
		value: 7,
		styleclass: ColorEnum.info
	}
];
export const STATUS_MAP = getMapfromArray(STATUS_OPTIONS);
export const STATUS_STORE = getStorefromArray(STATUS_OPTIONS);
