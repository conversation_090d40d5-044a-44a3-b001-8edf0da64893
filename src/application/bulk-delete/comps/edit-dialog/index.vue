<template>
	<pm-dialog v-model:visible="visible" :title="title" width="680px">
		<template v-if="visible">
			<m-form
				ref="formRef"
				:model="formState"
				:colon="false"
				:label-col="{ style: { width: '95px' } }"
				autocomplete="off"
			>
				<m-form-item label="企微账号" name="employeeId" required>
					<m-select
						show-search
						optionFilterProp="employeeMixName"
						v-model:value="formState.employeeId"
						:options="options.employeeOptions"
						:fieldNames="{ label: 'employeeMixName', value: 'id' }"
					/>
				</m-form-item>
				<m-form-item label="选择标签" name="tagId" required>
					<TagPickComp v-model:value="formState.tagId" />
				</m-form-item>
				<m-form-item label="推送时间" name="executeTime" required>
					<m-date-picker
						v-model:value="formState.executeTime"
						:show-time="{ format: 'HH:mm' }"
						format="YYYY-MM-DD HH:mm"
						valueFormat="YYYY-MM-DD HH:mm"
					/>
				</m-form-item>
			</m-form>
		</template>

		<template #footer>
			<m-button plain @click="visible = false">取消</m-button>
			<m-button :loading="loading" type="primary" @click="onConfirm">确定</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import Dayjs from 'dayjs';
import { defineComponent, reactive, toRefs, ref, computed } from 'vue';
import { MESSAGE_TYPE, MUtils } from 'admin-library';

import TagPickComp from './tag-picker.vue';
import { cloneFromPick, cloneByMerge } from '@/shard/utils';
import { ListStore as EmployeeListStore } from '@/application/promoter/store';
import { CreateStore, UpdateStore, TagListStore } from '../../store';

const DefaultFormState = {
	id: undefined,
	employeeId: '',
	tagId: [],
	executeTime: null,
	commandParam: '{}'
};

let tagList = [];

export default defineComponent({
	components: {
		TagPickComp
	},
	emits: ['refresh'],
	setup(props, { emit }) {
		const components = {
			formRef: ref(null)
		};

		const constants = {};

		const state = reactive({
			visible: false,
			title: '',
			loading: false,
			formState: MUtils.deepClone(DefaultFormState),
			options: {
				employeeOptions: []
			}
		});

		const wecomUserId = computed(() => {
			const { employeeId } = state.formState;
			let employee = state.options.employeeOptions.find(item => {
				return item.id === employeeId;
			});
			return employee?.wecomUserId;
		});

		const methods = {
			async open(row?: any) {
				state.formState = MUtils.deepClone(DefaultFormState);
				state.title = row?.id ? '编辑' : '新增';

				if (row) {
					state.formState = cloneFromPick(row, DefaultFormState);
				}

				if (state.formState.commandParam) {
					const commandParam = JSON.parse(state.formState.commandParam);
					if (commandParam.tagId) {
						state.formState.tagId = [commandParam.tagId];
					}
				}
				if (state.formState.executeTime) {
					state.formState.executeTime = Dayjs(state.formState.executeTime);
				}

				await methods.initEmployeeOptions();
				state.visible = true;
			},

			async initEmployeeOptions() {
				const res = await EmployeeListStore.request({
					enableAutomaticHosting: true,
					limit: 9999,
					status: 20
				}).getData();
				if (res) {
					state.options.employeeOptions = res;
				}
			},
			async getTagList() {
				if (tagList.length) {
					return tagList;
				}
				const res = await TagListStore.request({ limit: 9999 }).getData();
				tagList = res;
				return tagList;
			},
			async onConfirm() {
				await components.formRef.value.validate();

				try {
					state.loading = true;
					const params = cloneByMerge(state.formState, DefaultFormState);
					const fetchStore = state.formState.id ? UpdateStore : CreateStore;

					if (params.executeTime) {
						params.executeTime = +new Date(params.executeTime);
					}

					const _tagList = await methods.getTagList();
					params.commandParam = JSON.stringify(
						params.tagId.map(tagId => {
							let tag = _tagList.find(item => item.tagId === tagId) || {};
							const { tagName } = tag;
							return { tagId, tagName };
						})[0]
					);
					delete params.tagId;

					await fetchStore.request({ ...params, commandType: 1 }).getData();

					emit('refresh');
					state.visible = false;
					MUtils.toast('保存成功', MESSAGE_TYPE.success);
				} finally {
					state.loading = false;
				}
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			wecomUserId
		};
	}
});
</script>
