<template>
	<m-select
		mode="multiple"
		@click.stop="open"
		:value="selectedTags"
		:options="tagList"
		:open="false"
		:fieldNames="{ label: 'tagName', value: 'tagId' }"
		removeIcon=" "
	/>
	<m-form-item-rest>
		<pm-dialog
			v-model:visible="visible"
			:mask="false"
			title="标签筛选"
			width="680px"
			style="height: calc(100vh - 64px)"
			class="sjsj"
			@close="onCancel"
		>
			<div v-if="visible" class="int">
				<div class="sh">
					<div v-for="cate in tagGroup" :key="cate.groupId" style="margin-bottom: 10px">
						<div>{{ cate.groupName }}</div>
						<template v-for="tag in cate.dataList" :key="tag.tagId">
							<a-checkable-tag
								:checked="checkedTags.indexOf(tag.tagId) > -1"
								@change="checked => handleChange(tag.tagId, checked)"
							>
								{{ tag.tagName }}
							</a-checkable-tag>
						</template>
					</div>
				</div>
			</div>

			<template #footer>
				<m-button plain @click="onCancel">取消</m-button>
				<m-button type="primary" @click="onConfirm">确定</m-button>
			</template>
		</pm-dialog>
	</m-form-item-rest>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, watch } from 'vue';
import { MUtils } from 'admin-library';
import { CheckableTag } from 'ant-design-vue';

import { TagListStore } from '../../store';

function groupBy(list, key, label) {
	const newArr = [];
	list.forEach(item => {
		const value = item[key];
		let obj = newArr.find(item => item[key] === value);

		if (!obj) {
			obj = {
				[key]: item[key],
				[label]: item[label],
				dataList: []
			};
			newArr.push(obj);
		}
		obj.dataList.push(item);
	});
	return newArr;
}

let tagGroup = [];
let tagList = [];

export default defineComponent({
	components: { ACheckableTag: CheckableTag },
	props: {
		value: {
			type: Array,
			isRequired: true
		},
		multiple: {
			type: Boolean,
			default: false
		}
	},
	emits: ['update:value'],
	setup(props, { emit }) {
		const components = {};

		const constants = {};

		const state = reactive({
			tagGroup: [],
			tagList: [],
			checkedTags: [],
			selectedTags: [],

			loading: false,
			visible: false
		});

		const methods = {
			async open() {
				if (state.tagList.length === 0) {
					await methods.initTagGroup();
				}
				state.visible = true;
			},
			handleChange(tag: string, checked: boolean) {
				let nextCheckedTags = [];
				if (props.multiple) {
					const { checkedTags } = state;
					nextCheckedTags = checked ? [...checkedTags, tag] : checkedTags.filter(t => t !== tag);
				} else {
					nextCheckedTags = checked ? [tag] : [];
				}
				state.checkedTags = nextCheckedTags;
			},
			async initTagGroup() {
				if (tagList.length) {
					state.tagList = tagList;
					state.tagGroup = tagGroup;
					return;
				}
				const res = await TagListStore.request({ limit: 9999 }).getData();
				state.tagList = tagList = res;
				state.tagGroup = tagGroup = groupBy(res, 'groupId', 'groupName');
			},

			onConfirm() {
				const selectedTags = MUtils.deepClone(state.checkedTags);
				state.selectedTags = selectedTags;
				emit('update:value', selectedTags);
				state.visible = false;
			},
			onCancel() {
				state.checkedTags = MUtils.deepClone(props.value || []);
				state.visible = false;
			}
		};
		watch(
			() => props.value,
			() => {
				state.checkedTags = MUtils.deepClone(props.value || []);
				state.selectedTags = MUtils.deepClone(props.value || []);
			},
			{ immediate: true }
		);
		methods.initTagGroup();

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			...props
		};
	}
});
</script>

<style lang="less">
.sjsj {
	.ant-modal-content {
		overflow: hidden;
		height: 100%;
	}
	.ant-modal-body {
		overflow: hidden;
		height: calc(100% - 118px);
	}
	.m-dialog-body {
		overflow: hidden;
		height: 100%;
	}
	.int {
		display: flex;
		flex-direction: column;
		height: 100%;
	}
}
</style>
<style lang="less" scoped>
.sh {
	overflow-y: auto;
}
.ant-tag {
	padding: 3px 7px;
	margin-top: 8px;
	min-width: 96px;
	text-align: center;
	color: #5e5e5e;
	background-color: #f7f7f7;
}
.ant-tag-checkable-checked {
	color: #0081ff;
	background-color: #c5e8ff;
}
</style>
