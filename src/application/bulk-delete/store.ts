import { List, Delete, Create, Update, Publish } from '@/store/bulk-delete';
import { TagList } from '@/store/bulk-message';
import { ItemResponse } from './types';

export const ListStore = new List<Array<ItemResponse>>({});
export const DelStore = new Delete({});
export const CreateStore = new Create({});
export const UpdateStore = new Update({});

export const TagListStore = new TagList<Array<ItemResponse>>({});

export const PublishStore = new Publish({});
