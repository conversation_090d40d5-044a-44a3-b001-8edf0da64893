<template>
	<pm-effi :controller="controller">
		<pm-search>
			<pm-search-single
				:span="3"
				show-search
				optionFilterProp="employeeMixName"
				:antdProps="{
					placeholder: '员工',
					fieldNames: { label: 'employeeMixName', value: 'id' }
				}"
				data-index="employeeId"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '状态'
				}"
				data-index="status"
				xtype="SELECT"
			/>
		</pm-search>

		<pm-table
			:columns="COLUMNS"
			:use-custom-column="true"
			:operations-fixed="true"
			:operations-width="130"
			:sort-num="false"
			:scroll="{
				x: 'max-content'
			}"
		>
			<template #beforeCustom>
				<m-button type="primary" @click="onCreate">添加</m-button>
			</template>
			<template #operations="{ record }">
				<m-button type="link" @click="onEdit(record)" v-if="record.status === 1">编辑</m-button>
				<m-button type="link" @click="onLaunch(record)" v-if="record.status === 1">发送</m-button>
			</template>
		</pm-table>
	</pm-effi>

	<edit-comp ref="editRef" @refresh="onRefresh"></edit-comp>
</template>

<script lang="ts">
import { defineComponent, ref, reactive, toRefs } from 'vue';
import { MESSAGE_TYPE, MUtils, ModelController } from 'admin-library';

import EditComp from './comps/edit-dialog/index.vue';
import { confirmMessageBox } from '@/shard/utils';
import { COLUMNS } from './config';
import { STATUS_STORE } from './constant';
import { ListStore as EmployeeListStore } from '@/application/promoter/store';
import { ListStore, PublishStore } from './store';
import { ItemResponse } from './types';

export default defineComponent({
	components: {
		EditComp
	},
	setup() {
		const components = {
			editRef: ref<InstanceType<typeof EditComp>>()
		};

		const constants = {
			COLUMNS
		};

		const state = reactive({});

		const controller = new ModelController({
			table: {
				store: ListStore
			},
			search: {
				employeeId: {
					store: EmployeeListStore
				},
				status: {
					store: STATUS_STORE
				}
			}
		});
		controller.tableRequest();

		controller.search.employeeId.onRequest.use(params => {
			params = {
				...params,
				enableAutomaticHosting: true,
				limit: 9999
			};

			return params;
		});

		const methods = {
			onCreate() {
				components.editRef.value.open();
			},
			async onLaunch(row: ItemResponse) {
				await confirmMessageBox('确认发起任务吗？');

				await PublishStore.request({ id: row.id }).getData();
				methods.onRefresh();
				MUtils.toast('发起任务成功', MESSAGE_TYPE.success);
			},

			async onEdit(row: ItemResponse) {
				components.editRef.value.open(row);
			},

			onRefresh() {
				controller.tableRequest();
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			controller
		};
	}
});
</script>
