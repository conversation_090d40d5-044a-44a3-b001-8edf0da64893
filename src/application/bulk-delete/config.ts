import { TableColumn, TableDateFormat } from 'admin-library';
import { renderTextWithColor } from '@/shard/utils';
import { ColumnWidthEnum } from '@/shard/constant';
import { STATUS_OPTIONS } from './constant';

export const COLUMNS: TableColumn[] = [
	{
		title: '#',
		dataIndex: 'id',
		fixed: 'left'
	},
	{
		title: '企微账号',
		dataIndex: 'employeeName',
		fixed: 'left'
	},
	{
		title: '执行内容',
		dataIndex: 'commandParam'
	},
	{
		title: '推送状态',
		dataIndex: 'status',
		render: data => {
			return renderTextWithColor(data, STATUS_OPTIONS, { isTag: true });
		},
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '推送时间',
		dataIndex: 'executeTime',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	},
	{
		title: '创建人',
		dataIndex: 'createUserName',
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '创建时间',
		dataIndex: 'createTime',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	}
];
