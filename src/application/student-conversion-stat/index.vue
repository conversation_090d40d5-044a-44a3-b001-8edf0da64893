<template>
	<pm-effi :controller="controller">
		<pm-search>
			<pm-search-single
				:span="3"
				show-search
				optionFilterProp="employeeMixName"
				:antdProps="{
					placeholder: '销售',
					fieldNames: { label: 'employeeMixName', value: 'id' }
				}"
				data-index="saleEmployeeId"
				xtype="SELECT"
			/>
		</pm-search>
		<pm-table
			:columns="COLUMNS"
			:use-custom-column="true"
			:operations="false"
			:sort-num="false"
			:scroll="{
				x: 'max-content'
			}"
		>
			<template #dealPrice="{ record }">
				<div :class="{ danger: record.dealPrice && record.systemPrice - record.dealPrice >= 100 }">
					{{ record.dealPrice }}
				</div>
			</template>
		</pm-table>
	</pm-effi>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue';
import { ModelController } from 'admin-library';

import { renderTextWithColor } from '@/shard/utils';
import { COLUMNS } from './config';
import { SHOW_COLOR_OPTIONS } from '@/shard/constant';
import { ListStore as EmployeeListStore } from '@/application/promoter/store';
import { ListStore } from './store';

export default defineComponent({
	setup() {
		const components = {};

		const constants = {
			COLUMNS,
			SHOW_COLOR_OPTIONS
		};

		const state = reactive({});

		const controller = new ModelController({
			table: {
				store: ListStore
			},
			search: {
				saleEmployeeId: {
					store: EmployeeListStore
				}
			}
		});
		controller.tableRequest();

		controller.search.saleEmployeeId.onRequest.use(params => {
			params = {
				...params,
				station: 1,
				limit: 9999
			};

			return params;
		});

		const methods = {
			renderTextWithColor
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			controller
		};
	}
});
</script>
