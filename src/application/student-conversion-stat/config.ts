import { ColumnXtype, TableColumn, TableDateFormat } from 'admin-library';
import { formatTimeStr, renderCopyComp } from '@/shard/utils';
import { ColumnWidthEnum } from '@/shard/constant';
import { GENDER_MAP, EDUCATION_MAP } from '@/application/customer/constant';
import { CATEGORY_MAP } from '@/application/leads/constant';

export const COLUMNS: TableColumn[] = [
	{
		title: '姓名',
		dataIndex: 'name',
		fixed: 'left'
	},
	{
		title: '木仓Id',
		dataIndex: 'mucangId',
		render: data => {
			return renderCopyComp(data);
		},
		width: ColumnWidthEnum.TEXT3
	},
	{
		title: '微信昵称',
		dataIndex: 'wechatNickName'
	},
	{
		title: '性别',
		dataIndex: 'gender',
		render: data => {
			return GENDER_MAP[data];
		},
		width: ColumnWidthEnum.TEXT2
	},
	{
		title: '学历',
		dataIndex: 'education',
		render: data => {
			return EDUCATION_MAP[data];
		},
		width: ColumnWidthEnum.TEXT3
	},
	{
		title: '年龄',
		dataIndex: 'age',
		width: ColumnWidthEnum.TEXT2
	},
	{
		title: '考试城市',
		dataIndex: 'examCityName',
		width: ColumnWidthEnum.TEXT5
	},
	{
		title: '企微添加时间',
		dataIndex: 'wecomAddTime',
		type: 'date',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	},
	{
		title: '订单成交时间',
		dataIndex: 'protocolEffectiveTime',
		type: 'date',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	},
	{
		title: '成交周期',
		dataIndex: 'period',
		render: data => {
			return formatTimeStr(data, 'D天HH:mm:ss');
		},
		width: ColumnWidthEnum.TEXT6
	},
	{
		title: '用户分层',
		dataIndex: 'customerCategory',
		render: data => {
			return CATEGORY_MAP[data];
		},
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '建议成交价(元)',
		dataIndex: 'systemPrice',
		width: 120
	},
	{
		title: '私教订单价(元)',
		dataIndex: 'dealPrice',
		xtype: ColumnXtype.CUSTOM,
		width: 120
	},
	{
		title: '实际成交价(元)',
		dataIndex: 'orderPrice',
		width: 120
	},
	{
		title: '销售人员',
		dataIndex: 'salesName',
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '私教课时数',
		dataIndex: 'lessionPeroid',
		width: ColumnWidthEnum.TEXT5
	}
];
