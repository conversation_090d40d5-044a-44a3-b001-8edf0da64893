<template>
	<pm-effi :controller="controller">
		<pm-search>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '姓名'
				}"
				data-index="name"
				xtype="INPUT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '微信昵称'
				}"
				data-index="nickName"
				xtype="INPUT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '木仓Id'
				}"
				data-index="mucangId"
				xtype="INPUT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '学员Id'
				}"
				data-index="studentId"
				xtype="INPUT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '客户编码'
				}"
				data-index="customerNo"
				xtype="INPUT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '辅导科目'
				}"
				data-index="kemu"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '学情预警'
				}"
				data-index="learnSituation"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '学习方案'
				}"
				data-index="studyPlan"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '班型',
					fieldNames: { label: 'desc', value: 'code' }
				}"
				data-index="classTypeCode"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '建议约考状态'
				}"
				data-index="suggestExam"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '学习状态'
				}"
				data-index="status"
				xtype="SELECT"
			/>
			<pm-search-single>
				<template #custom>
					<teacher-select @onSearchLecturer="onSearchLecturer" data-index="lecturerId" comp-type="search" />
				</template>
			</pm-search-single>
			<pm-search-single
				v-model="supervisorId"
				:span="3"
				show-search
				optionFilterProp="employeeMixName"
				:antdProps="{
					placeholder: '督导老师',
					fieldNames: { label: 'employeeMixName', value: 'id' }
				}"
				data-index="supervisorId"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="3"
				show-search
				optionFilterProp="employeeMixName"
				:antdProps="{
					placeholder: '销售人员',
					fieldNames: { label: 'employeeMixName', value: 'id' }
				}"
				data-index="saleEmployeeId"
				xtype="SELECT"
			/>
			<pm-search-single :span="5">
				<template #custom>
					<date-range
						:antd-props="{
							placeholder: ['预约考试时间', '结束时间']
						}"
						data-index="startExamTime|endExamTime"
						:maxSpan="30"
						:rangeOptions="MonthRangeOptions"
					/>
				</template>
			</pm-search-single>
			<pm-search-single
				:span="5"
				:antd-props="{
					placeholder: ['入学时间', '结束时间']
				}"
				xtype="RANGEPICKER"
				data-index="createTimeFrom|createTimeTo"
			/>
		</pm-search>

		<pm-table
			:columns="COLUMNS"
			:use-custom-column="true"
			:operations-fixed="true"
			:operations-width="170"
			custom-column-id="student"
			:sort-num="false"
			:scroll="{
				x: 'max-content'
			}"
			:antd-props="{
				customRow: record => {
					return {
						onClick: () => {
							onClick(record);
						}
					};
				}
			}"
			:rowClassName="setInstallClassName"
		>
			<template #beforeCustom>
				<m-button type="primary" @click="onReceptionConfig">督导自动分配策略</m-button>
				<m-button type="primary" @click="onLayerConfig">学员分层规则配置</m-button>
			</template>
			<template #studyPlan="{ record }">
				<m-button type="link" @click="toChangeStudyPlan(record)">
					{{ STUDY_PLAN_MAP[record.studyPlan] || '更换' }}
				</m-button>
			</template>
			<template #cityName="{ record }">
				<div>{{ record.cityName }}</div>
				<span v-if="record.existLocalTiku" class="ant-tag ant-tag-danger">有地方题</span>
			</template>
			<template #totalMockExamCount="{ record }">
				<m-button type="link" @click="goMockPage(record)">
					{{ record.totalMockExamCount }}
				</m-button>
			</template>
			<template #wrongQuestionBook="{ record }">
				<m-button type="link" @click="goQuestionPage(record)">
					{{ record.wrongQuestionBook }}
				</m-button>
			</template>
			<template #abstract="{ record }">
				<m-button type="link" @click="viewCustomer(record)">查看</m-button>
			</template>
			<template #suggestExam="{ record }">
				<m-switch :checked="record.suggestExam" @change="onChangeSuggestExam(record)" disabled />
			</template>
			<template #reserveExamTime="{ record }">
				<div v-if="record.reserveExamTime">
					{{ formatDate(record.reserveExamTime, TableDateFormat.DAY) }}
				</div>
				<m-button type="link" v-else @click="onRealExam(record)">添加</m-button>
			</template>
			<template #examStatus="{ record }">
				<div
					v-if="record.examStatus === 3 || record.examStatus === 4"
					:class="record.examStatus === 3 ? 'danger' : 'success'"
				>
					{{ EXAM_STATUS_MAP[record.examStatus] }}({{ record.score }})
				</div>
				<m-button type="link" v-else @click="onRealExam(record)">
					{{ EXAM_STATUS_MAP[record.examStatus] }}
				</m-button>
			</template>
			<template #lessonPeriod="{ record }">
				<m-button type="link" @click="goOrderList(record)">{{ record.lessonPeriod }}</m-button>
			</template>
			<template #actualLessonPeriod="{ record }">
				<m-button type="link" @click="goCourseList(record)">{{ record.actualLessonPeriod }}</m-button>
			</template>
			<template #lecturerName="{ record }">
				<m-button type="link" v-if="record.lecturerId" @click="toLecturerSchedule(record)">
					{{ record.lecturerName }}({{ record.lecturerNickName }})
				</m-button>
				<!-- <m-button
					type="link"
					v-else-if="!(record.learnStatus === 11 || record.learnStatus === 20 || record.learnStatus === 30)"
					@click="toRecommendLecturer(record)"
				>
					去分配
				</m-button> -->
				<m-button
					v-else-if="record.learnStatus === 0 || record.learnStatus === 10"
					type="link"
					@click="onCourseInvite(record)"
				>
					代Ta约课
				</m-button>
			</template>
			<template #courseExercise="{ record }">
				<m-button type="link" @click="goCourseExercise(record)">查看</m-button>
			</template>
			<template #learnStatus="{ record }">
				<m-button
					type="link"
					v-if="record.learnStatus === 11 || record.learnStatus === 30"
					@click="viewStatusReason(LEARN_STATUS_MAP[record.learnStatus], record)"
				>
					<span
						v-html="
							renderTextWithColor(record.learnStatus, LEARN_STATUS_OPTIONS, {
								isReturnStr: true
							})
						"
					></span>
				</m-button>
				<template v-else>
					<span
						v-html="
							renderTextWithColor(record.learnStatus, LEARN_STATUS_OPTIONS, {
								isReturnStr: true
							})
						"
					></span>
				</template>
			</template>
			<template #studentRemark="{ record }">
				<m-button type="link" @click="viewStudentRemak(record)">查看</m-button>
			</template>

			<template #operations="{ record }">
				<m-dropdown trigger="click">
					<span class="primary cursor-pointer" @click.prevent>
						学习记录
						<DownOutlined />
					</span>
					<template #overlay>
						<m-menu>
							<m-menu-item @click="goMockPage(record)">模考记录</m-menu-item>
							<m-menu-item @click="goQuestionPage(record)">错题专项分布</m-menu-item>
							<m-menu-item @click="onRealExam(record)">真实考试记录</m-menu-item>
						</m-menu>
					</template>
				</m-dropdown>
				<m-button type="link" @click="openAutoRemind(record)">自动提醒</m-button>
				<m-dropdown trigger="click">
					<span class="primary cursor-pointer" @click.prevent>
						操作
						<DownOutlined />
					</span>
					<template #overlay>
						<m-menu>
							<m-menu-item v-if="record.learnStatus !== 30" @click="onTransfer(record)">
								转学其他科目
							</m-menu-item>
							<m-menu-item
								v-if="
									record.learnStatus !== 11 && record.learnStatus !== 20 && record.learnStatus !== 30
								"
								danger
								@click="onSuspend(record)"
							>
								停课
							</m-menu-item>
							<m-menu-item
								v-if="record.learnStatus !== 20 && record.learnStatus !== 30"
								danger
								@click="onTerminate(record)"
							>
								退学
							</m-menu-item>
							<m-menu-item @click="onPeriod(record)">修改课时数</m-menu-item>
							<m-menu-item @click="onClassType(record)">修改班型</m-menu-item>
							<m-menu-item @click="onAllocateSupervisor(record)">分配督导</m-menu-item>
							<m-menu-item v-if="record.learnStatus !== 20" @click="onGrad(record)">毕业</m-menu-item>
							<m-menu-item v-if="record.learnStatus === 11" @click="onRecover(record)">
								恢复上课
							</m-menu-item>
							<m-menu-item @click="viewPhone(record)">查看手机</m-menu-item>
							<m-menu-item danger @click="onRemoveLecturer(record)">移除讲师</m-menu-item>
							<m-menu-item @click="onCreateGroup(record)">拉群</m-menu-item>
							<m-menu-item @click="onSendWakeupMessage(record)">提醒回App</m-menu-item>
							<m-menu-item @click="onSetVip(record)">申请发VIP</m-menu-item>
							<m-menu-item @click="onApplyTempGroup(record)">查看学习群</m-menu-item>
						</m-menu>
					</template>
				</m-dropdown>
				<m-button type="link" @click="goScoreMonitor(record)">上课效果监测</m-button>
				<template v-if="record.learnStatus === 0 || record.learnStatus === 10">
					<m-button type="link" @click="onCourseCreate(record)">手动排课</m-button>
				</template>
			</template>
		</pm-table>
	</pm-effi>

	<view-comp ref="viewRef"></view-comp>
	<transfer-comp ref="transferRef" @refresh="onRefresh"></transfer-comp>
	<suspend-comp ref="suspendRef" @refresh="onRefresh"></suspend-comp>
	<terminate-comp ref="terminateRef" @refresh="onRefresh"></terminate-comp>
	<period-comp ref="periodRef" @refresh="onRefresh"></period-comp>
	<allocate-supervisor-comp ref="allocateSupervisorRef" @refresh="onRefresh"></allocate-supervisor-comp>
	<auto-remind-comp ref="autoRemindRef" @refresh="onRefresh"></auto-remind-comp>
	<reception-config-comp ref="receptionConfigRef" @refresh="onRefresh"></reception-config-comp>
	<layer-config-comp ref="layerConfigRef" @refresh="onRefresh"></layer-config-comp>
	<class-type-comp ref="classTypeRef" @refresh="onRefresh"></class-type-comp>
	<set-vip-comp ref="setVipRef" @refresh="onRefresh"></set-vip-comp>
	<send-wakeup-message-comp ref="sendWakeupMessageRef" @refresh="onRefresh"></send-wakeup-message-comp>
	<apply-temp-group-comp ref="applyTempGroupCompRef" @refresh="onRefresh"></apply-temp-group-comp>
	<course-batch-create-comp ref="courseBatchCreateRef" @refresh="onRefresh"></course-batch-create-comp>
	<course-invite-comp ref="courseInviteRef" @refresh="onRefresh"></course-invite-comp>
	<grad-comp ref="gradRef" @refresh="onRefresh"></grad-comp>
</template>

<script lang="ts">
import Dayjs from 'dayjs';
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { ModelController, MUtils, MESSAGE_TYPE, PaasPostMessage, TableDateFormat } from 'admin-library';
import { MonthRangeOptions } from '@paas/paas-library';
import { DownOutlined } from '@ant-design/icons-vue';
import { useRoute } from 'vue-router';
import { Store } from '@simplex/simple-store';

import TeacherSelect from '@/comp/teacher-select/index.vue';
import DateRange from '@/comp/date-range/index.vue';
import ViewComp from '@/application/leads/comps/view-dialog/index.vue';
import TransferComp from './comps/transfer-dialog/index.vue';
import SuspendComp from './comps/suspend-dialog/index.vue';
import TerminateComp from './comps/terminate-dialog/index.vue';
import PeriodComp from './comps/period-dialog/index.vue';
import AllocateSupervisorComp from './comps/allocate-supervisor-dialog/index.vue';
import AutoRemindComp from './comps/auto-remind-dialog/index.vue';
import ReceptionConfigComp from '@/application/customer/comps/reception-config-dialog/index.vue';
import LayerConfigComp from './comps/layer-config-dialog/index.vue';
import ClassTypeComp from './comps/class-type-dialog/index.vue';
import SetVipComp from './comps/set-vip-dialog/index.vue';
import SendWakeupMessageComp from './comps/send-wakeup-message-dialog/index.vue';
import ApplyTempGroupComp from './comps/apply-temp-group-dialog/index.vue';
import CourseBatchCreateComp from '@/application/courses/comps/batch-create-dialog/index.vue';
import CourseInviteComp from '@/application/course-invite/comps/edit-dialog/index.vue';
import GradComp from './comps/grad-dialog/index.vue';
import { confirmMessageBox, copyText, renderTextWithColor } from '@/shard/utils';
import { COLUMNS } from './config';
import { RADIO_STORE, EXAM_KEMU_STORE } from '@/shard/constant';
import {
	LEARN_STATUS_STORE,
	LEARN_STATUS_MAP,
	LEARN_STATUS_OPTIONS,
	EXAM_STATUS_MAP,
	STUDY_PLAN_MAP,
	STUDY_PLAN_STORE,
	WARNING_STORE
} from './constant';
import { ListStore as EmployeeListStore } from '@/application/promoter/store';
import {
	ListStore,
	RecoverStore,
	UpdateStatusStore,
	GetChangeLogStore,
	GetPhoneStore,
	CreateGroupStore,
	RemoveLecturerStore,
	GetSessionInfoStore,
	GetClassTypeStore
} from './store';
import { ItemResponse } from './types';

export default defineComponent({
	components: {
		TeacherSelect,
		DateRange,
		DownOutlined,
		ViewComp,
		TransferComp,
		SuspendComp,
		TerminateComp,
		PeriodComp,
		AllocateSupervisorComp,
		AutoRemindComp,
		ReceptionConfigComp,
		LayerConfigComp,
		ClassTypeComp,
		SetVipComp,
		SendWakeupMessageComp,
		ApplyTempGroupComp,
		CourseBatchCreateComp,
		CourseInviteComp,
		GradComp
	},
	setup() {
		const route = useRoute();
		const query = route.query;

		const components = {
			viewRef: ref<InstanceType<typeof ViewComp>>(),
			transferRef: ref<InstanceType<typeof TransferComp>>(),
			suspendRef: ref<InstanceType<typeof SuspendComp>>(),
			terminateRef: ref<InstanceType<typeof TerminateComp>>(),
			periodRef: ref<InstanceType<typeof PeriodComp>>(),
			allocateSupervisorRef: ref<InstanceType<typeof AllocateSupervisorComp>>(),
			autoRemindRef: ref<InstanceType<typeof AutoRemindComp>>(),
			receptionConfigRef: ref<InstanceType<typeof ReceptionConfigComp>>(),
			layerConfigRef: ref<InstanceType<typeof LayerConfigComp>>(),
			classTypeRef: ref<InstanceType<typeof ClassTypeComp>>(),
			setVipRef: ref<InstanceType<typeof SetVipComp>>(),
			sendWakeupMessageRef: ref<InstanceType<typeof SendWakeupMessageComp>>(),
			applyTempGroupCompRef: ref<InstanceType<typeof ApplyTempGroupComp>>(),
			courseBatchCreateRef: ref<InstanceType<typeof CourseBatchCreateComp>>(),
			courseInviteRef: ref<InstanceType<typeof CourseInviteComp>>(),
			gradRef: ref<InstanceType<typeof GradComp>>()
		};

		const constants = {
			MonthRangeOptions,
			COLUMNS,
			LEARN_STATUS_MAP,
			LEARN_STATUS_OPTIONS,
			EXAM_STATUS_MAP,
			STUDY_PLAN_MAP,
			TableDateFormat
		};

		let supervisorId;
		if (query.supervisorId) {
			supervisorId = +query.supervisorId;
		}
		let lecturerId;
		if (query.lecturerId) {
			lecturerId = +query.lecturerId;
		}

		const state = reactive({
			selectedId: 0,
			supervisorId,
			lecturerId
		});

		const controller = new ModelController({
			table: {
				store: ListStore
			},
			search: {
				kemu: {
					store: EXAM_KEMU_STORE
				},
				learnSituation: {
					store: WARNING_STORE
				},
				studyPlan: {
					store: STUDY_PLAN_STORE
				},
				classTypeCode: {
					store: GetClassTypeStore
				},
				suggestExam: {
					store: RADIO_STORE
				},
				status: {
					store: LEARN_STATUS_STORE
				},
				lecturerId: {
					store: new Store([])
				},
				supervisorId: {
					store: EmployeeListStore
				},
				saleEmployeeId: {
					store: EmployeeListStore
				}
			}
		});
		controller.tableRequest();

		controller.search.lecturerId.onResponse.use(data => {
			data.data = [{ lecturerMixName: '空', id: -1 }, ...data.data];
			return data;
		});

		controller.search.classTypeCode.onRequest.use(params => {
			params = {
				...params,
				limit: 9999
			};

			return params;
		});

		controller.search.supervisorId.onRequest.use(params => {
			params = {
				...params,
				station: 5,
				limit: 9999
			};

			return params;
		});

		controller.search.saleEmployeeId.onRequest.use(params => {
			params = {
				...params,
				station: 1,
				limit: 9999
			};

			return params;
		});

		controller.table.onRequest.use(params => {
			let sd = {};
			if (params.kemu) {
				sd = { carType: 'car' };
			}
			params = {
				...params,
				...sd
			};

			return params;
		});

		const methods = {
			renderTextWithColor,
			formatDate(date, template) {
				return Dayjs(date).format(template);
			},
			onClick(recored) {
				state.selectedId = recored.id;
			},
			setInstallClassName(record) {
				if (record.id === state.selectedId) {
					return 'selected-row';
				}
			},
			toChangeStudyPlan(row) {
				PaasPostMessage.post('navigation.to', '/#/study-plan-change-records', {
					title: '学习方案',
					query: {
						studentProfileId: row?.id,
						tutorKemu: row?.tutorKemu
					},
					extendData: {
						style: 'width: 980px; height: 90%; margin: 2% auto 0;'
					},
					target: '_popup'
				});
			},
			async viewCustomer(row: ItemResponse) {
				components.viewRef.value.viewCustomer({ studentProfileId: row.id });
			},
			onReceptionConfig() {
				components.receptionConfigRef.value.open();
			},
			onLayerConfig() {
				components.layerConfigRef.value.open();
			},
			async onSearchLecturer(list) {
				controller.search.lecturerId.updateStore(new Store(list));
			},
			viewStudentRemak(row: ItemResponse) {
				PaasPostMessage.post('navigation.to', '/#/student-remark-records', {
					title: '学员点评明细',
					query: {
						studentId: row?.studentId
					},
					extendData: {
						style: 'width: 960px; height: 600px; margin: 140px auto 0;'
					},
					target: '_popup'
				});
			},
			goMockPage(row: ItemResponse) {
				PaasPostMessage.post('navigation.to', '/#/mock-records', {
					title: '模考记录',
					query: {
						mucangId: row?.mucangId,
						from: '学员',
						createTime: row?.createTime
					},
					extendData: {
						style: 'width: 960px; height: 90%; margin: 2% auto 0;'
					},
					target: '_popup'
				});
			},
			goQuestionPage(row: ItemResponse) {
				PaasPostMessage.post('navigation.to', '/#/wrong-questions', {
					title: '错题专项分布',
					query: {
						mucangId: row?.mucangId,
						studyPlan: row?.studyPlan
					},
					extendData: {
						style: 'width: 960px; height: 90%; margin: 2% auto 0;'
					},
					target: '_popup'
				});
			},
			onRealExam(row: ItemResponse) {
				PaasPostMessage.post('navigation.to', '/#/real-exam-records', {
					title: '学员真实考试记录',
					query: {
						studentProfileId: row?.id,
						carType: row?.tiku,
						kemu: row?.tutorKemu
					},
					extendData: {
						style: 'width: 900px; height: 90%; margin: 2% auto 0;'
					},
					target: '_popup'
				});
			},
			toLecturerSchedule(row: ItemResponse) {
				PaasPostMessage.post('navigation.to', '/#/lecturer-schedule', {
					query: {
						studentId: row?.studentId,
						lecturerId: row?.lecturerId
					},
					target: '_tab'
				});
			},
			toRecommendLecturer(row: ItemResponse) {
				PaasPostMessage.post('navigation.to', '/#/recommend-lecturer', {
					query: {
						studentId: row?.studentId
					},
					target: '_tab'
				});
			},

			onTransfer(row: ItemResponse) {
				const { id, tutorKemu, lessonPeriod, consumeLessonPeriod } = row;
				components.transferRef.value.open({
					id,
					tutorKemu,
					balancePeriod: lessonPeriod - consumeLessonPeriod
				});
			},
			onSuspend(row: ItemResponse) {
				components.suspendRef.value.open({ studentProfileId: row.id });
			},
			onTerminate(row: ItemResponse) {
				components.terminateRef.value.open({ studentProfileId: row.id });
			},
			onPeriod(row: ItemResponse) {
				components.periodRef.value.open(row);
			},
			onClassType(row: ItemResponse) {
				components.classTypeRef.value.open({ studentProfileId: row.id });
			},
			onAllocateSupervisor(row: ItemResponse) {
				components.allocateSupervisorRef.value.open({ studentProfileId: row.id });
			},
			async onGrad(row: ItemResponse) {
				components.gradRef.value.open(row);
			},
			async onRecover(row: ItemResponse) {
				try {
					const confirmResult = await MUtils.confirm({
						title: '提示',
						content: `确认恢复上课吗？`,
						type: MESSAGE_TYPE.warning
					});
					if (confirmResult) {
						await RecoverStore.request({ id: row.id }).getData();
						MUtils.toast('恢复成功', MESSAGE_TYPE.success);
					}
				} catch (error) {
					console.error(error.message);
				} finally {
					methods.onRefresh();
				}
			},
			async onRemoveLecturer(row: ItemResponse) {
				await confirmMessageBox('确认要移除Ta的讲师吗？');

				await RemoveLecturerStore.request({ studentProfileId: row.id }).getData();
				methods.onRefresh();
				MUtils.toast('移除成功', MESSAGE_TYPE.success);
			},
			async onCreateGroup(row: ItemResponse) {
				await CreateGroupStore.request({ id: row.id }).getData();
				MUtils.toast('拉群成功', MESSAGE_TYPE.success);
			},
			onSendWakeupMessage(row: ItemResponse) {
				components.sendWakeupMessageRef.value.open({ id: row.id }, 1);
			},
			onSetVip(row: ItemResponse) {
				components.setVipRef.value.open(row);
			},
			async onApplyTempGroup(row: ItemResponse) {
				const { sessionKey, hasPermission } = await GetSessionInfoStore.request({
					studentId: row.studentId
				}).getData();
				if (hasPermission) {
					components.applyTempGroupCompRef.value.goMucangChat(sessionKey);
				} else {
					components.applyTempGroupCompRef.value.open({ studentId: row.studentId }, sessionKey);
				}
			},
			async onChangeSuggestExam(row: ItemResponse) {
				try {
					const confirmResult = await MUtils.confirm({
						title: '提示',
						content: `确认${row.suggestExam ? '取消' : ''}建议约考吗？`,
						type: MESSAGE_TYPE.warning
					});
					if (confirmResult) {
						await UpdateStatusStore.request({ suggestExam: !row.suggestExam, id: row.id }).getData();
						MUtils.toast(`${row.suggestExam ? '取消' : ''}建议约考成功`, MESSAGE_TYPE.success);
					}
				} catch (error) {
					console.error(error.message);
				} finally {
					methods.onRefresh();
				}
			},
			goCourseExercise(row: ItemResponse) {
				PaasPostMessage.post('navigation.to', '/#/course-exercise', {
					title: '学员课后作业',
					query: {
						studentProfileId: row.id
					},
					extendData: {
						style: 'width: 80%; height: 600px; margin: 140px auto 0;'
					},
					target: '_popup'
				});
			},
			goOrderList(row) {
				PaasPostMessage.post('navigation.to', '/#/protocol', {
					title: '学员私教订单',
					query: {
						customerNo: row?.customerNo
					},
					target: '_tab'
				});
			},
			goCourseList(row) {
				PaasPostMessage.post('navigation.to', '/#/courses', {
					title: '学员课程列表',
					query: {
						mucangId: row?.mucangId
					},
					target: '_tab'
				});
			},
			goScoreMonitor(row: ItemResponse) {
				PaasPostMessage.post('navigation.to', '/#/score-monitor', {
					title: '上课效果监测',
					query: {
						studentProfileId: row.id
					},
					extendData: {
						style: 'width: 80%; height: 90%; margin: 2% auto 0;'
					},
					target: '_popup'
				});
			},
			async viewStatusReason(typeName, row) {
				const v = await GetChangeLogStore.request({ studentProfileId: row.id }).getData();
				MUtils.alert({
					title: `查看${typeName}原因`,
					content: `${v.reason}`
				});
			},
			openAutoRemind(row: ItemResponse) {
				components.autoRemindRef.value.open({ studentProfileId: row.id });
			},
			async viewPhone(row: ItemResponse) {
				const viewData = (await GetPhoneStore.request({ studentId: row.studentId }).getData()) as any;

				const isConfirm = await MUtils.confirm({
					title: '学员手机号',
					content: viewData.value,
					type: MESSAGE_TYPE.success,
					confirmText: '复制',
					cancelText: '关闭'
				});
				if (isConfirm) {
					methods.copy(viewData.value);
				}
			},
			onCourseCreate(record) {
				components.courseBatchCreateRef.value.open({
					studentId: record.studentId
				});
			},
			onCourseInvite(record) {
				components.courseInviteRef.value.open({
					sno: record.sno
				});
			},

			copy(text) {
				if (!text) {
					return;
				}
				const ret = copyText(text);
				if (ret) {
					MUtils.toast('复制成功');
				}
			},

			onRefresh() {
				controller.tableRequest();
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			controller
		};
	}
});
</script>
