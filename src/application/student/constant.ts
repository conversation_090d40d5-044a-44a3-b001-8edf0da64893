import { getMapfromArray, getStorefromArray } from '@/shard/utils';
import { ColorEnum } from '@/shard/constant';

export const LEARN_STATUS_OPTIONS = [
	{
		label: '未开始',
		value: 0,
		styleclass: ColorEnum.volcano
	},
	{
		label: '学习中',
		value: 10,
		styleclass: ColorEnum.green
	},
	{
		label: '停课',
		value: 11,
		styleclass: ColorEnum.magenta
	},
	{
		label: '毕业',
		value: 20,
		styleclass: ColorEnum.info
	},
	{
		label: '退学',
		value: 30,
		styleclass: ColorEnum.info
	},
	{
		label: '已升级',
		value: 50,
		styleclass: ColorEnum.info
	},
	{
		label: '已改绑',
		value: 60,
		styleclass: ColorEnum.info
	}
];
export const LEARN_STATUS_MAP = getMapfromArray(LEARN_STATUS_OPTIONS);
export const LEARN_STATUS_STORE = getStorefromArray(LEARN_STATUS_OPTIONS);

export const EXAM_STATUS_OPTIONS = [
	{
		label: '待考',
		value: 1
	},
	{
		label: '未填写成绩',
		value: 2
	},
	{
		label: '挂科',
		value: 3
	},
	{
		label: '合格',
		value: 4
	},
	{
		label: '等待约考',
		value: 5
	}
];
export const EXAM_STATUS_MAP = getMapfromArray(EXAM_STATUS_OPTIONS);
export const EXAM_STATUS_STORE = getStorefromArray(EXAM_STATUS_OPTIONS);

export const STUDY_PLAN_OPTIONS = [
	{
		label: '标准教案80A',
		value: 2
	},
	{
		label: '标准教案70A',
		value: 4
	},
	{
		label: '标准教案60A',
		value: 3
	},
	{
		label: '标准教案50A',
		value: 7
	},
	{
		label: '考前冲刺1天',
		value: 5
	},
	{
		label: '考前冲刺2天',
		value: 6
	},
	{
		label: '强化方案',
		value: 1
	}
];
export const STUDY_PLAN_MAP = getMapfromArray(STUDY_PLAN_OPTIONS);
export const STUDY_PLAN_STORE = getStorefromArray(STUDY_PLAN_OPTIONS);

export const K4_STUDY_PLAN_OPTIONS = [
	{
		label: '标准教案80A',
		value: 2
	},
	{
		label: '标准教案60A',
		value: 3
	}
];

export const COND_TYPE_OPTIONS = [
	{
		label: '近五次模考平均分',
		value: 'pjf'
	},
	{
		label: '私教课时数',
		value: 'kss'
	}
];
export const COND_TYPE_MAP = getMapfromArray(COND_TYPE_OPTIONS);

export const SYMBOL_OPTIONS = [
	{
		label: '大于',
		value: 'gt'
	},
	{
		label: '大于等于',
		value: 'gte'
	},
	{
		label: '等于',
		value: 'eq'
	},
	{
		label: '小于',
		value: 'lt'
	},
	{
		label: '小于等于',
		value: 'lte'
	}
];
export const SYMBOL_MAP = getMapfromArray(SYMBOL_OPTIONS);

export const LAST_CLASS_OPTIONS = [
	{
		label: '2天',
		value: 2
	},
	{
		label: '5天',
		value: 5
	},
	{
		label: '7天',
		value: 7
	},
	{
		label: '15天',
		value: 15
	},
	{
		label: '30天',
		value: 30
	}
];
export const LAST_CLASS_MAP = getMapfromArray(LAST_CLASS_OPTIONS);
export const LAST_CLASS_STORE = getStorefromArray(LAST_CLASS_OPTIONS);

export const WARNING_OPTIONS = [
	{
		label: '课时预警',
		value: 10,
		styleclass: ColorEnum.orange
	},
	{
		label: '成绩无进步',
		value: 20,
		styleclass: ColorEnum.purple
	},
	{
		label: '配合度低',
		value: 30,
		styleclass: ColorEnum.geekblue
	},
	{
		label: '成绩进步过快',
		value: 40,
		styleclass: ColorEnum.green
	},
	{
		label: '教案更换',
		value: 50,
		styleclass: ColorEnum.red
	}
];
export const WARNING_MAP = getMapfromArray(WARNING_OPTIONS);
export const WARNING_STORE = getStorefromArray([{ label: '空', value: -1 }, ...WARNING_OPTIONS]);

export const TIMEPEROID_OPTIONS = [
	{
		label: '全天有空',
		value: 10
	},
	{
		label: '上午有空',
		value: 20
	},
	{
		label: '下午有空',
		value: 30
	},
	{
		label: '晚上有空',
		value: 40
	}
];
export const TIMEPEROID_MAP = getMapfromArray(TIMEPEROID_OPTIONS);
