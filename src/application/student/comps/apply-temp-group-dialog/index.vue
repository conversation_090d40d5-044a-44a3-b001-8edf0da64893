<template>
	<pm-dialog v-model:visible="visible" :title="title" width="480px">
		<template v-if="visible">
			<m-form
				ref="formRef"
				:model="formState"
				:colon="false"
				:label-col="{ style: { width: '95px' } }"
				autocomplete="off"
			>
				<m-form-item required>此学员群未分配给你，是否申请临时查看权限？</m-form-item>
				<m-form-item name="hours" required>
					<m-select v-model:value="formState.hours" :options="options.hoursList" />
				</m-form-item>
			</m-form>
		</template>

		<template #footer>
			<m-button plain @click="visible = false">取消</m-button>
			<m-button :loading="loading" type="primary" @click="onConfirm">确定</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { MESSAGE_TYPE, MUtils } from 'admin-library';

import { cloneFromPick, cloneByMerge } from '@/shard/utils';
import { ApplyTempGroupStore } from '../../store';

const DefaultFormState = {
	studentId: null,
	hours: null
};

export default defineComponent({
	emits: ['refresh'],
	setup() {
		const components = {
			formRef: ref(null)
		};

		const constants = {};

		const state = reactive({
			visible: false,
			title: '查看学习群',
			loading: false,
			formState: MUtils.deepClone(DefaultFormState),
			options: {
				hoursList: [6, 12, 24, 48].map(item => {
					return {
						label: `${item}小时`,
						value: item
					};
				})
			},
			groupId: ''
		});

		const methods = {
			open(row: any, groupId) {
				state.formState = cloneFromPick(row, DefaultFormState);
				state.groupId = groupId;

				state.visible = true;
			},

			goMucangChat(groupId) {
				window.open(`${APP.domain['laofuzi']}/mucang-chat/?bizCode=10009#session-${groupId}`, '_blank');
			},

			async onConfirm() {
				await components.formRef.value.validate();

				try {
					state.loading = true;
					const params = cloneByMerge(state.formState, DefaultFormState);
					const fetchStore = ApplyTempGroupStore;

					await fetchStore.request(params).getData();

					state.visible = false;

					const isConfirm = await MUtils.confirm({
						title: '申请成功',
						type: MESSAGE_TYPE.success,
						confirmText: '跳转学习群',
						cancelText: '关闭'
					});
					if (isConfirm) {
						methods.goMucangChat(state.groupId);
					}
				} finally {
					state.loading = false;
				}
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods
		};
	}
});
</script>
