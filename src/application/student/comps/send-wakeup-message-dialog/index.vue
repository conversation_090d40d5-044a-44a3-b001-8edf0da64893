<template>
	<pm-dialog v-model:visible="visible" :title="title" width="480px">
		<template v-if="visible">
			<m-form
				ref="formRef"
				:model="formState"
				:colon="false"
				:label-col="{ style: { width: '95px' } }"
				autocomplete="off"
			>
				<m-form-item label="提醒方式" name="channels" required>
					<m-checkbox-group v-model:value="formState.channels">
						<m-checkbox
							style="display: flex; margin-left: 0"
							v-for="item in options.typeList"
							:key="item.value"
							:value="item.value"
						>
							{{ item.label }}
						</m-checkbox>
					</m-checkbox-group>
				</m-form-item>
			</m-form>
		</template>

		<template #footer>
			<m-button plain @click="visible = false">取消</m-button>
			<m-button :loading="loading" type="primary" @click="onConfirm">确定</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { MESSAGE_TYPE, MUtils } from 'admin-library';

import { cloneFromPick, cloneByMerge } from '@/shard/utils';
import { WakeUpStudentStore } from '@/application/student/store';
import { WakeUpLecturerStore } from '@/application/part-time-teacher/store';

const typeList = [
	{
		label: '短信 + 服务号模板消息',
		value: '1,3'
	},
	{
		label: '电话',
		value: '2'
	}
];

const DefaultFormState = {
	id: null,
	channels: ''
};

export default defineComponent({
	emits: ['refresh'],
	setup(props, { emit }) {
		const components = {
			formRef: ref(null)
		};

		const constants = {};

		const state = reactive({
			visible: false,
			title: '提醒回App',
			loading: false,
			formState: MUtils.deepClone(DefaultFormState),
			options: {
				typeList
			},
			type: 1
		});

		const methods = {
			open(row: any, type?: number) {
				state.formState = cloneFromPick(row, DefaultFormState);

				state.visible = true;

				state.type = type || 1;
			},

			async onConfirm() {
				await components.formRef.value.validate();

				try {
					state.loading = true;
					const params = cloneByMerge(state.formState, DefaultFormState);
					const fetchStore = state.type === 1 ? WakeUpStudentStore : WakeUpLecturerStore;

					params.channels = params.channels.join(',');

					await fetchStore.request(params).getData();

					emit('refresh');
					state.visible = false;
					MUtils.toast('提醒成功', MESSAGE_TYPE.success);
				} finally {
					state.loading = false;
				}
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods
		};
	}
});
</script>
