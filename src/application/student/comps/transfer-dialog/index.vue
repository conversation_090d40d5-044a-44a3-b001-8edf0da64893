<template>
	<pm-dialog v-model:visible="visible" :title="title" width="680px">
		<template v-if="visible">
			<m-form
				ref="formRef"
				:model="formState"
				:colon="false"
				:label-col="{ style: { width: '95px' } }"
				autocomplete="off"
			>
				<m-form-item label="当前辅导科目" name="curTutorKemu">
					<span>{{ getCurrentKemuLabel() }}</span>
				</m-form-item>
				<m-form-item label="剩余课时数" name="balancePeriod">
					<span>{{ balancePeriod }}（私教课时数 - 消耗课时数）</span>
				</m-form-item>
				<m-form-item label="转学科目" name="tutorKemu" required>
					<m-select
						:options="
							options.kemuList.map(item => {
								return { ...item, disabled: item.value === curTutorKemu };
							})
						"
						v-model:value="formState.tutorKemu"
						:allowClear="true"
						placeholder="请选择转学科目"
					/>
				</m-form-item>
				<m-form-item label="转学课时数" name="lessonPeriod" required>
					<m-input-number
						v-model:value="formState.lessonPeriod"
						:max="balancePeriod"
						placeholder="请输入转学课时数"
					/>
				</m-form-item>
			</m-form>
		</template>

		<template #footer>
			<m-button plain @click="visible = false">取消</m-button>
			<m-button :loading="loading" type="primary" @click="onConfirm">确认转学</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { MESSAGE_TYPE, MUtils } from 'admin-library';

import { cloneFromPick, cloneByMerge } from '@/shard/utils';
import { EXAM_KEMU_OPTIONS, EXAM_KEMU_MAP } from '@/shard/constant';

import { TransferSubjectStore } from '../../store';

const DefaultFormState = {
	studentProfileId: null,
	tutorKemu: null,
	lessonPeriod: null
};

export default defineComponent({
	emits: ['refresh'],
	setup(props, { emit }) {
		const components = {
			formRef: ref(null)
		};

		const constants = {};

		const state = reactive({
			visible: false,
			title: '转学其他科目',
			loading: false,
			formState: MUtils.deepClone(DefaultFormState),
			options: {
				kemuList: EXAM_KEMU_OPTIONS
			},
			curTutorKemu: 0,
			balancePeriod: 0
		});

		const methods = {
			open(row: any) {
				state.formState = cloneFromPick(row, DefaultFormState);

				state.formState.lessonPeriod = row.balancePeriod;
				state.formState.studentProfileId = row.id;
				state.curTutorKemu = row.tutorKemu;
				state.balancePeriod = row.balancePeriod;
				state.visible = true;
			},	

			getCurrentKemuLabel() {
				return EXAM_KEMU_MAP[state.curTutorKemu] || '未知';
			},

			async onConfirm() {
				await components.formRef.value.validate();

				// 校验转学科目不能和当前科目相同
				if (state.formState.tutorKemu === state.curTutorKemu) {
					MUtils.toast('不能转学和当前辅导科目相同的科目！', MESSAGE_TYPE.error);
					return;
				}

				// 校验转学课时数不能大于剩余课时数
				if (state.formState.lessonPeriod > state.balancePeriod) {
					MUtils.toast('转学课时数不能大于当前档案剩余课时数！', MESSAGE_TYPE.error);
					return;
				}

				try {
					state.loading = true;
					const params = cloneByMerge(state.formState, DefaultFormState);

					await TransferSubjectStore.request(params).getData();

					emit('refresh');
					state.visible = false;
					MUtils.toast('转学成功', MESSAGE_TYPE.success);
				} finally {
					state.loading = false;
				}
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods
		};
	}
});
</script>
