<template>
	<pm-dialog v-model:visible="visible" :title="title" width="680px">
		<template v-if="visible">
			<m-form
				ref="formRef"
				:model="formState"
				:colon="false"
				:label-col="{ style: { width: '95px' } }"
				autocomplete="off"
			>
				<m-form-item label="督导老师" name="employeeId" required>
					<m-select
						show-search
						optionFilterProp="employeeMixName"
						v-model:value="formState.employeeId"
						:options="options.employeeList"
						:fieldNames="{ label: 'employeeMixName', value: 'id' }"
					/>
				</m-form-item>
			</m-form>
		</template>

		<template #footer>
			<m-button plain @click="visible = false">取消</m-button>
			<m-button :loading="loading" type="primary" @click="onConfirm">确定</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { MESSAGE_TYPE, MUtils } from 'admin-library';

import { cloneFromPick, cloneByMerge } from '@/shard/utils';
import { ListStore as EmployeeListStore } from '@/application/promoter/store';
import { AllocateSupervisorUpdateStore } from '../../store';

const DefaultFormState = {
	studentProfileId: null,
	employeeId: ''
};

export default defineComponent({
	emits: ['refresh'],
	setup(props, { emit }) {
		const components = {
			formRef: ref(null)
		};

		const constants = {};

		const state = reactive({
			visible: false,
			title: '分配督导老师',
			loading: false,
			formState: MUtils.deepClone(DefaultFormState),
			options: {
				employeeList: []
			}
		});

		const methods = {
			open(row) {
				state.formState = cloneFromPick(row, DefaultFormState);

				state.visible = true;

				methods.initEmployeeOptions();
			},

			async initEmployeeOptions() {
				let res = await EmployeeListStore.request({
					station: 5,
					status: 20,
					limit: 9999
				}).getData();

				if (res) {
					state.options.employeeList = res;
				}
			},
			async onConfirm() {
				await components.formRef.value.validate();

				try {
					state.loading = true;
					const params = cloneByMerge(state.formState, DefaultFormState);
					const fetchStore = AllocateSupervisorUpdateStore;

					await fetchStore.request(params).getData();

					emit('refresh');
					state.visible = false;
					MUtils.toast('保存成功', MESSAGE_TYPE.success);
				} finally {
					state.loading = false;
				}
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods
		};
	}
});
</script>
