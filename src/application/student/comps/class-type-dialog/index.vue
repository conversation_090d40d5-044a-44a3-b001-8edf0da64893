<template>
	<pm-dialog v-model:visible="visible" :title="title" width="680px">
		<template v-if="visible">
			<m-form
				ref="formRef"
				:model="formState"
				:colon="false"
				:label-col="{ style: { width: '95px' } }"
				autocomplete="off"
			>
				<m-form-item label="班型" name="classTypeCode" required>
					<m-select
						show-search
						optionFilterProp="desc"
						v-model:value="formState.classTypeCode"
						:options="options.classTypeList"
						:fieldNames="{ label: 'desc', value: 'code' }"
					/>
				</m-form-item>
			</m-form>
		</template>

		<template #footer>
			<m-button plain @click="visible = false">取消</m-button>
			<m-button :loading="loading" type="primary" @click="onConfirm">确定</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { MESSAGE_TYPE, MUtils } from 'admin-library';

import { cloneFromPick, cloneByMerge } from '@/shard/utils';
import { GetClassTypeStore, ChangeClassTypeStore } from '../../store';

const DefaultFormState = {
	studentProfileId: null,
	classTypeCode: ''
};

export default defineComponent({
	emits: ['refresh'],
	setup(props, { emit }) {
		const components = {
			formRef: ref(null)
		};

		const constants = {};

		const state = reactive({
			visible: false,
			title: '修改班型',
			loading: false,
			formState: MUtils.deepClone(DefaultFormState),
			options: {
				classTypeList: []
			}
		});

		const methods = {
			open(row: any) {
				state.formState = cloneFromPick(row, DefaultFormState);

				state.visible = true;

				methods.initClassTypeOptions();
			},

			async initClassTypeOptions() {
				let res = await GetClassTypeStore.request({
					limit: 9999
				}).getData();
				if (res) {
					state.options.classTypeList = res;
				}
			},
			async onConfirm() {
				await components.formRef.value.validate();

				try {
					state.loading = true;
					const params = cloneByMerge(state.formState, DefaultFormState);
					const fetchStore = ChangeClassTypeStore;

					await fetchStore.request(params).getData();

					emit('refresh');
					state.visible = false;
					MUtils.toast('保存成功', MESSAGE_TYPE.success);
				} finally {
					state.loading = false;
				}
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods
		};
	}
});
</script>
