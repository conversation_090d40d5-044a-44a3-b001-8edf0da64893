<template>
	<pm-dialog v-model:visible="visible" :title="title" width="480px">
		<template v-if="visible">
			<m-form
				ref="formRef"
				:model="formState"
				:colon="false"
				:label-col="{ style: { width: '95px' } }"
				autocomplete="off"
			>
				<m-form-item>
					确认要将学员
					<span class="warning">【{{ formState.name }}】</span>
					的
					<span class="warning">【{{ CAR_TYPE_MAP[formState.tiku] }}】</span>
					<span class="warning">【{{ EXAM_KEMU_MAP[formState.tutorKemu] }}】</span>
					档案标记为毕业吗？
				</m-form-item>
				<m-form-item>
					<m-checkbox v-model:checked="confirm">我已反复确认操作无误</m-checkbox>
				</m-form-item>
			</m-form>
		</template>

		<template #footer>
			<m-button plain @click="visible = false">取消</m-button>
			<m-button :loading="loading" :disabled="!confirm" type="primary" @click="onConfirm">确定</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { MESSAGE_TYPE, MUtils } from 'admin-library';

import { cloneFromPick, cloneByMerge } from '@/shard/utils';
import { EXAM_KEMU_MAP, CAR_TYPE_MAP } from '@/shard/constant';
import { GradStore } from '../../store';

const DefaultFormState = {
	id: null,
	name: '',
	tiku: '',
	tutorKemu: null
};

export default defineComponent({
	emits: ['refresh'],
	setup(props, { emit }) {
		const components = {
			formRef: ref(null)
		};

		const constants = {
			EXAM_KEMU_MAP,
			CAR_TYPE_MAP
		};

		const state = reactive({
			visible: false,
			title: '确认毕业',
			loading: false,
			formState: MUtils.deepClone(DefaultFormState),
			confirm: false
		});

		const methods = {
			open(row: any) {
				state.formState = cloneFromPick(row, DefaultFormState);

				state.visible = true;

				state.confirm = false;
			},

			async onConfirm() {
				await components.formRef.value.validate();

				try {
					state.loading = true;
					const params = cloneByMerge(state.formState, DefaultFormState);
					const fetchStore = GradStore;

					await fetchStore.request({ id: params.id }).getData();

					emit('refresh');
					state.visible = false;
					MUtils.toast('操作成功', MESSAGE_TYPE.success);
				} finally {
					state.loading = false;
				}
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods
		};
	}
});
</script>
