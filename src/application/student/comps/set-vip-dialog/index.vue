<template>
	<pm-dialog v-model:visible="visible" :title="title" width="680px">
		<template v-if="visible">
			<m-form
				ref="formRef"
				:model="formState"
				:colon="false"
				:label-col="{ style: { width: '95px' } }"
				autocomplete="off"
			>
				<m-form-item label="木仓Id" name="userId">
					<m-input v-model:value="formState.userId" placeholder="木仓Id" disabled />
				</m-form-item>
				<m-form-item label="发放VIP" name="configCode" required>
					<m-select
						show-search
						optionFilterProp="configCode"
						v-model:value="formState.configCode"
						:options="options.vipList"
						:fieldNames="{ label: 'configName', value: 'configCode' }"
					/>
				</m-form-item>
				<m-form-item label="备注" name="remark">
					<m-textarea v-model:value="formState.remark" :rows="3" />
				</m-form-item>
			</m-form>
		</template>

		<template #footer>
			<m-button plain @click="visible = false">取消</m-button>
			<m-button :loading="loading" type="primary" @click="onConfirm">确定</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { MESSAGE_TYPE, MUtils } from 'admin-library';

import { cloneByMerge } from '@/shard/utils';
import { SendVipStore, GetVipListStore } from '../../store';

const DefaultFormState = {
	userId: '',
	configCode: '',
	remark: ''
};

export default defineComponent({
	emits: ['refresh'],
	setup(props, { emit }) {
		const components = {
			formRef: ref(null)
		};

		const constants = {};

		const state = reactive({
			visible: false,
			title: '申请发VIP',
			loading: false,
			formState: MUtils.deepClone(DefaultFormState),
			options: {
				vipList: []
			}
		});

		const methods = {
			open(row: any) {
				let { mucangId } = row;
				state.formState = MUtils.deepClone(DefaultFormState);
				state.formState.userId = mucangId;

				state.visible = true;

				methods.initVipListOptions();
			},

			async initVipListOptions() {
				let res = await GetVipListStore.request({
					limit: 9999,
					_lang: 'all',
					enabled: true
				}).getData();
				if (res) {
					state.options.vipList = res;
				}
			},
			async onConfirm() {
				await components.formRef.value.validate();

				try {
					state.loading = true;
					const params = cloneByMerge(state.formState, DefaultFormState);
					const fetchStore = SendVipStore;

					await fetchStore
						.request({
							giftScene: '2',
							detailCreateParams: [
								{
									bizType: '1',
									...params
								}
							]
						})
						.getData();

					emit('refresh');
					state.visible = false;
					MUtils.toast('保存成功', MESSAGE_TYPE.success);
				} finally {
					state.loading = false;
				}
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods
		};
	}
});
</script>
