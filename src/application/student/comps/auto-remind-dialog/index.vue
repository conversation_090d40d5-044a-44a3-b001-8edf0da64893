<template>
	<pm-dialog v-model:visible="visible" :title="title" width="680px">
		<template v-if="visible">
			<m-form
				ref="formRef"
				:model="formState"
				:colon="false"
				:label-col="{ style: { width: '95px' } }"
				autocomplete="off"
			>
				<m-form-item label="自动提醒" name="enable">
					<m-switch v-model:checked="formState.enable" />
				</m-form-item>
				<m-form-item label="提醒节点" name="notifyNodes">
					<m-checkbox-group v-model:value="formState.notifyNodes">
						<m-checkbox :value="1">开始上课提醒</m-checkbox>
						<br />
						<m-checkbox :value="2">完成课后作业提醒</m-checkbox>
						<br />
						<m-checkbox :value="3">参加真实考试提醒</m-checkbox>
						<br />
						<m-checkbox :value="4">课后作业提醒</m-checkbox>
					</m-checkbox-group>
				</m-form-item>
			</m-form>
		</template>

		<template #footer>
			<m-button plain @click="visible = false">取消</m-button>
			<m-button :loading="loading" type="primary" @click="onConfirm">确定</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { MESSAGE_TYPE, MUtils } from 'admin-library';

import { cloneFromPick, cloneByMerge } from '@/shard/utils';
import { GetNotifyConfigStore, SetNotifyConfigStore } from '../../store';

const DefaultFormState = {
	studentProfileId: null,
	enable: false,
	notifyNodes: []
};

export default defineComponent({
	emits: ['refresh'],
	setup(props, { emit }) {
		const components = {
			formRef: ref(null)
		};

		const constants = {};

		const state = reactive({
			visible: false,
			title: '自动提醒配置',
			loading: false,
			formState: MUtils.deepClone(DefaultFormState)
		});

		const methods = {
			async open(row) {
				const t = await GetNotifyConfigStore.request({ studentProfileId: row.studentProfileId }).getData();
				state.formState = cloneFromPick({ ...row, ...t }, DefaultFormState);

				state.visible = true;
			},
			async onConfirm() {
				await components.formRef.value.validate();

				try {
					state.loading = true;
					let params = cloneByMerge(state.formState, DefaultFormState);
					const fetchStore = SetNotifyConfigStore;

					let { studentProfileId, enable, notifyNodes } = params;
					params = {
						studentProfileId,
						configValue: JSON.stringify({
							enable,
							notifyNodes
						})
					};

					await fetchStore.request(params).getData();

					emit('refresh');
					state.visible = false;
					MUtils.toast('保存成功', MESSAGE_TYPE.success);
				} finally {
					state.loading = false;
				}
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods
		};
	}
});
</script>
