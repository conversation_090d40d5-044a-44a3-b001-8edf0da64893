<template>
	<pm-dialog v-model:visible="visible" :title="title" width="540px">
		<template v-if="visible">
			<m-form
				ref="formRef"
				:model="formState"
				:colon="false"
				:label-col="{ style: { width: '95px' } }"
				autocomplete="off"
			>
				<m-form-item>
					<div
						v-for="(dayItem, dayIndex) in formState.itemList"
						:key="dayIndex"
						style="margin-bottom: 10px; background-color: #f2f2f2"
					>
						<m-row justify="space-between" align="middle" style="padding: 8px 8px 10px">
							<m-col>
								<div style="font-size: 14px; line-height: 1.6">用户分层{{ dayIndex + 1 }}</div>
							</m-col>
							<m-col>
								<m-button
									v-if="formState.itemList.length > 1"
									danger
									type="link"
									@click="removeDayItem(dayIndex)"
								>
									删除
								</m-button>
							</m-col>
						</m-row>
						<m-divider style="margin: 0 0 10px; height: 2px; background-color: #fff" />
						<div style="padding: 0 20px 10px">
							<div style="margin-bottom: 10px">
								<div style="margin-bottom: 6px">分层名称</div>
								<div>
									<m-form-item
										:name="['itemList', dayIndex, 'category']"
										label="分层名称"
										:labelCol="{ span: 0 }"
										required
									>
										<m-input v-model:value="dayItem.category" placeholder="分层名称" />
									</m-form-item>
								</div>
							</div>
							<div style="margin-bottom: 10px">
								<div style="margin-bottom: 6px">分层条件</div>
								<div style="padding: 10px 10px 1px; margin-bottom: 10px; background-color: #fff">
									<div
										v-for="(item, index) in dayItem.conditionList"
										:key="index + item.code + item.operator"
									>
										<m-row justify="space-between" align="middle" style="margin: 0 0 10px">
											<m-col>
												<div style="font-size: 14px; line-height: 1.6">条件{{ index + 1 }}</div>
											</m-col>
											<m-col>
												<m-button
													v-if="dayItem.conditionList.length > 1"
													danger
													type="link"
													@click="removeItem(dayIndex, index)"
												>
													删除
												</m-button>
											</m-col>
										</m-row>
										<div>
											<m-space>
												<m-form-item
													:name="['itemList', dayIndex, 'conditionList', index, 'code']"
													label="条件"
													:labelCol="{ span: 0 }"
													required
												>
													<m-select
														style="width: 180px"
														v-model:value="item.code"
														:options="options.typeList"
													/>
												</m-form-item>
												<m-form-item
													:name="['itemList', dayIndex, 'conditionList', index, 'operator']"
													label="符号"
													:labelCol="{ span: 0 }"
													required
												>
													<m-select
														style="width: 100px"
														v-model:value="item.operator"
														:options="options.symbolList"
													/>
												</m-form-item>
												<m-form-item
													:name="['itemList', dayIndex, 'conditionList', index, 'value']"
													label="条件值"
													:labelCol="{ span: 0 }"
													required
												>
													<m-input
														style="width: 120px"
														v-model:value="item.value"
														placeholder="条件值"
													/>
												</m-form-item>
											</m-space>
										</div>
									</div>
									<m-button
										style="margin-top: 0; margin-bottom: 8px; width: 100%"
										type="dashed"
										@click="addItem(dayIndex)"
									>
										<template #icon><PlusOutlined /></template>
										继续添加
									</m-button>
								</div>
							</div>
						</div>
					</div>
					<m-button style="margin-top: 0; margin-bottom: 8px; width: 100%" type="dashed" @click="addDayItem">
						<template #icon><PlusOutlined /></template>
						继续添加
					</m-button>
				</m-form-item>
			</m-form>
		</template>

		<template #footer>
			<m-button plain @click="visible = false">取消</m-button>
			<m-button :loading="loading" type="primary" @click="onConfirm">确定</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { MESSAGE_TYPE, MUtils } from 'admin-library';

import { cloneFromPick, cloneByMerge } from '@/shard/utils';
import { COND_TYPE_OPTIONS, SYMBOL_OPTIONS } from '../../constant';
import { SetLayerConfigStore, GetLayerConfigStore } from '../../store';

const DefaultFormState = {
	itemList: [
		{
			category: '',
			conditionList: [
				{
					code: '',
					operator: '',
					value: ''
				},
				{
					code: '',
					operator: '',
					value: ''
				}
			]
		}
	]
};

export default defineComponent({
	components: {},
	emits: ['refresh'],
	setup(props, { emit }) {
		const components = {
			formRef: ref(null)
		};

		const constants = {};

		const state = reactive({
			visible: false,
			title: '学员分层规则配置',
			loading: false,
			formState: MUtils.deepClone(DefaultFormState),
			options: {
				typeList: COND_TYPE_OPTIONS,
				symbolList: SYMBOL_OPTIONS
			}
		});

		const methods = {
			removeDayItem(index) {
				state.formState.itemList.splice(index, 1);
			},
			async addDayItem() {
				await components.formRef.value.validate();

				state.formState.itemList.push({
					category: '',
					conditionList: [
						{
							code: '',
							operator: '',
							value: ''
						},
						{
							code: '',
							operator: '',
							value: ''
						}
					]
				});
			},
			removeItem(dayIndex, index) {
				state.formState.itemList[dayIndex].conditionList.splice(index, 1);
			},
			async addItem(dayIndex) {
				await components.formRef.value.validate();

				state.formState.itemList[dayIndex].conditionList.push({
					messageData: {
						messageType: 1,
						messageContent: {}
					}
				});
			},
			async open() {
				let itemList: any = await GetLayerConfigStore.request().getData();

				let row = {};
				if (itemList) {
					row = { itemList };
				}

				state.formState = cloneFromPick(row, DefaultFormState);

				state.visible = true;
			},

			async onConfirm() {
				await components.formRef.value.validate();

				try {
					state.loading = true;
					const params = cloneByMerge(state.formState, DefaultFormState);
					const fetchStore = SetLayerConfigStore;

					await fetchStore.request(params).getData();

					emit('refresh');
					state.visible = false;
					MUtils.toast('保存成功', MESSAGE_TYPE.success);
				} finally {
					state.loading = false;
				}
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods
		};
	}
});
</script>
