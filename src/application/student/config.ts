import { createVNode } from 'vue';
import { ColumnXtype, TableColumn, TableDateFormat } from 'admin-library';
import { renderTextWithColor, renderCopyComp } from '@/shard/utils';
import { ColumnWidthEnum, CAR_TYPE_MAP, TUROR_KEMU_MAP } from '@/shard/constant';
import { GENDER_MAP, EDUCATION_MAP } from '@/application/customer/constant';
import { WARNING_OPTIONS, TIMEPEROID_MAP } from './constant';

export const COLUMNS: TableColumn[] = [
	{
		title: '#',
		dataIndex: 'id',
		fixed: 'left'
	},
	{
		title: '姓名',
		dataIndex: 'name',
		fixed: 'left'
	},
	{
		title: '学员Id',
		dataIndex: 'studentId',
		width: ColumnWidthEnum.TEXT3
	},
	{
		title: 'sno',
		dataIndex: 'sno'
	},
	{
		title: '学习方案',
		dataIndex: 'studyPlan',
		xtype: ColumnXtype.CUSTOM,
		width: ColumnWidthEnum.TEXT5
	},
	{
		title: '班型',
		dataIndex: 'classTypeName'
	},
	{
		title: '木仓Id',
		dataIndex: 'mucangId',
		render: data => {
			return renderCopyComp(data);
		},
		width: ColumnWidthEnum.TEXT3
	},
	{
		title: '木仓昵称',
		dataIndex: 'mucangNickName'
	},
	{
		title: '微信昵称',
		dataIndex: 'nickName'
	},
	{
		title: '性别',
		dataIndex: 'gender',
		render: data => {
			return GENDER_MAP[data];
		},
		width: ColumnWidthEnum.TEXT2
	},
	{
		title: '学历',
		dataIndex: 'education',
		render: data => {
			return EDUCATION_MAP[data];
		},
		width: ColumnWidthEnum.TEXT3
	},
	{
		title: '年龄',
		dataIndex: 'age',
		width: ColumnWidthEnum.TEXT2
	},
	{
		title: '考试城市',
		dataIndex: 'cityName',
		xtype: ColumnXtype.CUSTOM,
		width: ColumnWidthEnum.TEXT5
	},
	{
		title: '车型',
		dataIndex: 'tiku',
		render: data => {
			return CAR_TYPE_MAP[data];
		},
		width: ColumnWidthEnum.TEXT5
	},
	{
		title: '辅导科目',
		dataIndex: 'tutorKemu',
		render: data => {
			return TUROR_KEMU_MAP[data];
		},
		width: ColumnWidthEnum.TEXT5
	},
	{
		title: '学习概况',
		dataIndex: 'abstract',
		xtype: ColumnXtype.CUSTOM,
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '建议约考',
		dataIndex: 'suggestExam',
		xtype: ColumnXtype.CUSTOM,
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '预约考试时间',
		dataIndex: 'reserveExamTime',
		xtype: ColumnXtype.CUSTOM,
		width: ColumnWidthEnum.TEXT6
	},
	{
		title: '真实考试情况',
		dataIndex: 'examStatus',
		xtype: ColumnXtype.CUSTOM,
		width: ColumnWidthEnum.TEXT6
	},
	{
		title: '私教课时数',
		dataIndex: 'lessonPeriod',
		xtype: ColumnXtype.CUSTOM,
		width: ColumnWidthEnum.TEXT5
	},
	{
		title: '实际课时数',
		dataIndex: 'actualLessonPeriod',
		xtype: ColumnXtype.CUSTOM,
		width: ColumnWidthEnum.TEXT5
	},
	{
		title: '消耗课时数',
		dataIndex: 'consumeLessonPeriod',
		width: ColumnWidthEnum.TEXT5
	},
	{
		title: '最后一次排课时间',
		dataIndex: 'latestCourseTime',
		type: 'date',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	},
	{
		title: '讲师',
		dataIndex: 'lecturerName',
		xtype: ColumnXtype.CUSTOM
	},
	{
		title: '课后作业',
		dataIndex: 'courseExercise',
		xtype: ColumnXtype.CUSTOM,
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '上课偏好时间段',
		dataIndex: 'conveninentClassTimePeroid',
		render: data => {
			if (data) {
				return data
					.split(',')
					.map(item => TIMEPEROID_MAP[item])
					.join('，');
			}
		},
		width: 120
	},
	{
		title: '学习状态',
		dataIndex: 'learnStatus',
		xtype: ColumnXtype.CUSTOM,
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '学员评语',
		dataIndex: 'studentRemark',
		xtype: ColumnXtype.CUSTOM,
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '学员分层',
		dataIndex: 'category',
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '入学平均分',
		dataIndex: 'entranceAvgScore',
		width: ColumnWidthEnum.TEXT5
	},
	{
		title: '学情预警',
		dataIndex: 'learnSituationList',
		render: data => {
			if (data && data.length) {
				return createVNode(
					'span',
					{},
					data?.map(item => {
						return renderTextWithColor(Number(item), WARNING_OPTIONS, { isTag: true });
					})
				);
			} else {
				return '';
			}
		},
		width: ColumnWidthEnum.TEXT5
	},
	{
		title: '销售人员',
		dataIndex: 'saleEmployeeMixName',
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '入学时间',
		dataIndex: 'createTime',
		dateFormat: TableDateFormat.DAY,
		width: ColumnWidthEnum.DATEDAY
	},
	{
		title: '督导老师',
		dataIndex: 'supervisorName',
		width: ColumnWidthEnum.TEXT4
	}
];
