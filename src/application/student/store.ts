import {
	List,
	Del,
	StudentList,
	UpdateStatus,
	UpdatePeriod,
	Recover,
	AllocateUpdate,
	SuspendUpdate,
	TerminateUpdate,
	AllocateSupervisorUpdate,
	Grad,
	GetChangeLog,
	UpdateScheduleCourseNotify,
	GetPhone,
	GetNotifyConfig,
	SetNotifyConfig,
	GetLayerConfig,
	SetLayerConfig,
	GetClassType,
	ChangeClassType,
	CreateGroup,
	RemoveLecturer,
	GetVipList,
	SendVip,
	WakeUpStudent,
	GetSessionInfo,
	ApplyTempGroup,
	TransferSubject
} from '@/store/student';
import { ItemResponse } from './types';

export const ListStore = new List<Array<ItemResponse>>({});
export const DelStore = new Del({});
export const StudentListStore = new StudentList<Array<ItemResponse>>({});

export const RecoverStore = new Recover({});
export const GradStore = new Grad({});

export const AllocateUpdateStore = new AllocateUpdate({});
export const SuspendUpdateStore = new SuspendUpdate({});
export const TerminateUpdateStore = new TerminateUpdate({});
export const AllocateSupervisorUpdateStore = new AllocateSupervisorUpdate({});

export const UpdateStatusStore = new UpdateStatus({});
export const UpdatePeriodStore = new UpdatePeriod({});

export const GetChangeLogStore = new GetChangeLog<ItemResponse>({});

export const UpdateScheduleCourseNotifyStore = new UpdateScheduleCourseNotify({});
export const GetPhoneStore = new GetPhone<ItemResponse>({});

export const GetNotifyConfigStore = new GetNotifyConfig<ItemResponse>({});
export const SetNotifyConfigStore = new SetNotifyConfig<ItemResponse>({});

export const GetLayerConfigStore = new GetLayerConfig<ItemResponse>({});
export const SetLayerConfigStore = new SetLayerConfig<ItemResponse>({
	ajaxOptions: {
		contentType: false,
		headers: { 'content-type': 'application/json;charset=UTF-8' }
	}
});

export const GetClassTypeStore = new GetClassType<Array<ItemResponse>>({});
export const ChangeClassTypeStore = new ChangeClassType({});

export const CreateGroupStore = new CreateGroup({});
export const RemoveLecturerStore = new RemoveLecturer({});

export const GetVipListStore = new GetVipList<Array<ItemResponse>>({});
export const SendVipStore = new SendVip({
	ajaxOptions: {
		contentType: false,
		headers: { 'content-type': 'application/json;charset=UTF-8' }
	}
});

export const WakeUpStudentStore = new WakeUpStudent({});
export const GetSessionInfoStore = new GetSessionInfo<{
	sessionKey: string;
	hasPermission: boolean;
}>({});
export const ApplyTempGroupStore = new ApplyTempGroup({});

export const TransferSubjectStore = new TransferSubject({});
