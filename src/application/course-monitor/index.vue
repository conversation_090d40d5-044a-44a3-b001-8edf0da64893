<template>
	<div style="position: absolute; top: 20px; right: 20px; bottom: 20px; left: 20px; padding: 20px">
		<div style="display: flex; justify-content: end; padding-right: 20px; width: 100%">
			<m-button style="margin-left: 8px" @click="onReset" :loading="loading">
				<template #icon><undo-outlined /></template>
			</m-button>
		</div>
		<template v-if="courseList.length">
			<div style="display: flex; height: calc(100% - 32px)">
				<div style="overflow-x: auto; flex-grow: 1; flex-basis: 300px">
					<div style="display: flex; flex-wrap: wrap">
						<div
							v-for="(item, index) in courseList"
							:offset="(index + 1) % 5 !== 1 ? 1 : 0"
							:key="item.courseId"
							style="margin-right: 16px; margin-bottom: 12px; width: 240px"
							:style="{ backgroundColor: item.courseId === currentCourseId ? '#e0ff91' : '' }"
						>
							<div
								style="border: 1px solid #efefef; border-radius: 5px"
								@click="gotoRoom(item.roomNo, item.courseId)"
							>
								<div>
									<img style="width: 100%; background-color: #eee" :src="item.screenshot" />
									<div style="padding: 6px">讲师：{{ item.lecturerName }}</div>
								</div>
								<div style="padding: 0 6px 6px">
									<div>课程主题：{{ item.subject }}</div>
									<div>
										课程时间：{{ formatDate(item.beginTime, 'HH:mm') }} ~
										{{ formatDate(item.endTime, 'HH:mm') }}
									</div>
									<div>开始时间：{{ formatDate(item.startTeachingTime, 'HH:mm:ss') }}</div>
									<div>持续时间：{{ formatTimeStr(item.duration * 1000, 'mm:ss') }}</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div style="flex: 1 1 100%; position: relative" v-if="currentCourseId" :key="currentCourseId">
					<RoomDetail :roomNo="currentRoomNo" :courseId="currentCourseId" />
				</div>
			</div>
		</template>
		<template v-else>
			<m-empty style="margin: 50px auto 0" />
		</template>
	</div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { UndoOutlined } from '@ant-design/icons-vue';
import { onViewShow } from '@paas/paas-library';

import { TeachingRoomListStore } from '@/application/courses/store';
import { formatTimeStr, formatDate } from '@/shard/utils';
import RoomDetail from './room-detail.vue';

let courseList = ref([]);
let isFirst = false;
let timer = 0;
let currentRoomNo = ref('');
let currentCourseId = ref(0);

export default defineComponent({
	components: { UndoOutlined, RoomDetail },
	setup() {
		const components = {};

		const constants = {};

		const state = reactive({
			loading: false
		});

		const methods = {
			formatDate,
			formatTimeStr,
			init() {
				methods.getTeachingRooList();
				clearInterval(timer);
				timer = setInterval(() => {
					methods.getTeachingRooList();
				}, 10000) as any;
			},
			watchEnd() {
				clearInterval(timer);
				currentRoomNo.value = '';
				currentCourseId.value = 0;
			},
			gotoRoom(roomNo, courseId) {
				currentRoomNo.value = roomNo;
				currentCourseId.value = courseId;
			},
			async getTeachingRooList() {
				state.loading = true;
				let res = await TeachingRoomListStore.request().getData();
				courseList.value = res;
				state.loading = false;
			},
			onReset() {
				currentRoomNo.value = '';
				currentCourseId.value = 0;
				methods.getTeachingRooList();
			}
		};

		if (!isFirst) {
			isFirst = true;
			methods.init();
			onViewShow({
				online: true,
				callback: params => {
					const { id } = params;
					if (id === 'course-monitor') {
						methods.init();
					} else {
						methods.watchEnd();
					}
				}
			});
		}

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			courseList,
			currentRoomNo,
			currentCourseId
		};
	}
});
</script>
