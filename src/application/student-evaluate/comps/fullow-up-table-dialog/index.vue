<template>
	<pm-dialog v-model:visible="visible" :title="title" width="680px">
		<pm-effi :controller="controller">
			<pm-table
				:columns="COLUMNS"
				:use-custom-column="true"
				:pagination="false"
				:operations="false"
				:sort-num="false"
				:scroll="{
					y: '420px'
				}"
			></pm-table>
		</pm-effi>
		<m-form
			ref="formRef"
			:model="formState"
			:colon="false"
			:label-col="{ style: { width: '95px' } }"
			autocomplete="off"
		>
			<m-form-item name="remark" required>
				<div style="margin-bottom: 10px">若您已跟进或准备跟进本评价，请在下方填写跟进备注</div>
				<m-textarea v-model:value="formState.remark" placeholder="跟进备注" :rows="3" />
			</m-form-item>
		</m-form>

		<template #footer>
			<m-button plain @click="visible = false">取消</m-button>
			<m-button :loading="loading" type="primary" @click="onConfirm">确定</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { MESSAGE_TYPE, MUtils, ModelController } from 'admin-library';

import { cloneFromPick, cloneByMerge } from '@/shard/utils';
import { COLUMNS } from './config';
import { FollowListStore, FollowUpStore } from './store';

const DefaultFormState = {
	id: null,
	remark: ''
};

export default defineComponent({
	emits: ['refresh'],
	setup(props, { emit }) {
		const components = {
			formRef: ref(null)
		};

		const constants = {
			COLUMNS
		};

		const state = reactive({
			visible: false,
			title: '跟进',
			loading: false,
			formState: MUtils.deepClone(DefaultFormState)
		});

		const controller = new ModelController({
			table: {
				store: FollowListStore
			}
		});

		controller.table.onRequest.use(params => {
			params = {
				...params,
				evaluateId: state.formState.id
			};

			return params;
		});

		const methods = {
			open({ evaluateId }) {
				state.formState = cloneFromPick({ id: evaluateId }, DefaultFormState);

				controller.tableRequest();

				state.visible = true;
			},
			async onConfirm() {
				await components.formRef.value.validate();

				try {
					state.loading = true;
					const params = cloneByMerge(state.formState, DefaultFormState);
					const fetchStore = FollowUpStore;

					await fetchStore.request(params).getData();

					state.formState.remark = '';
					controller.tableRequest();
					MUtils.toast('保存成功', MESSAGE_TYPE.success);
					emit('refresh');
				} finally {
					state.loading = false;
				}
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			controller
		};
	}
});
</script>
