import { TableColumn, TableDateFormat } from 'admin-library';
import { ColumnWidthEnum } from '@/shard/constant';

export const COLUMNS: TableColumn[] = [
	{
		title: '#',
		dataIndex: 'id'
	},
	{
		title: '跟进人',
		dataIndex: 'followUserName',
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '跟进时间',
		dataIndex: 'followTime',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	},
	{
		title: '跟进备注',
		dataIndex: 'remark',
		width: 330
	}
];
