import { ColumnXtype, TableColumn, TableDateFormat } from 'admin-library';
import { renderTextWithColor, renderCopyComp } from '@/shard/utils';
import { ColumnWidthEnum, RADIO_MAP } from '@/shard/constant';
import { TYPE_OPTIONS } from './constant';

export const COLUMNS: TableColumn[] = [
	{
		title: '业务Id',
		dataIndex: 'bizId'
	},
	{
		title: '评价人',
		dataIndex: 'evaluateUserName',
		width: 160
	},
	{
		title: '销售人员',
		dataIndex: 'salesName'
	},
	{
		title: '讲师',
		dataIndex: 'lecturerName',
		xtype: ColumnXtype.CUSTOM
	},
	{
		title: '督导',
		dataIndex: 'supervisorName'
	},
	{
		title: '评价类型',
		dataIndex: 'evaluateType',
		render: data => {
			return renderTextWithColor(data, TYPE_OPTIONS, { isTag: true });
		},
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '评价人木仓ID',
		dataIndex: 'evaluateUserMucangId',
		render: data => {
			return renderCopyComp(data);
		},
		width: ColumnWidthEnum.TEXT6
	},
	{
		title: '补充描述',
		dataIndex: 'description',
		width: 180
	},
	{
		title: '评语',
		dataIndex: 'content',
		width: 240
	},
	{
		title: '评价时间',
		dataIndex: 'createTime',
		type: 'date',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	},
	{
		title: '是否已跟进',
		dataIndex: 'followUp',
		render: data => {
			return RADIO_MAP[data];
		},
		width: ColumnWidthEnum.TEXT5
	},
	{
		title: '是否恶意评价',
		dataIndex: 'isMalicious',
		xtype: ColumnXtype.CUSTOM,
		width: ColumnWidthEnum.TEXT6
	},
	{
		title: '最后跟进人',
		dataIndex: 'latestFollowUser',
		width: ColumnWidthEnum.TEXT5
	}
];
