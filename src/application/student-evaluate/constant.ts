import { getMapfromArray, getStorefromArray } from '@/shard/utils';
import { ColorEnum } from '@/shard/constant';

export const TYPE_OPTIONS = [
	{
		label: '好评',
		value: 1,
		styleclass: ColorEnum.success
	},
	{
		label: '中评',
		value: 2,
		styleclass: ColorEnum.primary
	},
	{
		label: '差评',
		value: 3,
		styleclass: ColorEnum.danger
	}
];
export const TYPE_MAP = getMapfromArray(TYPE_OPTIONS);
export const TYPE_STORE = getStorefromArray(TYPE_OPTIONS);
