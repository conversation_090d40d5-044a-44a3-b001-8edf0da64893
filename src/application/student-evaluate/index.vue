<template>
	<pm-effi :controller="controller">
		<pm-search>
			<pm-search-single
				:span="5"
				:antd-props="{
					placeholder: ['评价时间', '结束时间']
				}"
				xtype="RANGEPICKER"
				data-index="evaluateTimeFrom|evaluateTimeTo"
			/>
			<pm-search-single>
				<template #custom>
					<student-select
						@onSearchStudent="onSearchStudent"
						data-index="studentId"
						comp-type="search"
						:antdProps="{
							placeholder: '评价人'
						}"
					/>
				</template>
			</pm-search-single>
			<pm-search-single>
				<template #custom>
					<teacher-select
						@onSearchLecturer="onSearchLecturer"
						data-index="lecturerId"
						comp-type="search"
						:antdProps="{
							placeholder: '讲师'
						}"
					/>
				</template>
			</pm-search-single>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '木仓Id'
				}"
				data-index="mucangId"
				xtype="INPUT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '评价类型'
				}"
				data-index="evaluateType"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '是否已跟进'
				}"
				data-index="followUp"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '是否恶意评价'
				}"
				data-index="isMalicious"
				xtype="SELECT"
			/>
		</pm-search>
		<pm-table
			:columns="COLUMNS"
			:use-custom-column="true"
			:operations-fixed="true"
			:operations-width="80"
			:sort-num="false"
			:scroll="{
				x: 'max-content'
			}"
		>
			<template #lecturerName="{ record }">
				<m-button type="link" @click="searchLecturer(record)">
					{{ record.lecturerName }}
				</m-button>
			</template>
			<template #isMalicious="{ record }">
				<m-switch v-model:checked="record.isMalicious" @click="onClickMalicious(record)" />
			</template>
			<template #operations="{ record }">
				<m-button type="link" @click="fullowUp(record)">跟进</m-button>
			</template>
		</pm-table>
	</pm-effi>

	<fullow-up-table-comp ref="fullowUpTableRef" @refresh="onRefresh"></fullow-up-table-comp>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { ModelController, MUtils, MESSAGE_TYPE } from 'admin-library';
import { Store } from '@simplex/simple-store';

import StudentSelect from '@/comp/student-select/index.vue';
import TeacherSelect from '@/comp/teacher-select/index.vue';
import FullowUpTableComp from './comps/fullow-up-table-dialog/index.vue';
import { COLUMNS } from './config';
import { RADIO_STORE } from '@/shard/constant';
import { TYPE_STORE } from './constant';
import { ListStore, UpdateMaliciousStore } from './store';

export default defineComponent({
	components: {
		StudentSelect,
		TeacherSelect,
		FullowUpTableComp
	},
	setup() {
		const components = {
			fullowUpTableRef: ref<InstanceType<typeof FullowUpTableComp>>()
		};

		const constants = {
			COLUMNS
		};

		const state = reactive({});

		const controller = new ModelController({
			table: {
				store: ListStore
			},
			search: {
				studentId: {
					store: new Store([])
				},
				lecturerId: {
					store: new Store([])
				},
				evaluateType: {
					store: TYPE_STORE
				},
				followUp: {
					store: RADIO_STORE
				},
				isMalicious: {
					store: new Store([
						{ key: null, value: '全部' },
						{ key: true, value: '是' }
					])
				}
			}
		});
		controller.tableRequest();

		const methods = {
			onSearchStudent(list) {
				controller.search.studentId.updateStore(new Store(list));
			},
			async onSearchLecturer(list) {
				controller.search.lecturerId.updateStore(new Store(list));
			},
			fullowUp(row) {
				components.fullowUpTableRef.value.open({
					evaluateId: row.id
				});
			},
			async onClickMalicious(row) {
				const { isMalicious, id } = row;
				try {
					await UpdateMaliciousStore.request({ isMalicious: isMalicious, id: id }).getData();
					MUtils.toast(`${!isMalicious ? '取消' : '标记'}恶意评价成功`, MESSAGE_TYPE.success);
				} catch (error) {
					console.error(error.message);
				} finally {
					methods.onRefresh();
				}
			},
			searchLecturer(row) {
				controller.tableRequest({
					lecturerId: row.lecturerId
				});
			},
			onRefresh() {
				controller.tableRequest();
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			controller
		};
	}
});
</script>
