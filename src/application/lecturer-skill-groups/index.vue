<template>
	<pm-effi :controller="controller">
		<pm-search>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '车型'
				}"
				data-index="carType"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '科目'
				}"
				data-index="tutorKemu"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '审核状态'
				}"
				data-index="status"
				xtype="SELECT"
			/>
		</pm-search>
		<pm-table
			:columns="COLUMNS"
			:use-custom-column="true"
			:operations-fixed="true"
			:operations-width="80"
			:sort-num="false"
			:scroll="{
				x: '100%'
			}"
		>
			<template #lecturerName="{ record }">
				<m-button type="link" @click="toLecturer(record)">
					{{ record.lecturerMixName }}
				</m-button>
			</template>

			<template #operations="{ record }">
				<m-button type="link" @click="toSkillList(record)">管理Ta的技能组</m-button>
			</template>
		</pm-table>
	</pm-effi>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue';
import { ModelController, PaasPostMessage } from 'admin-library';

import { COLUMNS } from './config';
import { CAR_TYPE_STORE, EXAM_KEMU_STORE } from '@/shard/constant';
import { SKILL_STATUS_STORE } from '@/application/part-time-teacher/constant';
import { SkillGroupsListStore as ListStore } from '@/popup-pages/skill-list/store';
import { ItemResponse } from './types';

export default defineComponent({
	setup() {
		const components = {};

		const constants = {
			COLUMNS
		};

		const state = reactive({});

		const controller = new ModelController({
			autoSearch: false,
			table: {
				store: ListStore
			},
			search: {
				carType: {
					store: CAR_TYPE_STORE
				},
				tutorKemu: {
					store: EXAM_KEMU_STORE
				},
				status: {
					store: SKILL_STATUS_STORE
				}
			}
		});
		// controller.tableRequest();

		const methods = {
			toLecturer(row: ItemResponse) {
				PaasPostMessage.post('navigation.to', '/#/part-time-teacher', {
					query: {
						lecturerId: row?.lecturerId
					},
					target: '_tab'
				});
			},
			toSkillList(row) {
				PaasPostMessage.post('navigation.to', '/#/skill-list', {
					title: '技能组',
					query: {
						lecturerId: row?.lecturerId
					},
					extendData: {
						style: 'width: 780px; height: 90%; margin: 2% auto 0;'
					},
					target: '_popup'
				});
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			controller
		};
	}
});
</script>
