<template>
	<pm-effi :controller="controller">
		<pm-table
			:columns="COLUMNS"
			:use-custom-column="true"
			:operations-fixed="false"
			:operations-width="70"
			:sort-num="false"
			:scroll="{
				x: 'max-content'
			}"
		>
			<template #beforeCustom>
				<m-button type="primary" @click="onCreate()">添加</m-button>
			</template>
			<template #operations="{ record }">
				<m-button type="link" @click="onEdit(record)">编辑</m-button>
			</template>
		</pm-table>
	</pm-effi>

	<edit-comp ref="editRef" @refresh="onRefresh"></edit-comp>
</template>
<script lang="ts">
import { defineComponent, ref, reactive, toRefs } from 'vue';
import { ModelController } from 'admin-library';

import EditComp from './comps/edit-dialog/index.vue';
import { COLUMNS } from './config';
import { ListStore } from './store';
import { ItemResponse } from './types';

export default defineComponent({
	components: {
		EditComp
	},
	setup() {
		const components = {
			editRef: ref<InstanceType<typeof EditComp>>()
		};

		const constants = {
			COLUMNS
		};

		const state = reactive({});

		const controller = new ModelController({
			table: {
				store: ListStore
			}
		});
		controller.tableRequest();

		const methods = {
			onCreate() {
				components.editRef.value.open();
			},
			onEdit(row: ItemResponse) {
				components.editRef.value.open(row);
			},
			onRefresh() {
				controller.tableRequest();
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			controller
		};
	}
});
</script>
