<template>
	<pm-dialog v-model:visible="visible" :title="title" width="680px">
		<template v-if="visible">
			<m-form
				ref="formRef"
				:model="formState"
				:colon="false"
				:label-col="{ style: { width: '95px' } }"
				autocomplete="off"
			>
				<m-form-item label="讲师" name="lecturerId" required>
					<teacher-select v-model:value="formState.lecturerId" :disabled="again" />
				</m-form-item>
				<m-form-item label="拉黑原因" name="reason" required>
					<m-textarea v-model:value="formState.reason" :rows="3" />
				</m-form-item>
			</m-form>
		</template>

		<template #footer>
			<m-button plain @click="visible = false">取消</m-button>
			<m-button :loading="loading" type="primary" @click="onConfirm">确定</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { MESSAGE_TYPE, MUtils } from 'admin-library';

import TeacherSelect from '@/comp/teacher-select/index.vue';
import { cloneFromPick, cloneByMerge } from '@/shard/utils';
import { AddStore } from '../../store';

const DefaultFormState = {
	lecturerId: null,
	reason: ''
};

export default defineComponent({
	emits: ['refresh'],
	components: { TeacherSelect },
	setup(props, { emit }) {
		const components = {
			formRef: ref(null)
		};

		const constants = {};

		const state = reactive({
			visible: false,
			title: '手动拉黑',
			loading: false,
			formState: MUtils.deepClone(DefaultFormState),
			options: {},
			lecturerLoading: false,
			again: false
		});

		const methods = {
			async open(row?: any, again?) {
				state.formState = MUtils.deepClone(DefaultFormState);

				if (row) {
					state.formState = cloneFromPick(row, DefaultFormState);

					if (row.type === 1) {
						state.formState.resourceEncode = {
							encodedData: row.fileKey,
							showUrl: row.content
						};
					}
				}

				state.again = again;

				state.visible = true;
			},
			async onConfirm() {
				await components.formRef.value.validate();

				try {
					state.loading = true;
					const params = cloneByMerge(state.formState, DefaultFormState);
					const fetchStore = AddStore;

					await fetchStore.request(params).getData();

					emit('refresh');
					state.visible = false;
					MUtils.toast('保存成功', MESSAGE_TYPE.success);
				} finally {
					state.loading = false;
				}
			},
			changeRadio() {
				state.formState.content = '';
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods
		};
	}
});
</script>
