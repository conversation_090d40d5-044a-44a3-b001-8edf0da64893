import { TableColumn, TableDateFormat } from 'admin-library';
import { ColumnWidthEnum } from '@/shard/constant';
import { STATUS_MAP } from '@/application/promoter/constant';
import { LAYER_MAP } from '@/application/part-time-teacher/constant';

export const COLUMNS: TableColumn[] = [
	{
		title: '讲师',
		dataIndex: 'lectureMixName'
	},
	{
		title: '等级',
		dataIndex: 'level',
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '上岗状态',
		dataIndex: 'stationStatus',
		render: data => {
			return STATUS_MAP[data];
		},
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '层级',
		dataIndex: 'layer',
		render: data => {
			return LAYER_MAP[data];
		},
		width: ColumnWidthEnum.TEXT3
	},
	{
		title: '累计被拉黑次数',
		dataIndex: 'count',
		width: 126
	},
	{
		title: '最近拉黑时间',
		dataIndex: 'latestBlackedTime',
		type: 'date',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	}
];
