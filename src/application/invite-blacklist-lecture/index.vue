<template>
	<pm-effi :controller="controller">
		<pm-table
			:columns="COLUMNS"
			:use-custom-column="true"
			:operations="true"
			:operations-width="260"
			:sort-num="false"
			:scroll="{
				x: 'max-content'
			}"
		>
			<template #beforeCustom>
				<m-button type="primary" @click="onCreate">手动拉黑</m-button>
			</template>
			<template #operations="{ record }">
				<m-button type="link" @click="toInviteRecord(record)">邀约记录</m-button>
				<m-button type="link" @click="toBlackList(record)">拉黑&解除记录</m-button>
				<m-button type="link" danger @click="onRecovery(record)">解除拉黑</m-button>
			</template>
		</pm-table>
	</pm-effi>
	<edit-recovery-comp ref="editRecoveryRef" @refresh="onRefresh"></edit-recovery-comp>
	<CreateDialog ref="createRef" @refresh="onRefresh"></CreateDialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { ModelController, PaasPostMessage } from 'admin-library';
import { useRoute } from 'vue-router';

import EditRecoveryComp from '@/application/part-time-teacher/comps/edit-recovery-dialog/index.vue';
import CreateDialog from './comps/edit-dialog/index.vue';
import { COLUMNS } from './config';
import { ListStore } from './store';
import { ItemResponse } from './types';

export default defineComponent({
	components: {
		EditRecoveryComp,
		CreateDialog
	},

	setup() {
		const route = useRoute();
		const query = route.query;

		const components = {
			editRecoveryRef: ref<InstanceType<typeof EditRecoveryComp>>(),
			createRef: ref<InstanceType<typeof CreateDialog>>()
		};

		const constants = {
			COLUMNS
		};

		const state = reactive({});

		const controller = new ModelController({
			table: {
				store: ListStore
			}
		});
		controller.tableRequest();

		controller.table.onRequest.use(params => {
			params = {
				...params,
				lecturerId: query.lecturerId
			};

			return params;
		});

		const methods = {
			onCreate() {
				components.createRef.value?.open();
			},
			async onRecovery(row: ItemResponse) {
				components.editRecoveryRef.value.open(row);
			},
			toInviteRecord(row) {
				PaasPostMessage.post('navigation.to', '/#/lecturer-invite-record', {
					title: '邀约记录',
					query: {
						lecturerId: row?.lecturerId
					},
					extendData: {
						style: 'width: 1020px; height: 90%; margin: 2% auto 0;'
					},
					target: '_popup'
				});
			},
			toBlackList(row) {
				PaasPostMessage.post('navigation.to', '/#/invite-black-record', {
					title: '解除&拉黑记录',
					query: {
						lecturerId: row?.lecturerId
					},
					extendData: {
						style: 'width: 1020px; height: 90%; margin: 2% auto 0;'
					},
					target: '_popup'
				});
			},
			onRefresh() {
				controller.tableRequest();
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			controller
		};
	}
});
</script>
