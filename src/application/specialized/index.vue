<template>
	<pm-effi :controller="controller">
		<pm-search>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '车型'
				}"
				data-index="carType"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '状态'
				}"
				data-index="status"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '辅导科目'
				}"
				data-index="tutorKemu"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '层级'
				}"
				data-index="level"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '专项名称'
				}"
				data-index="name"
				xtype="INPUT"
			/>
		</pm-search>

		<pm-table
			:columns="COLUMNS"
			:use-custom-column="true"
			:operations-fixed="true"
			:operations-width="200"
			:sort-num="false"
			:scroll="{
				x: 'max-content'
			}"
		>
			<template #beforeCustom>
				<m-button type="primary" @click="onCreate">添加专项</m-button>
			</template>

			<template #operations="{ record }">
				<m-button type="link" @click="onEdit(record)">编辑</m-button>
				<m-button type="link" danger @click="toggleStatus(record)" v-if="record.status === 2">下线</m-button>
				<m-button type="link" @click="toggleStatus(record)" v-else>上线</m-button>
				<m-button type="link" @click="goSpeQuestion(record)">专项题库</m-button>
			</template>
		</pm-table>
	</pm-effi>

	<edit-comp ref="editRef" @refresh="onRefresh"></edit-comp>
</template>

<script lang="ts">
import { defineComponent, reactive, ref, toRefs } from 'vue';
import { MESSAGE_TYPE, MUtils, ModelController, PaasPostMessage } from 'admin-library';

import EditComp from './comps/edit-dialog/index.vue';
import { COLUMNS } from './config';
import { CAR_TYPE_STORE, EXAM_KEMU_STORE, STATUS_STORE } from '@/shard/constant';
import { LAYER_STORE } from './constant';
import { ListStore, UpdateStore } from './store';
import { ItemResponse } from './types';

export default defineComponent({
	components: {
		EditComp
	},
	setup() {
		const components = {
			editRef: ref<InstanceType<typeof EditComp>>()
		};

		const constants = {
			COLUMNS
		};

		const state = reactive({});

		const controller = new ModelController({
			table: {
				store: ListStore
			},
			search: {
				carType: {
					store: CAR_TYPE_STORE
				},
				tutorKemu: {
					store: EXAM_KEMU_STORE
				},
				status: {
					store: STATUS_STORE
				},
				level: {
					store: LAYER_STORE
				}
			}
		});
		controller.tableRequest();

		const methods = {
			goSpeQuestion(row) {
				PaasPostMessage.post('navigation.to', '/#/specialized-question', {
					title: '专项题库管理-' + row.name,
					query: {
						specializedId: row?.id
					},
					target: '_tab'
				});
			},
			onCreate() {
				components.editRef.value.open();
			},
			onEdit(row: ItemResponse) {
				components.editRef.value.open(row);
			},
			async toggleStatus(row) {
				await UpdateStore.request({ id: row.id, status: row.status === 2 ? 1 : 2 }).getData();
				methods.onRefresh();
				MUtils.toast('操作成功', MESSAGE_TYPE.success);
			},

			onRefresh() {
				controller.tableRequest();
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			controller
		};
	}
});
</script>
