<template>
	<pm-dialog v-model:visible="visible" :title="title" width="680px">
		<template v-if="visible">
			<m-form
				ref="formRef"
				:model="formState"
				:colon="false"
				:label-col="{ style: { width: '95px' } }"
				autocomplete="off"
			>
				<m-form-item label="专项图标" name="resource" extra="专项图标，图片大小不超过2MB" required>
					<fileUploadComp fileType="image" :maxSize="2" v-model:value="formState.resource" />
				</m-form-item>
				<m-form-item label="名称" name="name" required>
					<m-input v-model:value="formState.name" :maxlength="15" placeholder="专项名称，限15字以内" />
				</m-form-item>
				<m-form-item label="车型" name="carType" required>
					<m-select
						v-model:value="formState.carType"
						:options="options.carTypeList"
						:allowClear="true"
						placeholder="车型"
					/>
				</m-form-item>
				<m-form-item label="科目" name="tutorKemu" required>
					<m-select
						v-model:value="formState.tutorKemu"
						:options="options.kemuList"
						:allowClear="true"
						placeholder="科目"
					/>
				</m-form-item>
				<m-form-item label="层级" name="level" required>
					<m-select
						v-model:value="formState.level"
						:options="options.layerList"
						:allowClear="true"
						placeholder="层级"
					/>
				</m-form-item>
				<m-form-item label="发布状态" name="status" required>
					<m-select
						v-model:value="formState.status"
						:options="options.statusList"
						:allowClear="true"
						placeholder="发布状态"
					/>
				</m-form-item>
			</m-form>
		</template>

		<template #footer>
			<m-button plain @click="visible = false">取消</m-button>
			<m-button :loading="loading" type="primary" @click="onConfirm">确定</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { MESSAGE_TYPE, MUtils } from 'admin-library';

import fileUploadComp from '@/comp/file-upload/index.vue';
import { cloneFromPick, cloneByMerge } from '@/shard/utils';
import { CAR_TYPE_OPTIONS, EXAM_KEMU_OPTIONS, STATUS_OPTIONS } from '@/shard/constant';
import { LAYER_OPTIONS } from '../../constant';
import { CreateStore, UpdateStore } from '../../store';

const DefaultFormState = {
	id: null,
	resource: null,
	name: '',
	carType: '',
	tutorKemu: '',
	remark: '',
	level: '',
	status: 1
};

export default defineComponent({
	components: {
		fileUploadComp
	},
	emits: ['refresh'],
	setup(props, { emit }) {
		const components = {
			formRef: ref(null)
		};

		const constants = {};

		const state = reactive({
			visible: false,
			title: '',
			loading: false,
			formState: MUtils.deepClone(DefaultFormState),
			options: {
				carTypeList: CAR_TYPE_OPTIONS,
				kemuList: EXAM_KEMU_OPTIONS,
				layerList: LAYER_OPTIONS,
				statusList: STATUS_OPTIONS
			}
		});

		const methods = {
			open(row?: any) {
				state.formState = MUtils.deepClone(DefaultFormState);
				state.title = row?.id ? '编辑' : '新增';

				if (row) {
					state.formState = cloneFromPick(row, DefaultFormState);
					if (row.iconEncodedData) {
						state.formState.resource = {
							showUrl: row.icon,
							encodedData: row.iconEncodedData
						};
					}
				}

				state.visible = true;
			},
			async onConfirm() {
				await components.formRef.value.validate();

				try {
					state.loading = true;
					const params = cloneByMerge(state.formState, DefaultFormState);
					const fetchStore = state.formState.id ? UpdateStore : CreateStore;

					params.icon = params.resource.encodedData;
					delete params.resource;

					await fetchStore.request(params).getData();

					emit('refresh');
					state.visible = false;
					MUtils.toast('保存成功', MESSAGE_TYPE.success);
				} finally {
					state.loading = false;
				}
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods
		};
	}
});
</script>
