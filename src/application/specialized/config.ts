import { TableColumn, TableDateFormat } from 'admin-library';
import { ColumnWidthEnum, EXAM_KEMU_MAP, CAR_TYPE_MAP, STATUS_MAP } from '@/shard/constant';
import { renderImageComp } from '@/shard/utils';
import { LAYER_MAP } from './constant';

export const COLUMNS: TableColumn[] = [
	{
		title: '#',
		dataIndex: 'id',
		fixed: 'left'
	},
	{
		title: '专项名称',
		dataIndex: 'name',
		fixed: 'left'
	},
	{
		title: '专项图标',
		dataIndex: 'icon',
		render: data => {
			return renderImageComp(data);
		},
		width: 120
	},
	{
		title: '辅导科目',
		dataIndex: 'tutorKemu',
		render: data => {
			return EXAM_KEMU_MAP[data];
		},
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '车型',
		dataIndex: 'carType',
		render: data => {
			return CAR_TYPE_MAP[data];
		},
		width: ColumnWidthEnum.TEXT3
	},
	{
		title: '层级',
		dataIndex: 'level',
		render: data => {
			return LAYER_MAP[data];
		},
		width: ColumnWidthEnum.TEXT2
	},
	{
		title: '发布状态',
		dataIndex: 'status',
		render: data => {
			return STATUS_MAP[data];
		},
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '创建时间',
		dataIndex: 'createTime',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	}
];
