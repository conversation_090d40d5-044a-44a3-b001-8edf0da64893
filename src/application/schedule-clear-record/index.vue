<template>
	<pm-effi :controller="controller">
		<pm-search>
			<pm-search-single
				:span="6"
				:antd-props="{
					placeholder: ['操作时间', '结束时间']
				}"
				xtype="RANGEPICKER"
				data-index="createTimeFrom|createTimeTo"
			/>
			<pm-search-single>
				<template #custom>
					<teacher-select
						v-model:value="lecturerId"
						@onSearchLecturer="onSearchLecturer"
						data-index="lecturerId"
						comp-type="search"
					/>
				</template>
			</pm-search-single>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '清空原因'
				}"
				data-index="clearReasonType"
				xtype="SELECT"
			/>
		</pm-search>
		<pm-table
			:columns="COLUMNS"
			:use-custom-column="true"
			:operations="false"
			:sort-num="false"
			:scroll="{
				x: '100%'
			}"
		></pm-table>
	</pm-effi>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue';
import { ModelController } from 'admin-library';
import { useRoute } from 'vue-router';
import { Store } from '@simplex/simple-store';

import TeacherSelect from '@/comp/teacher-select/index.vue';
import { COLUMNS } from './config';
import { TYPE_STORE } from './constant';
import { ListStore } from './store';

export default defineComponent({
	components: {
		TeacherSelect
	},
	setup() {
		const route = useRoute();
		const query = route.query;

		const components = {};

		const constants = {
			COLUMNS
		};

		let lecturerId;
		if (query.lecturerId) {
			lecturerId = +query.lecturerId;
		}
		const state = reactive({
			lecturerId
		});

		const controller = new ModelController({
			table: {
				store: ListStore
			},
			search: {
				lecturerId: {
					store: new Store([])
				},
				clearReasonType: {
					store: TYPE_STORE
				}
			}
		});
		controller.tableRequest();

		const methods = {
			async onSearchLecturer(list) {
				controller.search.lecturerId.updateStore(new Store(list));
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			controller
		};
	}
});
</script>
