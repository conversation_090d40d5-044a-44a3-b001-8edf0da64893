import { TableColumn, TableDateFormat } from 'admin-library';
import { renderImageComp } from '@/shard/utils';
import { ColumnWidthEnum } from '@/shard/constant';
import { TYPE_MAP } from './constant';

export const COLUMNS: TableColumn[] = [
	{
		title: '清空的日期',
		dataIndex: 'scheduleDate',
		width: ColumnWidthEnum.DATEDAY
	},
	{
		title: '讲师',
		dataIndex: 'lecturerMixName'
	},
	{
		title: '截图',
		dataIndex: 'resourceUrl',
		render: data => {
			return renderImageComp(data);
		},
		width: 150
	},
	{
		title: '清空原因',
		dataIndex: 'clearReasonType',
		render: data => {
			return TYPE_MAP[data];
		}
	},
	{
		title: '清空说明',
		dataIndex: 'remark',
		width: 240
	},
	{
		title: '操作人',
		dataIndex: 'createUserName',
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '操作时间',
		dataIndex: 'createTime',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	}
];
