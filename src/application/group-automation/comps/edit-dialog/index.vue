<template>
	<pm-dialog v-model:visible="visible" :title="title" width="680px">
		<template v-if="visible">
			<m-form
				ref="formRef"
				:model="formState"
				:colon="false"
				:label-col="{ style: { width: '125px' } }"
				autocomplete="off"
			>
				<m-form-item label="企微账号" name="ownerEmployeeId" required>
					<m-select
						show-search
						optionFilterProp="employeeMixName"
						@change="onEventEmployeeNoChange"
						v-model:value="formState.ownerEmployeeId"
						:options="options.employeeOptions"
						:fieldNames="{ label: 'employeeMixName', value: 'id' }"
					/>
				</m-form-item>
				<m-form-item label="企微群" name="groupId" required>
					<m-select
						show-search
						optionFilterProp="contactMixName"
						v-model:value="formState.groupId"
						:options="options.contactOptions"
						:fieldNames="{ label: 'contactMixName', value: 'contactId' }"
					></m-select>
				</m-form-item>
				<m-form-item label="开班日期" name="classOpenDate" required>
					<m-date-picker
						v-model:value="formState.classOpenDate"
						:disabled-date="disabledDate"
						format="YYYY-MM-DD"
						valueFormat="YYYY-MM-DD"
					/>
				</m-form-item>
				<m-form-item label="班群周期" name="classPeriodDays" required :rules="positiveInteger">
					<m-input-number
						style="width: 240px"
						:min="1"
						v-model:value="formState.classPeriodDays"
						placeholder="输入班群持续天数，限正整数"
					/>
				</m-form-item>
				<m-form-item label="群消息模版" name="groupTemplateId" required>
					<m-select
						show-search
						optionFilterProp="name"
						v-model:value="formState.groupTemplateId"
						:options="options.tmplList"
						:fieldNames="{ label: 'name', value: 'id' }"
					/>
				</m-form-item>
				<m-form-item label="客户跟进消息模版" name="customerTemplateId">
					<m-select
						show-search
						optionFilterProp="name"
						v-model:value="formState.customerTemplateId"
						:allowClear="true"
						:options="options.tmplList2"
						:fieldNames="{ label: 'name', value: 'id' }"
					/>
				</m-form-item>
				<m-form-item v-if="formState.customerTemplateId" label="群发对象" name="customerTags" required>
					<TagPickComp :multiple="true" v-model:value="formState.customerTags" />
				</m-form-item>
				<m-form-item label="流量承接开始日期" name="flowBeginDate" required>
					<m-date-picker
						v-model:value="formState.flowBeginDate"
						:show-time="{ format: 'HH:mm' }"
						format="YYYY-MM-DD HH:mm"
						valueFormat="YYYY-MM-DD HH:mm"
					/>
				</m-form-item>
				<m-form-item label="流量承接结束日期" name="flowEndDate" required>
					<m-date-picker
						v-model:value="formState.flowEndDate"
						:show-time="{ format: 'HH:mm' }"
						format="YYYY-MM-DD HH:mm"
						valueFormat="YYYY-MM-DD HH:mm"
					/>
				</m-form-item>
			</m-form>
		</template>

		<template #footer>
			<m-button plain @click="visible = false">取消</m-button>
			<m-button :loading="loading" type="primary" @click="onConfirm">确定</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts">
import Dayjs from 'dayjs';
import { defineComponent, reactive, toRefs, ref, computed } from 'vue';
import { MESSAGE_TYPE, MUtils } from 'admin-library';

import { cloneFromPick, cloneByMerge } from '@/shard/utils';
import { positiveInteger } from '@/shard/validate';
import TagPickComp from '@/application/bulk-delete/comps/edit-dialog/tag-picker.vue';
import { ListStore as EmployeeListStore } from '@/application/promoter/store';
import { ContactListStore } from '@/application/bulk-message/store';
import { ListStore as TmplListStore } from '@/application/group-automaiton-tmpl/store';
import { CreateStore, UpdateStore } from '../../store';

const DefaultFormState = {
	id: null,
	ownerEmployeeId: '',
	groupId: '',
	classOpenDate: '',
	classPeriodDays: '',
	groupTemplateId: '',
	customerTemplateId: '',
	customerTags: '',
	flowBeginDate: '',
	flowEndDate: ''
};

export default defineComponent({
	components: {
		TagPickComp
	},
	emits: ['refresh'],
	setup(props, { emit }) {
		const components = {
			formRef: ref(null)
		};

		const constants = {
			positiveInteger
		};

		const state = reactive({
			visible: false,
			title: '',
			loading: false,
			formState: MUtils.deepClone(DefaultFormState),
			options: {
				employeeOptions: [],
				contactOptions: [],
				tmplList: [],
				tmplList2: []
			}
		});

		const wecomUserId = computed(() => {
			const { ownerEmployeeId } = state.formState;
			let employee = state.options.employeeOptions.find(item => {
				return item.id === ownerEmployeeId;
			});
			return employee?.wecomUserId;
		});

		const methods = {
			async open(row?: any) {
				state.formState = MUtils.deepClone(DefaultFormState);
				state.title = row?.id ? '编辑' : '新增';

				if (row) {
					state.formState = cloneFromPick(row, DefaultFormState);
				}

				if (state.formState.classOpenDate) {
					state.formState.classOpenDate = Dayjs(state.formState.classOpenDate);
				}
				if (state.formState.flowBeginDate) {
					state.formState.flowBeginDate = Dayjs(state.formState.flowBeginDate);
				}
				if (state.formState.flowEndDate) {
					state.formState.flowEndDate = Dayjs(state.formState.flowEndDate);
				}
				if (state.formState?.customerTags.length) {
					state.formState.customerTags = state.formState.customerTags.map(item => item.tagId);
				}

				state.visible = true;

				methods.initTmplOptions();
				await methods.initEmployeeOptions();
				methods.initContactListOptions();
			},

			onEventEmployeeNoChange() {
				methods.initContactListOptions();

				state.formState.groupId = '';
			},

			async initEmployeeOptions() {
				const res = await EmployeeListStore.request({
					enableAutomaticHosting: true,
					limit: 9999,
					status: 20
				}).getData();
				if (res) {
					state.options.employeeOptions = res;
				}
			},
			async initContactListOptions() {
				if (wecomUserId.value) {
					let res = await ContactListStore.request({
						ownerId: wecomUserId.value,
						contactType: 2,
						limit: 9999
					}).getData();
					if (res) {
						state.options.contactOptions = res;
					}
				}
			},
			async initTmplOptions() {
				let res = await TmplListStore.request({
					limit: 9999
				}).getData();
				if (res) {
					state.options.tmplList = res.filter(item => item.type === 2);
					state.options.tmplList2 = res.filter(item => item.type === 1);
				}
			},
			disabledDate(current) {
				return current && current < Dayjs().startOf('day');
			},

			async onConfirm() {
				await components.formRef.value.validate();

				try {
					state.loading = true;
					const params = cloneByMerge(state.formState, DefaultFormState);
					const fetchStore = state.formState.id ? UpdateStore : CreateStore;

					if (params.classOpenDate) {
						params.classOpenDate = +new Date(params.classOpenDate);
					}
					if (params.flowBeginDate) {
						params.flowBeginDate = +new Date(params.flowBeginDate);
					}
					if (params.flowEndDate) {
						params.flowEndDate = +new Date(params.flowEndDate);
					}
					if (params?.customerTags.length) {
						params.customerTags = params.customerTags.join(',');
					}

					if (params.flowBeginDate > params.flowEndDate) {
						MUtils.toast('流量承接开始日期不能大于结束日期', MESSAGE_TYPE.warning);
						state.loading = false;
						return;
					}

					await fetchStore.request(params).getData();

					emit('refresh');
					state.visible = false;
					MUtils.toast('保存成功', MESSAGE_TYPE.success);
				} finally {
					state.loading = false;
				}
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods
		};
	}
});
</script>
