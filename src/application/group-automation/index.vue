<template>
	<pm-effi :controller="controller">
		<pm-search>
			<pm-search-single
				:span="3"
				show-search
				optionFilterProp="employeeMixName"
				:antdProps="{
					placeholder: '企微账号',
					fieldNames: { label: 'employeeMixName', value: 'id' }
				}"
				data-index="employeeId"
				xtype="SELECT"
			/>
			<pm-search-single
				show-search
				optionFilterProp="name"
				:antdProps="{
					placeholder: '模版',
					fieldNames: { label: 'name', value: 'id' }
				}"
				data-index="msgTemplateId"
				xtype="SELECT"
				dropdownClassName="select-full-text"
			/>
			<pm-search-single
				:span="5"
				:antd-props="{
					placeholder: ['开班时间', '结束时间']
				}"
				xtype="RANGEPICKER"
				data-index="classOpenDateFrom|classOpenDateTo"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '状态'
				}"
				data-index="status"
				xtype="SELECT"
			/>
		</pm-search>
		<pm-table
			:columns="COLUMNS"
			:use-custom-column="true"
			:operations-fixed="false"
			:operations-width="120"
			:sort-num="false"
			:scroll="{
				x: 'max-content'
			}"
		>
			<template #beforeCustom>
				<m-button type="primary" @click="onCreate()">添加</m-button>
			</template>
			<template #customerTags="{ record }">
				<m-button type="link" v-if="record.customerTags.length > 5" @click="viewWecomTag(record)">
					{{ record.customerTags.length }}个标签
				</m-button>
				<div v-else style="max-width: 300px">
					{{ record.customerTags.map(item => item.tagName).join('，') }}
				</div>
			</template>
			<template #count="{ record }">
				<m-button type="link" @click="viewCount(record)">查看</m-button>
			</template>
			<template #operations="{ record }">
				<m-button type="link" @click="onEdit(record)" v-if="record.status === 1">编辑</m-button>
				<m-button type="link" @click="onLaunch(record)" v-if="record.status === 1">发送</m-button>
			</template>
		</pm-table>
	</pm-effi>

	<edit-comp ref="editRef" @refresh="onRefresh"></edit-comp>
</template>
<script lang="ts">
import { defineComponent, ref, reactive, toRefs } from 'vue';
import { MESSAGE_TYPE, MUtils, ModelController } from 'admin-library';

import EditComp from './comps/edit-dialog/index.vue';
import { confirmMessageBox } from '@/shard/utils';
import { COLUMNS } from './config';
import { STATUS_STORE } from './constant';
import { ListStore as EmployeeListStore } from '@/application/promoter/store';
import { ListStore as TmplListStore } from '@/application/group-automaiton-tmpl/store';
import { ListStore, PublishStore, GetGroupCountStore } from './store';
import { ItemResponse } from './types';

export default defineComponent({
	components: {
		EditComp
	},
	setup() {
		const components = {
			editRef: ref<InstanceType<typeof EditComp>>()
		};

		const constants = {
			COLUMNS
		};

		const state = reactive({});

		const controller = new ModelController({
			table: {
				store: ListStore
			},
			search: {
				employeeId: {
					store: EmployeeListStore
				},
				msgTemplateId: {
					store: TmplListStore
				},
				status: {
					store: STATUS_STORE
				}
			}
		});
		controller.tableRequest();

		controller.search.employeeId.onRequest.use(params => {
			params = {
				...params,
				enableAutomaticHosting: true,
				limit: 9999
			};

			return params;
		});

		controller.search.msgTemplateId.onRequest.use(params => {
			params = {
				...params,
				limit: 9999
			};

			return params;
		});

		const methods = {
			onCreate() {
				components.editRef.value.open();
			},
			async viewCount(row: ItemResponse) {
				const res = await GetGroupCountStore.request({ groupId: row.groupId }).getData();
				MUtils.alert({
					title: '群成员数',
					content: `${res.length}人`
				});
			},
			async onLaunch(row: ItemResponse) {
				await confirmMessageBox('确认发起任务吗？');

				await PublishStore.request({ id: row.id }).getData();
				methods.onRefresh();
				MUtils.toast('发起任务成功', MESSAGE_TYPE.success);
			},
			async viewWecomTag(row: ItemResponse) {
				row.customerTags;
				let contentStr = '--';
				if (row.customerTags.length) {
					contentStr = row.customerTags
						.map(item => {
							return `<span style="box-sizing: border-box;display: inline-block;white-space: nowrap;padding: 3px 7px;margin-top: 8px;margin-right: 8px;min-width: 96px;text-align: center;color: #0081ff;background-color: #c5e8ff;">${item.tagName}</span>`;
						})
						.join('');
				}
				MUtils.alert({
					title: '接收标签',
					content: contentStr
				});
			},
			onEdit(row: ItemResponse) {
				components.editRef.value.open(row);
			},
			onRefresh() {
				controller.tableRequest();
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			controller
		};
	}
});
</script>
