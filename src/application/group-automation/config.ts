import { TableColumn, ColumnXtype, TableDateFormat } from 'admin-library';
import { ColumnWidthEnum } from '@/shard/constant';
import { STATUS_MAP } from './constant';

export const COLUMNS: TableColumn[] = [
	{
		title: '#',
		dataIndex: 'id',
		fixed: 'left'
	},
	{
		title: '班群名称',
		dataIndex: 'groupName',
		fixed: 'left'
	},
	{
		title: '群主',
		dataIndex: 'ownerEmployeeName'
	},
	{
		title: '群创建时间',
		dataIndex: 'groupCreateTime',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	},
	{
		title: '开班时间',
		dataIndex: 'classOpenDate',
		width: ColumnWidthEnum.DATEDAY
	},
	{
		title: '班群周期',
		dataIndex: 'classPeriodDays',
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '群消息模版',
		dataIndex: 'groupTemplateName',
		width: 160
	},
	{
		title: '客户跟进消息模版',
		dataIndex: 'customerTemplateName',
		width: 160
	},
	{
		title: '客户跟进接收对象',
		dataIndex: 'customerTags',
		xtype: ColumnXtype.CUSTOM,
		width: 180
	},
	{
		title: '群成员总数',
		dataIndex: 'count',
		xtype: ColumnXtype.CUSTOM,
		width: ColumnWidthEnum.TEXT5
	},
	{
		title: '状态',
		dataIndex: 'status',
		render: data => {
			return STATUS_MAP[data];
		},
		width: ColumnWidthEnum.TEXT5
	},
	{
		title: '创建人',
		dataIndex: 'createUserName',
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '创建时间',
		dataIndex: 'createTime',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	}
];
