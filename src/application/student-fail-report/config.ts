import { TableColumn, TableDateFormat } from 'admin-library';
import { renderTextWithColor } from '@/shard/utils';
import { ColumnWidthEnum } from '@/shard/constant';
import { STATUS_OPTIONS } from './constant';

export const COLUMNS: TableColumn[] = [
	{
		title: '#',
		dataIndex: 'id',
		fixed: 'left'
	},
	{
		title: '汇报讲师',
		dataIndex: 'customerNickName',
		fixed: 'left'
	},
	{
		title: '讲师类型',
		dataIndex: 'customerNo'
	},
	{
		title: '汇报时间',
		dataIndex: 'draftTime',
		type: 'date',
		dateFormat: TableDateFormat.SECONDS,
		width: ColumnWidthEnum.DATESECONDS
	},
	{
		title: '学员姓名',
		dataIndex: 'promoterName',
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '课程ID',
		dataIndex: 'promoterNo',
		width: ColumnWidthEnum.TEXT4
	},
	{
		title: '课程主题',
		dataIndex: 'productName'
	},
	{
		title: '课程状态',
		dataIndex: 'status',
		render: data => {
			return renderTextWithColor(data, STATUS_OPTIONS);
		},
		width: ColumnWidthEnum.TEXT4
	},
];
