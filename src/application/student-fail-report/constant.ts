import { getMapfromArray, getStorefromArray } from '@/shard/utils';
import { ColorEnum } from '@/shard/constant';

export const STATUS_OPTIONS = [
	{
		label: '待确认',
		value: 1,
		styleclass: ColorEnum.warning
	},
	{
		label: '已支付',
		value: 2,
		styleclass: ColorEnum.primary
	},
	{
		label: '已生效',
		value: 10,
		styleclass: ColorEnum.success
	},
	{
		label: '已废弃',
		value: 99,
		styleclass: ColorEnum.info
	}
];
export const STATUS_MAP = getMapfromArray(STATUS_OPTIONS);
export const STATUS_STORE = getStorefromArray(STATUS_OPTIONS);
