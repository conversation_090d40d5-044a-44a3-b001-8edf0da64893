<template>
	<pm-effi :controller="controller">
		<pm-search>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '课程ID'
				}"
				data-index="promoterNo"
				xtype="INPUT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '订单状态'
				}"
				data-index="status"
				xtype="SELECT"
			/>
		</pm-search>

		<pm-table
			:columns="COLUMNS"
			:use-custom-column="true"
			:operations="true"
			:sort-num="false"
			:scroll="{
				x: 'max-content'
			}"
		>
			<template #operations="{ record }">
				<m-button danger type="link" @click="onCancel(record)">异常终止</m-button>
			</template>
		</pm-table>
	</pm-effi>
	<edit-cancel-comp ref="editCancelRef" @refresh="onRefresh"></edit-cancel-comp>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { ModelController } from 'admin-library';
import EditCancelComp from '@/application/courses/comps/edit-cancel-dialog/index.vue';

import { COLUMNS } from './config';
import { STATUS_STORE } from './constant';
import { ListStore } from './store';

export default defineComponent({
	components: {
		EditCancelComp
	},

	setup() {
		const components = {
			editCancelRef: ref<InstanceType<typeof EditCancelComp>>()
		};

		const constants = {
			COLUMNS
		};

		const state = reactive({});

		const controller = new ModelController({
			table: {
				store: ListStore
			},
			search: {
				status: {
					store: STATUS_STORE
				}
			}
		});
		controller.tableRequest();

		const methods = {
			async onCancel(row) {
				components.editCancelRef.value.open(row);
			},
			onRefresh() {
				controller.tableRequest();
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			controller
		};
	}
});
</script>
