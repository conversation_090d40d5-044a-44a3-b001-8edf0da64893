<template>
	<pm-search-single
		v-if="compType === 'search'"
		:model-value="value"
		:span="3"
		show-search
		:antdProps="{
			filterOption: false,
			placeholder: '学员',
			fieldNames: { label: 'mixName', value: dataIndex },
			notFoundContent: '使用学员姓名搜索',
			onSearch: onSearchStudent,
			onChange: onHandleChange,
			onCompositionstart: handleCompositionStart,
			onCompositionend: handleCompositionEnd,
			...antdProps
		}"
		:data-index="dataIndex"
		xtype="SELECT"
	/>
	<template v-else>
		<m-select
			show-search
			:filterOption="false"
			:value="value"
			:options="options"
			:fieldNames="{ label: 'mixName', value: dataIndex }"
			notFoundContent="使用学员姓名搜索"
			@change="onHandleChange"
			@search="onSearchStudent"
			@compositionstart="handleCompositionStart"
			@compositionend="handleCompositionEnd"
			placeholder="学员"
			v-bind="antdProps"
		/>
	</template>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, watch } from 'vue';

import { StudentListStore } from '@/application/student/store';

export default defineComponent({
	props: {
		dataIndex: {
			type: String,
			isRequired: false
		},
		value: {
			type: String,
			isRequired: false
		},
		compType: {
			type: String,
			isRequired: false
		},
		antdProps: {
			type: Object,
			default: () => ({})
		}
	},
	setup(props, { emit }) {
		const constants = {};
		const state = reactive({
			isComposing: false,
			options: []
		});
		let lastSearchWord = '';

		watch(
			() => props.value,
			val => {
				if (state.options.filter(item => item[props.dataIndex] === val).length === 0) {
					methods.init();
				}
			}
		);

		const methods = {
			async init() {
				if (props.value) {
					let list = [];
					let res = await StudentListStore.request({
						[props.dataIndex]: props.value
					}).getData();
					if (res) {
						list = res;
					}
					state.options = list;
					emit('onSearchStudent', list);
					methods.emitSelectChange(props.value);
				}
			},
			onHandleChange(val) {
				emit('update:value', val);
				methods.emitSelectChange(val);
			},
			emitSelectChange(val) {
				let studentData = {};
				if (val && state.options?.length) {
					let student = state.options.find(item => {
						return item[props.dataIndex] === val;
					});
					studentData = student || {};
				}
				emit('onSelectChange', val, studentData);
			},
			async onSearchStudent(val) {
				if (state.isComposing) {
					return;
				}
				let list = [];
				lastSearchWord = val;
				if (val) {
					let res = await StudentListStore.request({
						name: val
					}).getData();
					if (res) {
						list = res;
					}
				}
				if (lastSearchWord === val) {
					state.options = list;
					emit('onSearchStudent', list);
				}
			},
			handleCompositionStart() {
				state.isComposing = true;
			},
			handleCompositionEnd(event) {
				state.isComposing = false;
				methods.onSearchStudent(event.target.value);
			}
		};
		methods.init();

		return {
			...toRefs(state),
			...constants,
			...methods
		};
	}
});
</script>
