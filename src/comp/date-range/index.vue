<template>
	<pm-search-single
		:span="5"
		:model-value="hackValue || value"
		:antd-props="{
			disabledDate: disabledDate,
			onChange: onChange,
			onOpenChange: onOpenChange,
			onCalendarChange: onCalendarChange,
			...antdProps
		}"
		xtype="RANGEPICKER"
		:rangeOptions="[]"
		:data-index="dataIndex"
	/>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue';

export default defineComponent({
	props: {
		dataIndex: {
			type: String,
			isRequired: false
		},
		// value: {
		// 	type: String,
		// 	isRequired: false
		// },
		antdProps: {
			type: Object,
			default: () => ({})
		},
		maxSpan: {
			type: Number,
			default: 7
		}
	},
	setup(props) {
		const constants = {};
		const state = reactive({
			hackValue: [],
			value: [],
			dates: []
		});

		const methods = {
			disabledDate(current) {
				if (!state.dates || (state.dates as any).length === 0) {
					return false;
				}
				const tooLate = state.dates[0] && current.diff(state.dates[0], 'days') > props.maxSpan;
				const tooEarly = state.dates[1] && state.dates[1].diff(current, 'days') > props.maxSpan;
				return tooEarly || tooLate;
			},
			onChange(val) {
				// emit('update:value', val);
				state.value = val;
			},
			onOpenChange(open) {
				if (open) {
					state.dates = [] as any;
					state.hackValue = [] as any;
				} else {
					state.hackValue = undefined;
				}
			},
			onCalendarChange(val) {
				state.dates = val;
			}
		};

		return {
			...toRefs(state),
			...constants,
			...methods
		};
	}
});
</script>
