<template>
	<div style="border: 1px solid #ccc">
		<Toolbar
			style="border-bottom: 1px solid #ccc"
			:editor="editorRef"
			:defaultConfig="toolbarConfig"
			:mode="mode"
		/>
		<Editor
			style="overflow-y: hidden; height: 500px"
			v-model="showValue"
			:defaultConfig="editorConfig"
			:mode="mode"
			@onCreated="handleCreated"
		/>
	</div>
</template>
<script>
import { toRefs, onBeforeUnmount, shallowRef, reactive, defineComponent, watch } from 'vue';
import '@wangeditor/editor/dist/css/style.css';
import { Boot } from '@wangeditor/editor';
import { Editor, Toolbar } from '@wangeditor/editor-for-vue';
import { PaasPostMessage } from 'admin-library';

const imageToHtmlConf = {
	type: 'image',
	elemToHtml: function (elemNode) {
		const { src, alt = '', href = '', style = {} } = elemNode || {};
		const { width = '', height = '' } = style;

		// 就为了加上最大宽度
		let styleStr = 'max-width: 100%;';
		if (width) {
			styleStr += `width: ${width};`;
		}
		if (height) {
			styleStr += `height: ${height};`;
		}
		return `<img src="${src}" alt="${alt}" data-href="${href}" style="${styleStr}" />`;
	}
};
Boot.registerElemToHtml(imageToHtmlConf);

export default defineComponent({
	components: { Editor, Toolbar },
	props: {
		value: {
			type: String
		}
	},
	emits: ['update:value'],
	setup(props, { emit }) {
		const components = {
			// 编辑器实例，必须用 shallowRef
			editorRef: shallowRef()
		};
		const constants = {
			mode: 'default'
		};
		const state = reactive({
			showValue: props.value,
			toolbarConfig: {},
			editorConfig: {
				placeholder: '请输入内容...',
				// 修改 uploadImage 菜单配置
				MENU_CONF: {
					uploadImage: {
						async customUpload(file, insertFn) {
							const imgUrl = await methods.uploadOssFile(file);
							insertFn(imgUrl, file.name);
						}
					},
					uploadVideo: {
						async customUpload(file, insertFn) {
							const videoUrl = await methods.uploadOssFile(file);
							insertFn(videoUrl);
						}
					}
				}
			}
		});

		const methods = {
			handleCreated(editor) {
				components.editorRef.value = editor;
			},
			async uploadOssFile(file, progressCallback) {
				let type = file.type.split('/')[0] === 'image' ? 'image' : 'file';
				return new Promise((resolve, rejects) => {
					PaasPostMessage.post('base://file.upload.old', file, {
						url: `https://upload-image.kakamobi.cn/api/admin/upload/file.htm?wenaho=1&bucket=jiakao-web&type=${type}`
					}).on(res => {
						if (res.success) {
							let url = res.response[0]?.url;
							if (url) {
								resolve(url);
							} else {
								rejects(res.response);
							}
						} else if (res.errorCode || res.errorCode === 0) {
							rejects(res.response);
						} else {
							progressCallback && progressCallback(Math.ceil(res.progress));
						}
					});
				});
			}
		};
		watch(
			() => state.showValue,
			val => {
				emit('update:value', val);
			}
		);

		// 组件销毁时，也及时销毁编辑器
		onBeforeUnmount(() => {
			const editor = components.editorRef.value;
			if (editor === null) {
				return;
			}
			editor.destroy();
		});

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods
		};
	}
});
</script>
