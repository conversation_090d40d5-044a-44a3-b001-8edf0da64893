<template>
	<pm-search-single
		v-if="compType === 'search'"
		:model-value="value"
		:span="3"
		show-search
		:antdProps="{
			filterOption: false,
			placeholder: '讲师',
			fieldNames: { label: 'lecturerMixName', value: 'id' },
			notFoundContent: '使用讲师对外昵称搜索',
			onSearch: onSearchLecturer,
			onChange: onHandleChange,
			onCompositionstart: handleCompositionStart,
			onCompositionend: handleCompositionEnd,
			...antdProps
		}"
		:data-index="dataIndex"
		xtype="SELECT"
	/>
	<template v-else>
		<m-select
			show-search
			:filterOption="false"
			:value="value"
			:options="options"
			:fieldNames="{ label: 'lecturerMixName', value: 'id' }"
			notFoundContent="使用讲师对外昵称搜索"
			@change="onHandleChange"
			@search="onSearchLecturer"
			@compositionstart="handleCompositionStart"
			@compositionend="handleCompositionEnd"
			placeholder="讲师"
			v-bind="{ ...$attrs, ...antdProps }"
		/>
		<div v-if="showDetail" class="mt10" style="display: flex; flex-wrap: wrap">
			<template v-if="lecturerData.id">
				<div>类型：{{ TYPE_MAP[lecturerData.type] }}；</div>
				<div>层级：{{ LAYER_MAP[lecturerData.layer] }}；</div>
				<div>等级：{{ lecturerData.levelName }}；</div>
				<div>主管老师：{{ lecturerData.dockingPersonMixName }}；</div>
				<div>上岗状态：{{ STATUS_MAP[lecturerData.status] }}；</div>
			</template>
		</div>
	</template>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, watch } from 'vue';

import { LAYER_MAP, TYPE_MAP } from '@/application/part-time-teacher/constant';
import { STATUS_MAP } from '@/application/promoter/constant';
import { ListStore as TeacherListStore } from '@/application/part-time-teacher/store';
import { ItemResponse } from '@/application/part-time-teacher/types';

export default defineComponent({
	props: {
		dataIndex: {
			type: String,
			isRequired: false
		},
		value: {
			type: String,
			isRequired: false
		},
		compType: {
			type: String,
			isRequired: false
		},
		antdProps: {
			type: Object,
			default: () => ({})
		},
		showDetail: {
			type: Boolean,
			isRequired: false
		},
		requestParams: {
			type: Object,
			isRequired: false
		}
	},
	setup(props, { emit }) {
		const constants = {
			LAYER_MAP,
			TYPE_MAP,
			STATUS_MAP
		};
		const state = reactive({
			isComposing: false,
			options: [],
			lecturerData: {} as ItemResponse
		});
		let lastSearchWord = '';

		watch(
			() => props.value,
			val => {
				if (state.options.filter(item => item.id === val).length === 0) {
					methods.init();
				}
			}
		);

		const methods = {
			async init() {
				if (props.value) {
					let list = [];
					let res = await TeacherListStore.request({
						id: props.value,
						...props.requestParams
					}).getData();
					if (res) {
						list = res;
					}
					state.options = list;
					emit('onSearchLecturer', list);
					methods.emitSelectChange(props.value);
				}
			},
			onHandleChange(val) {
				emit('update:value', val);
				methods.emitSelectChange(val);
			},
			emitSelectChange(val) {
				let lecturerData = {};
				if (val && state.options?.length) {
					let lecturer = state.options.find(item => {
						return item.id === val;
					});
					lecturerData = lecturer || {};
				}
				emit('onSelectChange', val, lecturerData);
				state.lecturerData = lecturerData as ItemResponse;
			},
			async onSearchLecturer(val) {
				if (state.isComposing) {
					return;
				}
				let list = [];
				lastSearchWord = val;
				if (val) {
					let res = await TeacherListStore.request({
						nickName: val,
						...props.requestParams
					}).getData();
					if (res) {
						list = res;
					}
				}
				if (lastSearchWord === val) {
					state.options = list;
					emit('onSearchLecturer', list);
				}
			},
			handleCompositionStart() {
				state.isComposing = true;
			},
			handleCompositionEnd(event) {
				state.isComposing = false;
				methods.onSearchLecturer(event.target.value);
			}
		};
		methods.init();

		return {
			...toRefs(state),
			...constants,
			...methods
		};
	}
});
</script>
