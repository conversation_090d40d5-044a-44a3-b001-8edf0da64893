<template>
	<pm-table
		:columns="tableColumns"
		:store="tableData"
		:use-custom-column="true"
		:pagination="false"
		:operations-fixed="false"
		:sort-num="false"
		:antd-props="{
			showHeader: showHeader
		}"
		style="margin-top: 0"
		class="advanced-table"
	>
		<template #headerCell="{ title, column }">
			<slot name="headerCell" :title="title" :column="column"></slot>
		</template>
		<template v-slot:[item.dataIndex]="{ value, index }" v-for="item in tableColumns">
			<slot
				v-if="data[index]"
				:name="item.dataIndex"
				:text="value"
				:data="data"
				:index="index"
				:lineData="data[index]"
				:dataIndex="item.dataIndex"
				:editable="editable[index]"
			></slot>
		</template>

		<template #operations="{ index }">
			<template v-if="showEditBtn">
				<a v-if="editable[index]" @click="saveRow(index)">保存</a>
				<a v-else @click="edit(index)">编辑</a>
				<a-divider type="vertical" />
			</template>
			<a @click="remove(index)">删除</a>
		</template>
	</pm-table>
	<a-button style="margin-top: 16px; margin-bottom: 8px; width: 100%" type="dashed" @click="newMember()">
		<template #icon><PlusOutlined /></template>
		新增
	</a-button>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, watch } from 'vue';
import { MUtils } from 'admin-library';
import { Store } from '@simplex/simple-store';
import { PlusOutlined } from '@ant-design/icons-vue';
import { Divider, Button } from 'ant-design-vue';

export default defineComponent({
	props: {
		value: {
			type: Array,
			isRequired: true
		},
		columns: {
			type: Array<any>,
			isRequired: true
		},
		formRef: {
			type: Object,
			isRequired: false
		},
		showHeader: {
			type: Boolean,
			default: true,
			isRequired: false
		},
		showEditBtn: {
			type: Boolean,
			default: true,
			isRequired: false
		}
	},
	emits: ['update:value'],
	components: {
		PlusOutlined,
		ADivider: Divider,
		AButton: Button
	},
	setup(props, { emit }) {
		const components = {};

		const constants = {
			tableColumns: [...props.columns]
		};

		const state = reactive({
			data: MUtils.deepClone(props.value),
			editable: props.value.map(() => false),
			tableData: new Store(MUtils.deepClone(props.value))
		});

		const methods = {
			async newMember() {
				await props.formRef.validate();
				state.editable = state.editable.map(() => false);
				state.data.push(
					props.columns.reduce((prevVal, currVal) => {
						prevVal[currVal.dataIndex] = undefined;

						return prevVal;
					}, {})
				);
				state.editable.push(true);
			},
			remove(index) {
				state.data.splice(index, 1);
				state.editable.splice(index, 1);
			},
			async saveRow(index) {
				await props.formRef.validate();
				state.editable[index] = false;
			},
			async edit(index) {
				await props.formRef.validate();
				state.editable = state.editable.map(() => false);
				state.editable[index] = true;
			}
		};

		watch(
			() => state.data,
			data => {
				emit('update:value', data);
				state.tableData = new Store(MUtils.deepClone(data));
			},
			{ deep: true }
		);

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			...props
		};
	}
});
</script>

<style lang="less" scoped>
.advanced-table {
	:deep .before-pm-table {
		display: none;
	}
}
// .advanced-table {
// 	background-color: #000;
// 	:deep(.ant-table.ant-table-middle) {
// 		.ant-table-tbody > tr > td {
// 			padding: 0;
// 		}
// 	}
// }
</style>
