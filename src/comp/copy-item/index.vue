<template>
	<div class="wrap" @click="copy(text)" v-if="text">
		<div class="elli w80">{{ text }}</div>
		<m-button type="link" style="margin-right: 0">
			<copy-outlined />
		</m-button>
	</div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { MUtils } from 'admin-library';
import { CopyOutlined } from '@ant-design/icons-vue';

import { copyText } from '@/shard/utils';

export default defineComponent({
	components: {
		CopyOutlined
	},
	props: {
		text: {
			type: String || Number,
			isRequired: true
		}
	},
	setup(props) {
		const methods = {
			copy(text) {
				if (!text) {
					return;
				}
				const ret = copyText(text);
				if (ret) {
					MUtils.toast('复制成功');
				}
			}
		};

		return {
			...methods,
			...props
		};
	}
});
</script>

<style lang="less" scoped>
.wrap {
	cursor: pointer;
	display: flex;
	color: var(--ant-primary-color);
	&:hover {
		color: var(--ant-primary-color-hover);
	}
	&:hover .ant-btn {
		color: var(--ant-primary-color-hover);
	}
}
</style>
