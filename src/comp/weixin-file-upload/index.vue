<template>
	<m-upload
		:multiple="multiple"
		list-type="picture-card"
		:fileList="fileList"
		:isImageUrl="() => fileType === 'image'"
		:show-upload-list="multiple"
		:max-count="multiple ? 0 : 1"
		:customRequest="() => {}"
		:before-upload="onBeforeUpload"
		@remove="onDelFile"
	>
		<template v-if="!multiple && fileList && fileList.length">
			<div v-for="item in fileList" :key="item.uid" class="file-uploader">
				<div v-if="item.status === 'uploading'" style="margin: 40px 6px 0; width: 100%">
					<m-progress :percent="item.percent" />
				</div>
				<template v-else>
					<div v-if="fileType === 'image'">
						<img style="width: 100%" :src="item.url" />
					</div>
					<div v-else style="margin: 6px 6px 0; word-break: break-all">
						{{ item.name }}
					</div>
					<div class="btns">
						<a :href="item.url" target="_blank" @click.stop>
							<EyeOutlined />
						</a>
						<span @click.stop="onDelFile(item)"><DeleteOutlined /></span>
					</div>
				</template>
			</div>
		</template>
		<div v-else>
			<PlusOutlined />
		</div>
	</m-upload>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue';
import { MESSAGE_TYPE, MUtils, PaasPostMessage } from 'admin-library';
import { debounce, isPlainObject } from 'lodash';
import { PlusOutlined, EyeOutlined, DeleteOutlined } from '@ant-design/icons-vue';
import getUid from 'ant-design-vue/es/vc-upload/uid';

import { PARROT_SPACE_ID } from '@/shard/constant';
import { WeixinUploadStore } from '@/store/common';

const getFileName = function (path) {
	if (!path) {
		return path;
	}
	const parts = path.split('/');
	return parts[parts.length - 1];
};

export default defineComponent({
	components: {
		PlusOutlined,
		EyeOutlined,
		DeleteOutlined
	},
	props: {
		value: {
			type: String,
			isRequired: true
		},
		fileType: {
			type: String,
			isRequired: true
		},
		maxSize: {
			type: Number
		},
		appSpaceId: {
			type: String
		}
	},
	emits: ['update:value'],
	setup(props, { emit }) {
		const constants = {
			multiple: false
		};

		const components = {};

		let value = [];
		if (props.value && isPlainObject(props.value)) {
			value = [props.value];
		} else if (props.value && props.value.length) {
			value = [...props.value];
		}
		const state = reactive({
			fileList: value.map(item => {
				return {
					uid: getUid(),
					url: item.showUrl,
					name: getFileName(item.showUrl),
					encodedData: item.encodedData,
					mediaId: item.mediaId,
					status: 'done'
				};
			}) as Array<any>
		});

		const methods = {
			async uploadWeixinFile(file, progressCallback) {
				const formData = new FormData();
				formData.append('fileName', file.name);
				formData.append('fileType', props.fileType);
				formData.append('file', file);
				const res = await new WeixinUploadStore(progressCallback).request(formData).getData();
				return res.data?.value;
			},
			async uploadSecureFile(file, progressCallback) {
				return new Promise((resolve, rejects) => {
					PaasPostMessage.post('base://file.upload', [file], {
						appSpaceId: props.appSpaceId || PARROT_SPACE_ID,
						useProgress: true
					})
						.on(res => {
							if (res.success) {
								let data = res.response[0];
								if (data) {
									resolve(data);
								} else {
									rejects(res.response);
								}
							} else if (res.errorCode || res.errorCode === 0) {
								rejects(res.response);
							} else {
								progressCallback && progressCallback(Math.ceil(res.progress));
							}
						})
						.catch(() => {
							rejects();
						});
				});
			},
			onBeforeUpload: debounce(async function (file, fileList) {
				let maxSize = props.maxSize;

				for (let i in fileList) {
					let file = fileList[i];
					if (maxSize && file.size > maxSize * 1024 * 1024) {
						MUtils.toast(`文件尺寸不能大于${maxSize}M`, MESSAGE_TYPE.error);
						return false;
					}
				}

				for (let i in fileList) {
					let file = fileList[i];
					const uid = file.uid;
					if (!constants.multiple) {
						state.fileList = [];
					}
					state.fileList.push({
						url: '',
						uid: file.uid,
						status: 'uploading',
						percent: 0,
						name: file.name,
						mediaId: ''
					});
					const index = state.fileList.findIndex(item => item.uid === uid);
					try {
						let progress1 = 0;
						let progress2 = 0;
						const item = state.fileList[index];
						const mediaId = (await methods.uploadWeixinFile(file, progress => {
							progress1 = progress;
							item.percent = (progress1 + progress2) / 2;
						})) as string;
						const result = (await methods.uploadSecureFile(file, progress => {
							progress2 = progress;
							item.percent = (progress1 + progress2) / 2;
						})) as any;
						item.status = 'done';
						item.name = getFileName(result.previewUrl || result.fileName);
						item.mediaId = mediaId;
						item.url = result.previewUrl;
						item.encodedData = result.encodedData;
					} catch (error) {
						state.fileList.splice(index, 1);
						let message = error && error.message;
						MUtils.toast(message || '上传失败，请重新上传', MESSAGE_TYPE.error);
						return;
					}
				}

				if (constants.multiple) {
					emit('update:value', state.fileList);
				} else {
					emit('update:value', state.fileList[0]);
				}
			}, 50),
			onDelFile({ uid }) {
				const index = state.fileList.findIndex(item => item.uid === uid);
				state.fileList.splice(index, index + 1);

				if (constants.multiple) {
					emit('update:value', state.fileList);
				} else {
					emit('update:value', state.fileList[0]);
				}
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			...props
		};
	}
});
</script>

<style lang="less" scoped>
.file-uploader {
	position: relative;
	display: flex;
	justify-content: center;
	align-items: center;
	overflow: hidden;
	width: 102px;
	height: 102px;
	.btns {
		position: absolute;
		bottom: 0;
		left: 0;
		display: none;
		padding-top: 4px;
		width: 100%;
		height: 26px;
		background-color: rgba(0, 0, 0, 0.5);
	}
	&:hover {
		.btns {
			display: block;
		}
	}
	.anticon {
		margin: 0 4px;
		width: 16px;
		font-size: 16px;
		color: rgba(255, 255, 255, 0.85);
		cursor: pointer;
	}
}
</style>
