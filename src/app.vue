<template>
	<pm-app-wrapper>
		<pm-router-view :router="router"></pm-router-view>
	</pm-app-wrapper>
</template>
<script lang="ts">
import { defineComponent } from 'vue';
import router from './router';

export default defineComponent({
	setup() {
		return {
			router
		};
	}
});
</script>
<style lang="less">
:root {
	--parrot-red-color: #f5222d;
	--parrot-yellow-color: #fadb14;
	--parrot-orange-color: #fa8c16;
	--parrot-cyan-color: #13c2c2;
	--parrot-green-color: #52c41a;
	--parrot-blue-color: #1890ff;
	--parrot-purple-color: #722ed1;
	--parrot-geekblue-color: #2f54eb;
	--parrot-magenta-color: #eb2f96;
	--parrot-volcano-color: #fa541c;
	--parrot-gold-color: #faad14;
	--parrot-lime-color: #a0d911;
}

.blue {
	color: var(--ant-primary-color);
}
.green {
	color: var(--ant-success-color);
}
.gold {
	color: var(--ant-warning-color);
}
.red {
	color: var(--ant-error-color);
}

.pink {
	color: var(--parrot-magenta-color);
}
.yellow {
	color: var(--parrot-yellow-color);
}
.orange {
	color: var(--parrot-orange-color);
}
.cyan {
	color: var(--parrot-cyan-color);
}
.purple {
	color: var(--parrot-purple-color);
}
.geekblue {
	color: var(--parrot-geekblue-color);
}
.magenta {
	color: var(--parrot-magenta-color);
}
.volcano {
	color: var(--parrot-volcano-color);
}
.lime {
	color: var(--parrot-lime-color);
}

.ant-tag {
	&.ant-tag-blue,
	&.ant-tag-primary {
		color: var(--ant-primary-color);
		background: #e6f7ff;
		border-color: #91d5ff;
	}
	&.ant-tag-blue-inverse,
	&.ant-tag-primary-inverse {
		color: #fff;
		background-color: var(--ant-primary-color);
		border-color: transparent;
	}

	&.ant-tag-green,
	&.ant-tag-success {
		color: var(--ant-success-color);
		background: #f6ffed;
		border-color: #b7eb8f;
	}

	&.ant-tag-green-inverse,
	&.ant-tag-success-inverse {
		color: #fff;
		background-color: var(--ant-success-color);
		border-color: transparent;
	}

	&.ant-tag-gold,
	&.ant-tag-warning {
		color: var(--ant-warning-color);
		background: #fffbe6;
		border-color: #ffe58f;
	}
	&.ant-tag-gold-inverse,
	&.ant-tag-warning-inverse {
		color: #fff;
		background-color: var(--ant-warning-color);
		border-color: transparent;
	}

	&.ant-tag-red,
	&.ant-tag-danger {
		color: var(--ant-error-color);
		background: #fff1f0;
		border-color: #ffa39e;
	}
	&.ant-tag-red-inverse,
	&.ant-tag-danger-inverse {
		color: #fff;
		background-color: var(--ant-error-color);
		border-color: transparent;
	}

	&.ant-tag-info {
		color: rgba(0, 0, 0, 0.4);
		background-color: #fafafa;
		border-color: #d9d9d9;
	}
	&.ant-tag-info-inverse {
		color: #fff;
		background-color: var(--color-disable);
		border-color: transparent;
	}
}

body {
	background-color: transparent !important;
}
#app {
	position: relative;
	height: 100%;
	background-color: #edf0f4;
	box-sizing: border-box;
}
.app-wrapper {
	height: 100%;
	background: #fff;
	border-radius: 4px;
}
.w80 {
	width: 80px;
}
.w120 {
	width: 120px;
}
.w150 {
	width: 150px;
}
.w200 {
	width: 200px;
}
.mt10 {
	margin-top: 10px;
}
.mr10 {
	margin-right: 10px;
}
.pm-table .ant-table-cell .ant-btn {
	padding: 0;
	margin-right: 10px;
	height: auto;
	border: 0;
}
.pm-table .ant-table-cell.operations-column > .ant-btn {
	padding: 0;
}
.pm-table-btns {
	padding-top: 0;
	margin-top: 0 !important;
	margin-bottom: 20px;
}
.pm-table-buttons .ant-btn + .ant-btn {
	margin-left: 10px;
}
.before-pm-table {
	.pm-table-buttons {
		margin-top: 15px;
		flex-wrap: wrap;
	}
}
.operations-column {
	.ant-dropdown-trigger {
		padding-right: 10px;
		white-space: nowrap;
	}
}

// .pm-form-search {
// 	.ant-row-top {
// 		display: flex;
// 		justify-content: flex-end;
// 		margin-right: 0 !important;
// 		.ant-col:first-child {
// 			flex: 1;
// 			display: block;
// 			max-width: 100%;
// 			.ant-btn {
// 				margin-right: 6px;
// 			}
// 		}

// 		.search-btn-col {
// 			min-width: auto !important;
// 			max-width: 295px !important;
// 			flex: 0 !important;
// 		}
// 	}
// 	.search-btn-container {
// 		display: flex;
// 		justify-content: flex-end;
// 		margin-left: 10px;
// 		min-width: 155px !important;
// 	}

// 	.ant-row {
// 		> :first-child {
// 			.pm-search-single-item {
// 				display: block;
// 				height: 32px;
// 			}
// 		}
// 	}
// }

.table-no-hover {
	.ant-table-tbody > tr.ant-table-row:hover > td {
		background-color: transparent;
	}
	.ant-table-tbody > tr.table-striped:hover > td {
		background-color: #f6f8fa;
	}
}

.selected-row > td {
	background-color: var(--ant-error-color-deprecated-bg) !important;
}
.select-full-text {
	.ant-select-item-option-content {
		overflow: initial;
		text-overflow: initial;
		white-space: initial;
	}
}

.global-loading {
	.ant-modal-confirm {
		width: 64px;
		.ant-modal-content {
			background-color: rgba(255, 255, 255, 0.8);
		}
		.ant-modal-body {
			padding: 19px;
			line-height: 1;
		}
		.ant-modal-confirm-content {
			display: none;
		}
		.ant-modal-confirm-btns {
			display: none;
		}
	}
}
</style>
