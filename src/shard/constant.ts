import { getMapfromArray, getStorefromArray } from '@/shard/utils';

export const PARROT_SPACE_ID = 'a9e090cea9e0cec18dce';

export enum ColorEnum {
	primary = 'primary',
	success = 'success',
	warning = 'warning',
	danger = 'danger',
	info = 'info',

	pink = 'pink',
	red = 'red',
	yellow = 'yellow',
	orange = 'orange',
	cyan = 'cyan',
	green = 'green',
	blue = 'blue',
	purple = 'purple',
	geekblue = 'geekblue',
	magenta = 'magenta',
	volcano = 'volcano',
	gold = 'gold',
	lime = 'lime'
}

export enum ColumnWidthEnum {
	'TEXT2' = 56,
	'TEXT3' = 70,
	'TEXT4' = 84,
	'TEXT5' = 98,
	'TEXT6' = 112,
	'TEXT8' = 156,
	'DATESECONDS' = 156,
	'DATEDAY' = 100
}

export const SHOW_COLOR_OPTIONS = [
	{
		label: 'primary,blue',
		value: 1,
		styleclass: ColorEnum.primary
	},
	{
		label: 'success,green',
		value: 2,
		styleclass: ColorEnum.success
	},
	{
		label: 'warning,gold',
		value: 3,
		styleclass: ColorEnum.warning
	},
	{
		label: 'danger,red',
		value: 4,
		styleclass: ColorEnum.danger
	},
	{
		label: 'info',
		value: 5,
		styleclass: ColorEnum.info
	},
	{
		label: 'pink',
		value: 6,
		styleclass: ColorEnum.pink
	},
	{
		label: 'yellow',
		value: 7,
		styleclass: ColorEnum.yellow
	},
	{
		label: 'orange',
		value: 8,
		styleclass: ColorEnum.orange
	},
	{
		label: 'cyan',
		value: 9,
		styleclass: ColorEnum.cyan
	},
	{
		label: 'purple',
		value: 10,
		styleclass: ColorEnum.purple
	},
	{
		label: 'geekblue',
		value: 11,
		styleclass: ColorEnum.geekblue
	},
	{
		label: 'magenta',
		value: 12,
		styleclass: ColorEnum.magenta
	},
	{
		label: 'volcano',
		value: 13,
		styleclass: ColorEnum.volcano
	},
	{
		label: 'lime',
		value: 14,
		styleclass: ColorEnum.lime
	}
];

export const RADIO_OPTIONS = [
	{
		label: '否',
		value: false,
		styleclass: ColorEnum.danger
	},
	{
		label: '是',
		value: true,
		styleclass: ColorEnum.success
	}
];
export const RADIO_MAP = getMapfromArray(RADIO_OPTIONS);
export const RADIO_STORE = getStorefromArray(RADIO_OPTIONS);

export const KEMU_OPTIONS = [
	{
		label: '科一',
		value: 1
	},
	{
		label: '科四',
		value: 4
	}
];
export const KEMU_MAP = getMapfromArray(KEMU_OPTIONS);
export const KEMU_STORE = getStorefromArray(KEMU_OPTIONS);

export const FULL_KEMU_OPTIONS = [
	{
		label: '科一',
		value: '1'
	},
	{
		label: '科二',
		value: '2'
	},
	{
		label: '科三',
		value: '3'
	},
	{
		label: '科四',
		value: '4'
	}
];
export const FULL_KEMU_MAP = getMapfromArray(FULL_KEMU_OPTIONS);
export const FULL_KEMU_STORE = getStorefromArray(FULL_KEMU_OPTIONS);

export const TUROR_KEMU_OPTIONS = [
	{
		label: '未知',
		value: '-1'
	},
	{
		label: '科一',
		value: '10'
	},
	{
		label: '科四',
		value: '20'
	},
	{
		label: '扣满12分',
		value: '30'
	},
	{
		label: '恢复驾驶证',
		value: '40'
	}
];
export const TUROR_KEMU_MAP = getMapfromArray(TUROR_KEMU_OPTIONS);
export const TUROR_KEMU_STORE = getStorefromArray(TUROR_KEMU_OPTIONS);

export const EXAM_KEMU_OPTIONS = [
	{
		label: '科一',
		value: 10
	},
	{
		label: '科四',
		value: 20
	},
	{
		label: '扣满12分',
		value: 30
	},
	{
		label: '恢复驾驶证',
		value: 40
	}
];
export const EXAM_KEMU_MAP = getMapfromArray(EXAM_KEMU_OPTIONS);
export const EXAM_KEMU_STORE = getStorefromArray(EXAM_KEMU_OPTIONS);

export const CAR_TYPE_OPTIONS = [
	{
		label: '小车',
		value: 'car'
	},
	{
		label: '货车',
		value: 'truck'
	},
	{
		label: '客车',
		value: 'bus'
	},
	{
		label: '摩托车',
		value: 'moto'
	}
	// {
	// 	label: '轻型牵引挂车',
	// 	value: 'light_trailer'
	// }
];
export const CAR_TYPE_MAP = getMapfromArray(CAR_TYPE_OPTIONS);
export const CAR_TYPE_STORE = getStorefromArray(CAR_TYPE_OPTIONS);

export const SCENE_OPTIONS = [
	{
		label: '普通场景',
		value: '101'
	},
	{
		label: '扣满12分',
		value: '102'
	}
];
export const SCENE_MAP = getMapfromArray(SCENE_OPTIONS);
export const SCENE_STORE = getStorefromArray(SCENE_OPTIONS);

export const STATUS_OPTIONS = [
	{
		label: '下线',
		value: 1,
		styleclass: ColorEnum.danger
	},
	{
		label: '上线',
		value: 2,
		styleclass: ColorEnum.success
	}
];
export const STATUS_MAP = getMapfromArray(STATUS_OPTIONS);
export const STATUS_STORE = getStorefromArray(STATUS_OPTIONS);
