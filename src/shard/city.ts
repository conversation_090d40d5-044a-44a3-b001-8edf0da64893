import { CityJson } from '@/store/common';
import { MUtils } from 'admin-library';

interface CityDataResponse {
	count: string;
	info: string;
	infocode: string;
	status: string;
	districts: Array<CityModel>;
}

export interface CityModel {
	adcode: string;
	center: string;
	districts: Array<CityModel>;
	level: string;
	name: string;
}

export enum CityLevelEnum {
	/** 省级 */
	province,

	/** 市级 */
	city,

	/** 区级 */
	district
}

const CityJsonStore = new CityJson({
	ajaxOptions: {
		timeout: 1000,
		xhrFields: {
			withCredentials: false
		}
	}
});

const thirdLevel = { district: true, street: true };

const firstLevel = { province: true };

const secondLevel = { city: true };

const codeSplitSheng = (code: string) => {
	return code.slice(0, 2);
};

const getLevel = (level: CityLevelEnum) => {
	switch (level) {
		case CityLevelEnum.province:
			return firstLevel;

		case CityLevelEnum.city:
			return secondLevel;

		default:
			return thirdLevel;
	}
};

// 处理直辖市，比如 北京市
// 如果读取省市，那就变成 [北京市，北京市]；
// 如果读取省市区，下面就是区，那就变成 [北京市，东城区]；
const processZhiXiaShi = (level: CityLevelEnum = CityLevelEnum.district) => {
	const zhiXiaList = ['110000', '120000', '310000', '500000', '810000', '820000', '710000'];

	const provinces = cityJsonData.districts[0]?.districts || [];

	for (let i = 0; i < provinces.length; i++) {
		const province = provinces[i];

		const { districts, adcode } = province;

		for (let j = 0; j < zhiXiaList.length; j++) {
			const zhiXia = zhiXiaList[j];

			if (zhiXia === adcode) {
				const shi = districts[0];

				if (level === CityLevelEnum.city) {
					province.districts = [{ ...shi, adcode: province.adcode, name: province.name }];
				} else {
					const shiChild = shi.districts ? shi.districts[0] : null;

					const newProvinceDistricts = [];

					if (shi.level === 'city' && shiChild.level === 'district') {
						districts.forEach((item: { districts: any }) => {
							newProvinceDistricts.push(...item.districts);
						});

						// 直辖市下面直接就是区，去掉
						province.districts = newProvinceDistricts;
					}
				}
			}
		}
	}
};

// 处理县级市，比如 仙桃市 下面直接就是街道了，没有区 那就回变成 [湖北省，仙桃市]
const processXianLevelShi = () => {
	const provinces = cityJsonData.districts[0]?.districts || [];

	const deepEach = list => {
		for (let i = 0; i < list.length; i++) {
			const item = list[i];

			const { districts } = item;

			const child = districts ? districts[0] : districts;

			if (child && child.level === 'street') {
				delete item.districts;
			} else {
				districts && deepEach(districts);
			}
		}
	};

	deepEach(provinces);
};

let cityJsonData = null;

const deepFind = (cityData, callback) => {
	const path = [];

	const _find = (list, deep) => {
		for (let i = 0; i < list.length; i++) {
			const item = list[i];

			const { districts } = item;

			const result = callback(item, path, deep);

			path.length = deep + 1;

			path[deep] = item;

			if (result === true) {
				return true;
			} else if (districts) {
				if (_find(districts, deep + 1)) {
					return true;
				}
			}
		}
	};

	_find(cityData, 0);
};

const getLevelData = (data: CityDataResponse, level: CityLevelEnum) => {
	const list = data?.districts[0]?.districts || [];

	const endLevel = getLevel(level);

	const deep = list => {
		if (!list) {
			return;
		}

		for (let i = 0; i < list.length; i++) {
			const item = list[i];

			if (endLevel[item.level]) {
				delete item.districts;
			} else {
				deep(item.districts);
			}
		}
	};

	deep(list);

	return data;
};

/** 初始化省市区数据 */
export const initCityJson = (level: CityLevelEnum = CityLevelEnum.district) => {
	const waiting = new Promise(resolve => {
		CityJsonStore.request()
			.getFullData()
			.then(data => {
				cityJsonData = data;

				processXianLevelShi();

				processZhiXiaShi(level);

				resolve(cityJsonData);
			})
			.catch(() => {
				cityJsonData = null;

				resolve([]);
			});
	});

	cityJsonData = waiting;
};

/**获取省市区数据
 * @param level 省市区级别
 */
export const getCityJsonData = (level: CityLevelEnum = CityLevelEnum.district): Promise<CityDataResponse> => {
	!cityJsonData && initCityJson(level);

	const isWaiting = !!cityJsonData.then;

	const process = (data: CityDataResponse) => getLevelData(MUtils.deepClone(data), level);

	if (isWaiting) {
		return new Promise(resolve => {
			cityJsonData.then(data => resolve(process(data)));
		});
	}

	return Promise.resolve(process(cityJsonData));
};

/** 通过省市区代码查找城市，只传 code 获取 [省，市，区]
 * trace = false 时，只返回 code 的城市
 * onlyCityCode = true 时，只返回城市代码
 */
export function findCityByCode(code: string): CityModel[];
export function findCityByCode(code: string, trace: boolean, onlyCityCode: boolean): (CityModel | string)[];
export function findCityByCode(code: string, trace = true, onlyCityCode?: boolean): (CityModel | string)[] {
	if (!cityJsonData) {
		return [];
	}

	const targetSheng = codeSplitSheng(code);

	const _cityData = cityJsonData.districts[0]?.districts || [];

	let result = null;

	deepFind(_cityData, function (item, tracePath) {
		const adcode = item.adcode;

		const currentSheng = codeSplitSheng(adcode);

		// 只判断是不是同一个省
		if (currentSheng !== targetSheng) {
			return;
		}

		result = trace ? tracePath : item;

		return adcode === code;
	});

	// 只返回城市代码
	if (onlyCityCode && result) {
		let codes = null;

		if (trace) {
			codes = result.map(item => item.adcode);
		} else {
			codes = [result.adcode];
		}

		result = codes;
	}

	return result;
}
