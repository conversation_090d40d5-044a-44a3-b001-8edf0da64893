import { createVNode } from 'vue';
import Dayjs from 'dayjs';
import { Store } from '@simplex/simple-store';
import { MUtils, MESSAGE_TYPE } from 'admin-library';
import { cloneDeep, pick, merge, keyBy, values } from 'lodash';
import { IframePreview } from '@paas/paas-library/src/components/image/utils';
import { Table as ATable, Image as AImage } from 'ant-design-vue';
import { MModal } from '@paas/paas-design';
import { LoadingOutlined } from '@ant-design/icons-vue';
import CopyComp from '@/comp/copy-item/index.vue';

/**
 * 参数转 formData
 * @param obj 普通对象
 * @returns formData 对象
 */
export const paramsToFormData = (obj: Record<string, any>) => {
	const formData = new FormData();

	Object.keys(obj).forEach(key => {
		if (obj[key] instanceof Array) {
			obj[key].forEach(item => {
				formData.append(key, item);
			});
			return;
		}
		formData.append(key, obj[key]);
	});

	return formData;
};

/**
 * 消息确认提示框
 * @param content 文本内容 或 html 字符串
 * @returns
 */
export const confirmMessageBox = async (content: string) => {
	const isConfirm = await MUtils.confirm({
		title: '提示',
		content,
		type: MESSAGE_TYPE.warning,
		confirmText: '确定',
		cancelText: '取消'
	});

	return isConfirm ? Promise.resolve() : Promise.reject('用户选择取消');
};

/**
 * 获取 host
 */
export const getHost = (domain: string) => {
	if (domain.startsWith('http')) {
		return domain;
	}

	// 默认调用 https
	return `https://${domain}`;
};

function selectText(textbox, startIndex, stopIndex) {
	if (textbox.createTextRange) {
		// ie
		const range = textbox.createTextRange();
		range.collapse(true);
		range.moveStart('character', startIndex); // 起始光标
		range.moveEnd('character', stopIndex - startIndex); // 结束光标
		range.select(); // 不兼容苹果
	} else {
		// firefox/chrome
		textbox.setSelectionRange(startIndex, stopIndex);
		textbox.focus();
	}
}

export function copyText(text) {
	let ret = false;
	// 数字没有 .length 不能执行selectText 需要转化成字符串
	const textString = text.toString();
	let input = document.querySelector('#copy-input') as HTMLInputElement;
	if (!input) {
		input = document.createElement('input') as HTMLInputElement;
		input.id = 'copy-input';
		input.readOnly = true; // 防止ios聚焦触发键盘事件
		input.style.position = 'absolute';
		input.style.left = '-1000px';
		input.style.zIndex = '-1000';
		document.body.appendChild(input);
	}

	input.value = textString;
	// ios必须先选中文字且不支持 input.select();
	selectText(input, 0, textString.length);
	if (document.execCommand('copy')) {
		document.execCommand('copy');
		ret = true;
	} else {
		console.log('不兼容');
	}
	input.blur();

	return ret;
}

export function getMapfromArray(options) {
	const obj = {};
	options.forEach(item => {
		obj[item.value] = item.label;
	});
	return obj;
}

export function getStorefromArray(options) {
	const obj = {};
	options.forEach(item => {
		obj[item.value] = item.label;
	});
	return new Store(
		options.map(item => {
			return { key: item.value, value: item.label };
		})
	);
}

export function cloneFromPick(obj, keySource) {
	const clearObj = pick(obj, Object.keys(keySource));
	return cloneDeep(merge({}, keySource, clearObj));
}

export function cloneByMerge(obj, keySource) {
	return cloneDeep(merge({}, keySource, obj));
}

export function mergeByKey(key, ...sources) {
	const merged = merge(...sources.map(item => keyBy(item, key)));
	return values(merged);
}

export function formatDate(date, fmt) {
	if (date) {
		return Dayjs(date).format(fmt);
	} else {
		return '';
	}
}
const timeUnits = [
	['Y', 1000 * 60 * 60 * 24 * 365],
	['M', 1000 * 60 * 60 * 24 * 30],
	['D', 1000 * 60 * 60 * 24],
	['H', 1000 * 60 * 60],
	['m', 1000 * 60],
	['s', 1000],
	['S', 1]
];
function padStart0(num, len) {
	if (String(num).length >= len) {
		return num;
	} else {
		return padStart0('0' + num, len);
	}
}
export function formatTimeStr(duration, format) {
	let leftDuration = duration;
	const escapeRegex = /\[[^\]]*]/g;
	const keepList = (format.match(escapeRegex) || []).map(function (str) {
		return str.slice(1, -1);
	});
	const templateText = format.replace(escapeRegex, '[]');
	const replacedText = timeUnits.reduce(function (current, _ref) {
		const name = _ref[0] as string;
		const unit = _ref[1] as number;
		if (current.indexOf(name) !== -1) {
			const value = Math.floor(leftDuration / unit);
			leftDuration -= value * unit;
			return current.replace(new RegExp(`${name}+`, 'g'), function (match) {
				const len = match.length;
				return padStart0(value, len);
			});
		}
		return current;
	}, templateText);
	let index = 0;
	return replacedText.replace(escapeRegex, function () {
		const match = keepList[index];
		index += 1;
		return match;
	});
}

export function timeToSecond(time) {
	const regex = /(\d+):(\d+):(\d+)/;
	const match = time.match(regex);
	const hours = parseInt(match[1]);
	const minutes = parseInt(match[2]);
	const seconds = parseInt(match[3]);
	return hours * 3600 + minutes * 60 + seconds;
}

const oneDay = 1000 * 60 * 60 * 24;
export const recentSixMouth = () => {
	const end = new Date();
	end.setHours(23, 59, 59, 999);

	const start = new Date();
	start.setTime(start.getTime() - 30 * oneDay * 6);
	start.setHours(0, 0, 0, 0);

	return [start, end];
};

export function toAwait(promise) {
	return promise
		.then(data => {
			return [null, data];
		})
		.catch(err => [err]);
}

export function explodeAnswer(answer: number) {
	const segments = 4;
	const shl = 4;
	const answers: number[] = [];

	for (let i = 0; i < segments; i++) {
		const mask = 1 << (shl + i);
		const part = answer & mask;
		if (part) {
			answers.push(mask);
		}
	}
	return answers;
}
export function openLoading() {
	IframePreview(true, () => ({}));
	const modal = MModal.confirm({
		icon: () =>
			createVNode(LoadingOutlined, {
				style: { fontSize: '26px', color: '#08c' }
			}),
		width: 64,
		centered: true,
		wrapClassName: 'global-loading'
		// appContext,
	});
	return {
		close() {
			modal.destroy();
			IframePreview(false, () => ({}));
		}
	};
}

export function openTextInfo(config) {
	const { title, content } = config;
	IframePreview(true, () => ({}));
	const modal = MModal.info({
		width: 420,
		closable: true,
		title: title,
		content: content,
		// appContext,
		onCancel() {
			// 恢复 iframe
			IframePreview(false, () => ({}));
		}
	});
	return {
		close() {
			modal.destroy();
			IframePreview(false, () => ({}));
		}
	};
}

export function openTableDialog(tableConfig) {
	IframePreview(true, () => ({}));
	const renderContent = createVNode(ATable, tableConfig);
	MModal.info({
		icon: '',
		closable: true,
		maskClosable: true,
		width: 420,
		content: renderContent,
		okButtonProps: {
			type: 'default'
		},
		// appContext,
		onCancel() {
			// 恢复 iframe
			IframePreview(false, () => ({}));
		},
		onOk() {
			// 恢复 iframe
			IframePreview(false, () => ({}));
		}
	});
}

export function renderTextWithColor(status, enumList, config?) {
	config = config || {};
	const { isTag, inverse, isReturnStr } = config;
	const val = enumList.find(item => item.value === status);
	if (val?.styleclass) {
		let styleclass;
		if (isTag) {
			styleclass = `ant-tag ant-tag-${val.styleclass}`;
			if (inverse) {
				styleclass += '-inverse';
			}
		} else {
			styleclass = val.styleclass;
		}
		if (isReturnStr) {
			return `<span class="${styleclass}">${val.label}</span>`;
		} else {
			return createVNode(
				'span',
				{
					class: styleclass
				},
				val.label
			);
		}
	} else {
		return val?.label;
	}
}

export function renderImageComp(src) {
	if (src) {
		return createVNode(AImage, {
			src,
			key: src
		}) as any;
	} else {
		return '';
	}
}

export function renderCopyComp(text) {
	if (text) {
		return createVNode(CopyComp, {
			text,
			key: text
		}) as any;
	} else {
		return '';
	}
}
