export const required = {
	required: true,
	message: '请输入'
};

export const idCard = {
	pattern: /^[1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dX]$/,
	message: '请输入正确的身份证号',
	trigger: 'blur'
};

export const bankNo = {
	pattern: /^62\d{14,17}$/,
	message: '请输入正确的银行卡号',
	trigger: 'blur'
};

export const positiveInteger = {
	pattern: /^(\d)+$/,
	message: '请输入正整数',
	trigger: 'blur'
};

export const noBlank = {
	pattern: /^\S+$/,
	message: '不允许空格',
	trigger: 'blur'
};

export const phoneNumber = {
	pattern: /^(\d){11}$/,
	message: '请输入正确的手机号',
	trigger: 'blur'
};