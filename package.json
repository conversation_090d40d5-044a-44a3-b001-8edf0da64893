{"name": "parrot", "version": "1.0.0", "private": true, "scripts": {"serve": "vue-cli-service serve --mode development --isLocal", "build:test": "vue-cli-service build --mode production --isTest", "build": "vue-cli-service build --mode production --isProd", "lint": "vue-cli-service lint", "format": "prettier --write \"src/**/*.ts\" \"src/**/*.vue\" \"src/**/*.less\"", "lint:style": "stylelint  --custom-syntax postcss-less  \"./**/*.{css,less,vue}\"", "fix:style": "stylelint  --custom-syntax postcss-less --fix  \"./**/*.{css,less,vue}\""}, "dependencies": {"@ant-design/icons-vue": "6.1.0", "@simplex/simple-base": "6.2.3", "@simplex/simple-core": "4.0.17", "@wangeditor/editor-for-vue": "^5.1.12", "admin-library": "^1.0.15", "core-js": "3.23.4", "echarts": "^5.4.3", "tcplayer.js": "^4.9.1", "tim-js-sdk": "^2.27.6", "vue": "3.2.45", "vue-echarts": "6.2.3", "vue-router": "^4.1.3", "vue-virtual-scroller": "^2.0.0-beta.8"}, "devDependencies": {"@babel/plugin-proposal-optional-chaining": "^7.18.9", "@paas/eslint-config-paas": "^2.0.7", "@paas/paas-webpack-plugin": "^1.0.0", "@paas/stylelint-config-paas": "^1.0.4", "@types/node": "18.0.0", "@typescript-eslint/eslint-plugin": "^5.30.6", "@typescript-eslint/parser": "^5.30.6", "@vue/cli-plugin-babel": "^5.0.8", "@vue/cli-plugin-eslint": "^5.0.8", "@vue/cli-plugin-router": "^5.0.8", "@vue/cli-plugin-typescript": "^5.0.8", "@vue/cli-service": "^5.0.8", "@vue/eslint-config-typescript": "^9.1.0", "eslint": "^7.32.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-vue": "^8.0.3", "husky": "^4.2.5", "ip": "^1.1.8", "less": "^4.1.3", "less-loader": "^11.0.0", "prettier": "^2.4.1", "typescript": "~4.7.4"}, "eslintConfig": {"root": true, "env": {"browser": true, "node": true, "es6": true}, "globals": {"APP": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended", "@vue/typescript/recommended", "@paas/eslint-config-paas"], "parserOptions": {"sourceType": "module"}, "plugins": ["prettier"], "rules": {}}, "husky": {"hooks": {"pre-commit": "npm run lint && npm run lint:style"}}, "browserslist": ["> 1%", "Chrome > 68", "not dead", "not ie 11"]}